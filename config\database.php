<?php
// Set default timezone to Asia/Kolkata (Indian Standard Time)
date_default_timezone_set('Asia/Kolkata');

// Database configuration - store in constants for security
define('DB_SERVER', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '');
define('DB_NAME', 'lead_management');

// Error reporting - disable in production
if ($_SERVER['SERVER_NAME'] !== 'localhost' && 
    $_SERVER['SERVER_NAME'] !== '127.0.0.1' && 
    !strpos($_SERVER['SERVER_NAME'], '.local')) {
    // Production environment - disable error reporting for security
    error_reporting(0);
    ini_set('display_errors', 0);
} else {
    // Development environment - show errors
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
}

/**
 * Get database connection
 *
 * @return mysqli Database connection object
 */
function get_database_connection() {
    // Use static variable to avoid creating multiple connections
    static $conn = null;

    if ($conn === null) {
        try {
            // Create a new connection
            $conn = new mysqli(DB_SERVER, DB_USERNAME, DB_PASSWORD);

            // Check connection
            if ($conn->connect_error) {
                throw new Exception("Connection failed: " . $conn->connect_error);
            }

            // Set charset to ensure proper encoding
            $conn->set_charset('utf8mb4');

            // Create database if it doesn't exist
            $sql = "CREATE DATABASE IF NOT EXISTS " . DB_NAME;
            if ($conn->query($sql)) {
                // Select the database
                $conn->select_db(DB_NAME);
            } else {
                throw new Exception("Could not create database: " . $conn->error);
            }

        } catch (Exception $e) {
            // Log error instead of displaying it
            error_log("Database connection error: " . $e->getMessage());

            // In production, show generic error
            if (error_reporting() === 0) {
                die("A database error occurred. Please try again later.");
            } else {
                // In development, show detailed error
                die("DATABASE ERROR: " . $e->getMessage() . "<br><br>Please check:<br>1. XAMPP MySQL is running<br>2. Database credentials are correct<br>3. Run <a href='test_db_connection.php'>database connection test</a>");
            }
        }
    }

    return $conn;
}

// Create and return the connection
$conn = get_database_connection();

// For backward compatibility, also make it available as a global
$GLOBALS['db_connection'] = $conn;

return $conn;
?>
