<?php
// Include database configuration
$conn = require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if the user is logged in
ensure_session_started();
require_login();

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    // Redirect back to products page with error
    header("Location: products.php?error=1&message=" . urlencode("No product ID provided"));
    exit;
}

$product_id = intval($_GET['id']);

// Check if the product exists
$check_sql = "SELECT id FROM products WHERE id = ?";
$check_stmt = mysqli_prepare($conn, $check_sql);
mysqli_stmt_bind_param($check_stmt, "i", $product_id);
mysqli_stmt_execute($check_stmt);
$result = mysqli_stmt_get_result($check_stmt);

if (mysqli_num_rows($result) == 0) {
    // Product doesn't exist
    header("Location: products.php?error=1&message=" . urlencode("Product not found"));
    exit;
}

// Check if the product has any sales
$sales_check_sql = "SELECT COUNT(*) as count FROM sales WHERE product_id = ?";
$sales_check_stmt = mysqli_prepare($conn, $sales_check_sql);
mysqli_stmt_bind_param($sales_check_stmt, "i", $product_id);
mysqli_stmt_execute($sales_check_stmt);
$sales_result = mysqli_stmt_get_result($sales_check_stmt);
$sales_count = mysqli_fetch_assoc($sales_result)['count'];

if ($sales_count > 0) {
    // Product has sales, don't delete
    header("Location: products.php?error=1&message=" . urlencode("Cannot delete product with existing sales. This would affect sales records."));
    exit;
}

// Delete the product
$delete_sql = "DELETE FROM products WHERE id = ?";
$delete_stmt = mysqli_prepare($conn, $delete_sql);
mysqli_stmt_bind_param($delete_stmt, "i", $product_id);

if (mysqli_stmt_execute($delete_stmt)) {
    // Redirect to products page with success message
    header("Location: products.php?success=1&message=" . urlencode("Product deleted successfully"));
    exit;
} else {
    // Error deleting product
    header("Location: products.php?error=1&message=" . urlencode("Error deleting product: " . mysqli_error($conn)));
    exit;
}
?>
