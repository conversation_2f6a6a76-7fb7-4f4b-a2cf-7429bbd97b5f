<?php

function check_database_setup() {
    try {
        // Include database configuration
        $conn = require_once __DIR__ . '/config/database.php';
        
        // Check if essential tables exist
        $essential_tables = ['employees', 'contacts', 'products', 'sales'];
        
        foreach ($essential_tables as $table) {
            $check_sql = "SHOW TABLES LIKE '$table'";
            $result = mysqli_query($conn, $check_sql);
            
            if (mysqli_num_rows($result) == 0) {
                // Table doesn't exist - database not set up
                return false;
            }
        }
        
        // Check if employees table has data (at least one user should exist)
        $user_check_sql = "SELECT COUNT(*) as count FROM employees";
        $user_result = mysqli_query($conn, $user_check_sql);
        $user_row = mysqli_fetch_assoc($user_result);
        
        if ($user_row['count'] == 0) {
            // No users exist - database not properly initialized
            return false;
        }
        
        return true;
        
    } catch (Exception $e) {
        // Any error means database is not set up properly
        return false;
    }
}

function redirect_to_setup_if_needed() {
    if (!check_database_setup()) {
        // Get the current directory relative to the document root
        $current_dir = dirname($_SERVER['SCRIPT_NAME']);
        
        // Calculate the path to setup.html
        if ($current_dir === '/' || $current_dir === '') {
            $setup_path = 'setup.html';
        } else {
            // Count directory levels to go back to root
            $levels = substr_count(trim($current_dir, '/'), '/');
            $back_path = str_repeat('../', $levels);
            $setup_path = $back_path . 'setup.html';
        }
        
        header("Location: $setup_path");
        exit;
    }
}

// If this file is called directly, just check and return JSON
if (basename($_SERVER['SCRIPT_NAME']) === 'check_database_setup.php') {
    header('Content-Type: application/json');
    echo json_encode(['database_ready' => check_database_setup()]);
    exit;
}
?>
