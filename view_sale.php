<?php
// Get database connection using reliable method
$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';

// Check if database is set up first
require_once 'check_database_setup.php';
redirect_to_setup_if_needed();

// Check if the user is logged in
ensure_session_started();
require_login();

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    // Redirect back to sales page with error
    header("Location: sales.php?error=1&message=" . urlencode("No sale ID provided"));
    exit;
}

$sale_id = intval($_GET['id']);

// Get the sale data with related information (unified contacts table)
$sql = "SELECT s.*, CONCAT(c.first_name, ' ', c.last_name) as customer_name, c.email as customer_email, c.phone as customer_phone,
        p.name as product_name, p.price as product_price,
        CONCAT(u.first_name, ' ', u.last_name) as sales_rep_name
        FROM sales s
        LEFT JOIN contacts c ON s.contact_id = c.id
        LEFT JOIN products p ON s.product_id = p.id
        LEFT JOIN employees u ON s.sales_rep_id = u.id
        WHERE s.id = ?";
$stmt = mysqli_prepare($conn, $sql);
mysqli_stmt_bind_param($stmt, "i", $sale_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) == 0) {
    // Sale doesn't exist
    header("Location: sales.php?error=1&message=" . urlencode("Sale not found"));
    exit;
}

$sale = mysqli_fetch_assoc($result);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="img/minilogo.jpg" type="image/x-icon">
    <title>View Sale - CRM System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .text-color{ color: #FF6500; }
        .button-hover {
            transition: background-color 0.3s ease, transform 0.2s ease;
        }

        .button-hover:hover {
            background-color: #e55b00;
            transform: scale(1.05);
        }
    </style>
</head>
<body class="">
<?php include 'includes/navigation.php'; ?>

    <!-- Main Content -->
    <div class="ml-0 min-[870px]:ml-60 min-h-screen flex flex-col">
        <!-- Navbar -->
        <header class="bg-[#1E3E62] p-4 flex justify-between z-10 flex-col">
            <h1 class="text-2xl font-bold text-white mx-10 my-2">View Sale</h1>
                    </header>
        <main class="flex-1 overflow-y-auto space-y-10 p-10">
            <!-- Sale Details -->
            <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                <div class="px-6 py-5 border-b border-gray-200 sm:px-8" style="background: linear-gradient(to right, #1E3E62, #0B192C);">
                    <div class="flex justify-between items-center">
                        <div>
                            <h3 class="text-lg leading-6 font-medium text-white">
                                Sale Information
                            </h3>
                            <p class="mt-1 max-w-2xl text-sm text-white">
                                Details for Sale #<?php echo $sale_id; ?>
                            </p>
                        </div>
                        <div class="flex space-x-3">
                            <a href="edit_sale.php?id=<?php echo $sale_id; ?>" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700" title="Edit Sale">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="delete_sale.php?id=<?php echo $sale_id; ?>" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 ml-2" onclick="return confirm('Are you sure you want to delete this sale?');" title="Delete Sale">
                                <i class="fas fa-trash-alt"></i>
                            </a>
                            <a href="invoice.php?id=<?php echo $sale_id; ?>" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 ml-2" title="Generate Invoice">
                                <i class="fas fa-file-invoice"></i>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="px-4 py-5 sm:p-6">
                    <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                        <div class="sm:col-span-1">
                            <dt class="text-sm font-medium text-gray-500">Customer</dt>
                            <dd class="mt-1 text-sm text-gray-900"><?php echo htmlspecialchars($sale['customer_name']); ?></dd>
                        </div>
                        <div class="sm:col-span-1">
                            <dt class="text-sm font-medium text-gray-500">Product</dt>
                            <dd class="mt-1 text-sm text-gray-900"><?php echo htmlspecialchars($sale['product_name']); ?></dd>
                        </div>
                        <div class="sm:col-span-1">
                            <dt class="text-sm font-medium text-gray-500">Amount</dt>
                            <dd class="mt-1 text-sm text-gray-900"><?php echo format_currency($sale['total_amount']); ?></dd>
                        </div>
                        <div class="sm:col-span-1">
                            <dt class="text-sm font-medium text-gray-500">Sale Date</dt>
                            <dd class="mt-1 text-sm text-gray-900"><?php echo date('F j, Y', strtotime($sale['sale_date'])); ?></dd>
                        </div>
                        <div class="sm:col-span-1">
                            <dt class="text-sm font-medium text-gray-500">Payment Method</dt>
                            <dd class="mt-1 text-sm text-gray-900">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    <?php echo htmlspecialchars($sale['payment_method']); ?>
                                </span>
                            </dd>
                        </div>
                        <div class="sm:col-span-1">
                            <dt class="text-sm font-medium text-gray-500">Sales Representative</dt>
                            <dd class="mt-1 text-sm text-gray-900">
                                <?php echo htmlspecialchars($sale['sales_rep_name']); ?>
                            </dd>
                        </div>
                       
                        
                    </dl>
                </div>
                <div class="px-4 py-3 bg-gray-50 text-right sm:px-6">
                    <a href="sales.php" class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Back to Sales
                    </a>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
