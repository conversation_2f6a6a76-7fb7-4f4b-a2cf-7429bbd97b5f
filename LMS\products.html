<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>LMS Product</title>
    <link rel="icon" type="image/png" href="/images/logo.title.png">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Open+Sans:wght@400;500;600&display=swap" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" />
    <link href="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
        }
        
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            font-family: 'Inter', sans-serif;
        }
        
        .feature-card {
            transition: all 0.3s cubic-bezier(.4, 2, .6, 1);
            cursor: pointer;
        }
        
        .feature-card:hover {
            transform: translateY(-10px) scale(1.04) rotate(-1deg);
            box-shadow: 0 16px 40px rgba(74, 119, 168, 0.18), 0 2px 8px rgba(0, 0, 0, 0.04);
            background-color: #f7fafc;
        }
        
        .feature-card:hover .ri-user-follow-line,
        .feature-card:hover .ri-mail-send-line,
        .feature-card:hover .ri-customer-service-2-line,
        .feature-card:hover .ri-bar-chart-grouped-line,
        .feature-card:hover .ri-calendar-todo-line,
        .feature-card:hover .ri-global-line {
            color: #F7A041 !important;
            transition: color 0.3s;
        }
        
        .pricing-card {
            transition: transform 0.35s cubic-bezier(.4, 2, .6, 1), box-shadow 0.35s cubic-bezier(.4, 2, .6, 1), border-color 0.35s cubic-bezier(.4, 2, .6, 1);
            cursor: pointer;
            border: 2px solid transparent;
            will-change: transform, box-shadow;
        }
        
        .pricing-card:hover {
            transform: translateY(-12px) scale(1.04) rotate(-1deg);
            box-shadow: 0 24px 48px rgba(247, 160, 65, 0.18), 0 2px 8px rgba(0, 0, 0, 0.08);
            border-color: #F7A041;
            z-index: 2;
        }
        
        .pricing-popular:hover .bg-\[\#F7A041\] {
            background-color: #1E3E62 !important;
            transition: background 0.35s;
        }
        
        .pricing-card:hover a {
            filter: brightness(1.1);
            box-shadow: 0 4px 16px rgba(30, 62, 98, 0.08);
        }
        /* Removed empty ruleset for .pricing-popular */
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .animate-fade-in {
            animation: fadeIn 0.6s ease-out forwards;
        }
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .animate-fadeInUp { animation: fadeInUp 0.6s ease-out forwards; }
           /* Hamburger to X animation for mobile menu button */
           #mobileMenuBtn.open span:nth-child(1) {
          transform: rotate(45deg) translateY(8px);
        }
        #mobileMenuBtn.open span:nth-child(2) {
          opacity: 0;
        }
        #mobileMenuBtn.open span:nth-child(3) {
          transform: rotate(-45deg) translateY(-8px);
        }
    </style>
</head>

<body class="bg-white text-black font-sans">

  <header class="fixed w-full top-0 z-50">
    <div class="lg:max-w-full mx-auto px-4 sm:px-4 md:px-6 lg:px-8 py-2 sm:py-3 md:py-4">
      <div
        class=" bg-white backdrop-blur-lg rounded-lg sm:rounded-xl lg:rounded-2xl shadow-lg border border-white/20 mx-1 sm:mx-2 md:mx-3 lg:mx-4">
        <div class=" flex justify-between items-center h-12 sm:h-14 md:h-16 px-2 sm:px-4 md:px-6">

          <div>
            <h1
              class="text-lg sm:text-xl md:text-2xl bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent relative">
              <img src="images/logo.png" alt="LMS Logo" class="lg:w-32  w-20 object-contain">
            </h1>
          </div>

          <!-- Desktop Nav -->
          <nav class="hidden lg:flex justify-between items-center">
            <!-- logo -->

            
            <div class="flex  ">
              <a href="index.html"
                class="flex nav-link px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-all duration-300">
                Home
              </a>
              <a href="About.html"
                class="flex px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-all duration-300">
                About
              </a>
              <a href="products.html"
                class=" flex px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-all duration-300">
                Products
              </a>

              <!-- Features Dropdown -->
              <div class="relative">
                <button id="featuresDropdownBtn"
                  class="px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-all duration-300 flex items-center">
                  Features
                  <i class="ri-arrow-down-s-line ml-1 transition-transform duration-200"></i>
                </button>
                <div id="featuresDropdownMenu" class="absolute top-full left-0 mt-2 w-64 bg-white rounded-xl shadow-2xl border border-gray-100 py-2 z-50 hidden">
                  <a href="leadmanagement.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-brain-line text-blue-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Lead Management</div>
                      <div class="text-xs text-gray-500">Smart lead prioritization</div>
                    </div>
                  </a>
                  <a href="salemanagement.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-settings-3-line text-green-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Sales Management</div>
                      <div class="text-xs text-gray-500">Streamline your process</div>
                    </div>
                  </a>
                  <a href="analytics.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-pie-chart-line text-purple-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Advanced Analytics</div>
                      <div class="text-xs text-gray-500">Detailed insights & reports</div>
                    </div>
                  </a>
                  <a href="taskmanager.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-smartphone-line text-orange-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Task Manager</div>
                      <div class="text-xs text-gray-500">Capture from anywhere</div>
                    </div>
                  </a>
                  <a href="customersupport.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-team-line text-indigo-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Customer Support</div>
                      <div class="text-xs text-gray-500">Work together effectively</div>
                    </div>
                  </a>
                  <a href="integration.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-teal-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-shield-check-line text-teal-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Integration</div>
                      <div class="text-xs text-gray-500">Connect seamlessly with tools</div>
                    </div>
                  </a>
                </div>
              </div>

            </div>
            <!-- login -->
            <div class="flex ">
              <a href="contact.html"
                class=" flex items-center font-bold px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black  transition-all duration-300">
                Contact
              </a>

              <a href="/LEADManagement/auth/login.php"
                class=" flex items-center bg-orange-500 rounded-3xl font-bold px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-white  transition-all duration-300">
                Login
              </a>
            </div>

          </nav>

          <!-- Mobile Menu Button -->
          <div class="lg:hidden z-50">
            <button id="mobileMenuBtn" class="focus:outline-none relative w-6 sm:w-7 md:w-8 h-6 sm:h-7 md:h-8">
              <span class="absolute top-1 left-0 w-6 sm:w-7 md:w-8 h-0.5 bg-black transition-transform duration-300"></span>
              <span class="absolute top-3 left-0 w-6 sm:w-7 md:w-8 h-0.5 bg-black transition-opacity duration-300"></span>
              <span class="absolute top-5 left-0 w-6 sm:w-7 md:w-8 h-0.5 bg-black transition-transform duration-300"></span>
            </button>
          </div>
        </div>

        <!-- Mobile Nav Menu -->
        <div id="mobileMenu" class="lg:hidden px-3 sm:px-4 md:px-6 pb-4 sm:pb-5 md:pb-6 pt-2 hidden">
          <div class="flex flex-col space-y-2 sm:space-y-3 md:space-y-4">
            <a href="index.html"
              class="text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200">
              Home
            </a>
            <a href="About.html"
              class="text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200">
              About
            </a>
            <a href="products.html"
              class="text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200">
              Products
            </a>
            <a href="contact.html"
              class="text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200">
              Contact
            </a>

            <!-- Mobile Features Section -->
            <div>
              <button id="mobileFeaturesBtn"
                class="w-full text-left text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200 flex items-center justify-between">
                <span>Features</span>
                <i class="ri-arrow-down-s-line transition-transform duration-200"></i>
              </button>

              <div id="mobileFeaturesMenu" class="ml-4 mt-2 space-y-2 hidden">
                <a href="leadmanagement.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-brain-line mr-2"></i>Lead Management
                </a>
                <a href="salemanagement.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-settings-3-line mr-2"></i>Email
                  Marketing
                </a>
                <a href="analytics.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-pie-chart-line mr-2"></i>Advanced Analytics
                </a>
                <a href="taskmanager.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-smartphone-line mr-2"></i>Task Manager
                </a>
                <a href="customersupport.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-team-line mr-2"></i>Customer Support
                </a>
                <a href="integration.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-shield-check-line mr-2"></i>Integration
                </a>
              </div>
            </div>

            <a href="/LEADManagement/auth/login.php"
              class="bg-[#FF6500] text-white px-4 sm:px-5 md:px-6 py-1.5 sm:py-2 md:py-2.5 text-sm sm:text-base md:text-lg rounded-full font-bold text-center mt-2 hover:bg-[#e55a00] transition-colors duration-200">
              Login
            </a>
          </div>
        </div>
      </div>
    </div>
  </header>

    <!-- Hero Section -->
    <section class="pt-32 pb-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-orange-50 via-blue-50 to-white relative overflow-hidden" data-aos="fade-up">
        <div class="text-center w-auto mx-auto space-y-10">
            <div class="animate-fadeInUp">
                <h1 class="text-5xl sm:text-6xl md:text-7xl font-extrabold font-serif bg-gradient-to-r from-orange-500 to-blue-600 bg-clip-text text-transparent mb-4">
                    Our <span class="text-[#FF6500]">LMS</span> Solutions
                </h1>
                <p class="text-lg sm:text-2xl md:text-3xl text-gray-700 font-medium mt-6 px-2 max-w-3xl mx-auto">
                    Powerful customer relationship management tools designed to help your business grow, streamline operations, and build lasting customer relationships.
                </p>
            </div>
            <div class="absolute inset-0 pointer-events-none">
                <div class="absolute -top-20 left-1/4 w-32 h-32 bg-blue-700 rounded-full opacity-10 animate-float"></div>
                <div class="absolute -top-40 right-1/4 w-24 h-24 bg-orange-500 rounded-full opacity-50 animate-float" style="animation-delay: -2s;"></div>
                <div class="absolute top-10 left-[16%] w-16 h-16 bg-blue-900 rounded-full opacity-25 animate-float" style="animation-delay: -4s;"></div>
            </div>
        </div>
    </section>

    <!-- Product Overview -->
    <section class="py-16 px-4 sm:px-6 lg:px-8 bg-white" data-aos="fade-up">
        <div class="w-auto mx-auto grid grid-cols-1 md:grid-cols-2 gap-12 items-center ml-6 mr-6">
            <div class="animate-fadeInUp" style="animation-delay: 0.2s;">
                <h2 class="text-3xl sm:text-4xl font-bold text-gray-900 text-transparent bg-gradient-to-r from-orange-500 to-blue-500 bg-clip-text mb-4">Complete LMS Platform</h2>
                <p class="text-lg text-gray-700 font-medium mb-8">
                    Our comprehensive CRM solution provides everything you need to manage customer relationships effectively, from lead generation to customer service and beyond.
                </p>
                <ul class="space-y-4">
                    <li class="flex items-start"><i class="ri-check-line text-green-500 text-xl mr-3 mt-1"></i><span class="text-gray-700">Centralized customer data management</span></li>
                    <li class="flex items-start"><i class="ri-check-line text-green-500 text-xl mr-3 mt-1"></i><span class="text-gray-700">Automated sales and marketing workflows</span></li>
                    <li class="flex items-start"><i class="ri-check-line text-green-500 text-xl mr-3 mt-1"></i><span class="text-gray-700">Advanced analytics and reporting</span></li>
                    <li class="flex items-start"><i class="ri-check-line text-green-500 text-xl mr-3 mt-1"></i><span class="text-gray-700">Seamless integration with existing tools</span></li>
                </ul>
                <div class="mt-8">
                    <a href="#pricing" class="bg-[#1E3E62] text-white px-6 py-3 rounded-full font-semibold hover:bg-[#16324f] transition-colors inline-flex items-center">View Pricing<i class="ri-arrow-right-line ml-2"></i></a>
                </div>
            </div>
            <div class="animate-fadeInUp" style="animation-delay: 0.4s;">
                <img src="https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80" alt="CRM Dashboard" class="rounded-2xl shadow-lg w-full h-auto object-cover">
            </div>
        </div>
    </section>

    <!-- Key Features -->
    <section class="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-white via-blue-50 to-orange-50" data-aos="fade-up">
        <div class="w-auto mx-auto ml-6 mr-6">
            <div class="text-center mb-16">
                <h2 class="text-3xl sm:text-4xl font-bold text-gray-900 text-transparent bg-gradient-to-r from-orange-500 to-blue-500 bg-clip-text mb-4">Key Features</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Our LMS platform is packed with powerful features to help you manage and grow your business.
                </p>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Feature Cards: Add data-aos and rounded-2xl, shadow-xl, hover:-translate-y-2, hover:shadow-2xl -->
                <div class="relative cursor-pointer overflow-hidden bg-white p-8 shadow-xl ring-1 ring-gray-900/5 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl rounded-2xl" data-aos="fade-up">
                    <span class="grid h-14 w-14 place-items-center rounded-full bg-blue-500 mb-6"><i class="ri-user-follow-line text-2xl text-white"></i></span>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Lead Management</h3>
                    <p class="text-gray-600">Capture, track, and nurture leads through your sales pipeline with automated workflows and personalized follow-ups.</p>
                </div>
                <div class="relative cursor-pointer overflow-hidden bg-white p-8 shadow-xl ring-1 ring-gray-900/5 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl rounded-2xl" data-aos="fade-up">
                    <span class="grid h-14 w-14 place-items-center rounded-full bg-orange-500 mb-6"><i class="ri-mail-send-line text-2xl text-white"></i></span>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Sales Management</h3>
                    <p class="text-gray-600">Create and send targeted email campaigns, track performance, and automate follow-ups based on customer behavior.</p>
                </div>
                <div class="relative cursor-pointer overflow-hidden bg-white p-8 shadow-xl ring-1 ring-gray-900/5 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl rounded-2xl" data-aos="fade-up">
                    <span class="grid h-14 w-14 place-items-center rounded-full bg-green-500 mb-6"><i class="ri-customer-service-2-line text-2xl text-white"></i></span>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Customer Support</h3>
                    <p class="text-gray-600">Provide exceptional customer service with ticket management, knowledge base, and multi-channel support options.</p>
                </div>
                <div class="relative cursor-pointer overflow-hidden bg-white p-8 shadow-xl ring-1 ring-gray-900/5 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl rounded-2xl" data-aos="fade-up">
                    <span class="grid h-14 w-14 place-items-center rounded-full bg-purple-500 mb-6"><i class="ri-bar-chart-grouped-line text-2xl text-white"></i></span>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Analytics & Reporting</h3>
                    <p class="text-gray-600">Gain valuable insights with customizable dashboards, real-time reporting, and advanced analytics capabilities.</p>
                </div>
                <div class="relative cursor-pointer overflow-hidden bg-white p-8 shadow-xl ring-1 ring-gray-900/5 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl rounded-2xl" data-aos="fade-up">
                    <span class="grid h-14 w-14 place-items-center rounded-full bg-red-500 mb-6"><i class="ri-calendar-todo-line text-2xl text-white"></i></span>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Task Management</h3>
                    <p class="text-gray-600">Stay organized with task assignments, reminders, and calendar integration to ensure nothing falls through the cracks.</p>
                </div>
                <div class="relative cursor-pointer overflow-hidden bg-white p-8 shadow-xl ring-1 ring-gray-900/5 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl rounded-2xl" data-aos="fade-up">
                    <span class="grid h-14 w-14 place-items-center rounded-full bg-teal-500 mb-6"><i class="ri-global-line text-2xl text-white"></i></span>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Integrations</h3>
                    <p class="text-gray-600">Connect seamlessly with your favorite tools including email, calendar, e-commerce platforms, and more.</p>
                </div>
            </div>
        </div>
    </section>

      <!-- 1. Product Use Cases -->
      <section class="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-blue-50 via-orange-50 to-white" data-aos="fade-up">
        <div class="w-auto mx-auto ml-6 mr-6">
          <h2 class="text-3xl sm:text-4xl font-bold text-gray-900 text-transparent bg-gradient-to-r from-blue-500 to-orange-500 bg-clip-text mb-10 text-center">Product Use Cases</h2>
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            <div class="bg-white rounded-2xl shadow-lg p-8 flex flex-col items-center text-center hover:-translate-y-2 hover:shadow-2xl transition-all" data-aos="fade-up">
              <span class="text-4xl mb-4 text-blue-500"><i class="ri-briefcase-4-line"></i></span>
              <h3 class="text-xl font-bold mb-2">Sales Teams</h3>
              <p class="text-gray-600">Track leads, automate follow-ups, and close deals faster with our LMS.</p>
            </div>
            <div class="bg-white rounded-2xl shadow-lg p-8 flex flex-col items-center text-center hover:-translate-y-2 hover:shadow-2xl transition-all" data-aos="fade-up">
              <span class="text-4xl mb-4 text-orange-500"><i class="ri-user-settings-line"></i></span>
              <h3 class="text-xl font-bold mb-2">HR & Recruitment</h3>
              <p class="text-gray-600">Manage candidate pipelines, schedule interviews, and onboard new hires efficiently.</p>
            </div>
            <div class="bg-white rounded-2xl shadow-lg p-8 flex flex-col items-center text-center hover:-translate-y-2 hover:shadow-2xl transition-all" data-aos="fade-up">
              <span class="text-4xl mb-4 text-green-500"><i class="ri-customer-service-2-line"></i></span>
              <h3 class="text-xl font-bold mb-2">Customer Support</h3>
              <p class="text-gray-600">Centralize support tickets, track resolutions, and improve customer satisfaction.</p>
            </div>
            <div class="bg-white rounded-2xl shadow-lg p-8 flex flex-col items-center text-center hover:-translate-y-2 hover:shadow-2xl transition-all" data-aos="fade-up">
              <span class="text-4xl mb-4 text-purple-500"><i class="ri-store-2-line"></i></span>
              <h3 class="text-xl font-bold mb-2">E-commerce</h3>
              <p class="text-gray-600">Integrate with your store, nurture leads, and boost repeat purchases.</p>
            </div>
          </div>
        </div>
      </section>

        <!-- 4. Upcoming Features / Roadmap -->
    <section class="py-16 px-4 sm:px-6 lg:px-8 bg-white" data-aos="fade-up">
      <div class="w-auto mx-auto ml-6 mr-6">
        <h2 class="text-3xl sm:text-4xl font-bold text-gray-900 text-transparent bg-gradient-to-r from-orange-500 to-blue-500 bg-clip-text mb-10 text-center">Upcoming Features & Roadmap</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div class="bg-gradient-to-br from-blue-100 to-white rounded-2xl shadow-lg p-6 flex flex-col items-center text-center">
            <span class="text-3xl mb-3 text-blue-600"><i class="ri-robot-2-line"></i></span>
            <h3 class="font-bold mb-2">AI Lead Scoring</h3>
            <p class="text-gray-600 text-sm">Automatically prioritize leads based on engagement and fit.</p>
            <span class="mt-2 text-xs text-blue-500 font-semibold">Coming Q3 2025</span>
          </div>
          <div class="bg-gradient-to-br from-orange-100 to-white rounded-2xl shadow-lg p-6 flex flex-col items-center text-center">
            <span class="text-3xl mb-3 text-orange-500"><i class="ri-bar-chart-2-line"></i></span>
            <h3 class="font-bold mb-2">Custom Dashboards</h3>
            <p class="text-gray-600 text-sm">Build your own analytics dashboards with drag-and-drop widgets.</p>
            <span class="mt-2 text-xs text-orange-500 font-semibold">Coming Q4 2025</span>
          </div>
          <div class="bg-gradient-to-br from-green-100 to-white rounded-2xl shadow-lg p-6 flex flex-col items-center text-center">
            <span class="text-3xl mb-3 text-green-500"><i class="ri-global-line"></i></span>
            <h3 class="font-bold mb-2">Multi-language Support</h3>
            <p class="text-gray-600 text-sm">Use the LMS in your preferred language for global teams.</p>
            <span class="mt-2 text-xs text-green-500 font-semibold">Coming 2026</span>
          </div>
        </div>
      </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="py-16 px-4 sm:px-6 lg:px-8 bg-white" data-aos="fade-up">
        <div class="w-auto mx-auto ml-6 mr-6">
            <div class="text-center mb-16">
                <h2 class="text-3xl sm:text-4xl font-bold text-gray-900 text-transparent bg-gradient-to-r from-orange-500 to-blue-500 bg-clip-text mb-4">Simple, Transparent Pricing</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Choose the plan that works best for your business needs.
                </p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Pricing Cards: Add data-aos, rounded-2xl, shadow-xl, hover:-translate-y-2, hover:shadow-2xl -->
                <div class="pricing-card bg-white rounded-2xl shadow-xl overflow-hidden animate-fadeInUp flex flex-col hover:-translate-y-2 hover:shadow-2xl transition-all" data-aos="fade-up">
                    <div class="bg-[#F7A041] text-white text-center py-2">
                        <span class="font-semibold">Recommended</span>
                    </div>
                    <div class="p-8">
                        <h3 class="text-xl font-bold text-gray-900 mb-2">Starter</h3>
                        <p class="text-gray-600 mb-6">Perfect for small businesses</p>
                        <div class="flex items-end mb-6">
                            <span class="text-4xl font-bold text-gray-900">Rs299</span>
                            <span class="text-gray-600 ml-2">/month</span>
                        </div>
                        <ul class="space-y-3 mb-8">
                            <li class="flex items-start"><i class="ri-check-line text-green-500 text-xl mr-3 mt-0.5"></i><span class="text-gray-700">Up to 1,000 contacts</span></li>
                            <li class="flex items-start"><i class="ri-check-line text-green-500 text-xl mr-3 mt-0.5"></i><span class="text-gray-700">Basic lead management</span></li>
                            <li class="flex items-start"><i class="ri-check-line text-green-500 text-xl mr-3 mt-0.5"></i><span class="text-gray-700">Email marketing (500/month)</span></li>
                            <li class="flex items-start"><i class="ri-check-line text-green-500 text-xl mr-3 mt-0.5"></i><span class="text-gray-700">Standard reporting</span></li>
                            <li class="flex items-start"><i class="ri-close-line text-red-500 text-xl mr-3 mt-0.5"></i><span class="text-gray-400">Advanced automation</span></li>
                            <li class="flex items-start"><i class="ri-close-line text-red-500 text-xl mr-3 mt-0.5"></i><span class="text-gray-400">API access</span></li>
                        </ul>
                    </div>
                    <div class="px-8 pb-8 mt-auto">
                        <a href="#" class="block w-full bg-[#1E3E62] hover:bg-[#16324f] text-white font-semibold py-3 px-4 rounded-lg text-center transition-colors">Get Started</a>
                    </div>
                </div>
                <div class="pricing-card pricing-popular bg-white rounded-2xl shadow-2xl overflow-hidden animate-fadeInUp flex flex-col hover:-translate-y-2 hover:shadow-2xl transition-all" data-aos="fade-up">
                    <div class="bg-[#F7A041] text-white text-center py-2">
                        <span class="font-semibold">Most Popular</span>
                    </div>
                    <div class="p-8">
                        <h3 class="text-xl font-bold text-gray-900 mb-2">Professional</h3>
                        <p class="text-gray-600 mb-6">Ideal for growing businesses</p>
                        <div class="flex items-end mb-6">
                            <span class="text-4xl font-bold text-gray-900">Rs799</span>
                            <span class="text-gray-600 ml-2">/month</span>
                        </div>
                        <ul class="space-y-3 mb-8">
                            <li class="flex items-start"><i class="ri-check-line text-green-500 text-xl mr-3 mt-0.5"></i><span class="text-gray-700">Up to 10,000 contacts</span></li>
                            <li class="flex items-start"><i class="ri-check-line text-green-500 text-xl mr-3 mt-0.5"></i><span class="text-gray-700">Advanced lead management</span></li>
                            <li class="flex items-start"><i class="ri-check-line text-green-500 text-xl mr-3 mt-0.5"></i><span class="text-gray-700">Email marketing (5,000/month)</span></li>
                            <li class="flex items-start"><i class="ri-check-line text-green-500 text-xl mr-3 mt-0.5"></i><span class="text-gray-700">Advanced reporting & analytics</span></li>
                            <li class="flex items-start"><i class="ri-check-line text-green-500 text-xl mr-3 mt-0.5"></i><span class="text-gray-700">Basic automation workflows</span></li>
                            <li class="flex items-start"><i class="ri-close-line text-red-500 text-xl mr-3 mt-0.5"></i><span class="text-gray-400">API access</span></li>
                        </ul>
                    </div>
                    <div class="px-8 pb-8 mt-auto">
                        <a href="#" class="block w-full bg-[#1E3E62] hover:bg-[#16324f] text-white font-semibold py-3 px-4 rounded-lg text-center transition-colors">Get Started</a>
                    </div>
                </div>
                <div class="pricing-card bg-white rounded-2xl shadow-xl overflow-hidden animate-fadeInUp flex flex-col hover:-translate-y-2 hover:shadow-2xl transition-all" data-aos="fade-up">
                    <div class="bg-[#F7A041] text-white text-center py-2">
                        <span class="font-semibold">Value for money</span>
                    </div>
                    <div class="p-8">
                        <h3 class="text-xl font-bold text-gray-900 mb-2">Enterprise</h3>
                        <p class="text-gray-600 mb-6">For large organizations</p>
                        <div class="flex items-end mb-6">
                            <span class="text-4xl font-bold text-gray-900">Rs1999</span>
                            <span class="text-gray-600 ml-2">/month</span>
                        </div>
                        <ul class="space-y-3 mb-8">
                            <li class="flex items-start"><i class="ri-check-line text-green-500 text-xl mr-3 mt-0.5"></i><span class="text-gray-700">Unlimited contacts</span></li>
                            <li class="flex items-start"><i class="ri-check-line text-green-500 text-xl mr-3 mt-0.5"></i><span class="text-gray-700">Complete lead management suite</span></li>
                            <li class="flex items-start"><i class="ri-check-line text-green-500 text-xl mr-3 mt-0.5"></i><span class="text-gray-700">Email marketing (unlimited)</span></li>
                            <li class="flex items-start"><i class="ri-check-line text-green-500 text-xl mr-3 mt-0.5"></i><span class="text-gray-700">Custom reporting & dashboards</span></li>
                            <li class="flex items-start"><i class="ri-check-line text-green-500 text-xl mr-3 mt-0.5"></i><span class="text-gray-700">Advanced automation workflows</span></li>
                            <li class="flex items-start"><i class="ri-check-line text-green-500 text-xl mr-3 mt-0.5"></i><span class="text-gray-700">Full API access</span></li>
                        </ul>
                    </div>
                    <div class="px-8 pb-8 mt-auto">
                        <a href="#" class="block w-full bg-[#1E3E62] hover:bg-[#16324f] text-white font-semibold py-3 px-4 rounded-lg text-center transition-colors">Get Started</a>
                    </div>
                </div>
            </div>
            <div class="text-center mt-12">
                <p class="text-gray-600 mb-4">Need a custom solution for your enterprise?</p>
                <a href="contact.html" class="text-[#1E3E62] font-semibold hover:underline inline-flex items-center">Contact our sales team<i class="ri-arrow-right-line ml-1"></i></a>
            </div>
        </div>
    </section>


    <!-- 6. Awards & Certifications -->
    <section class="pt-4 sm:pt-6 lg:pt-8 pb-16 sm:pb-20 lg:pb-24 bg-white" data-aos="fade-up">
      <div class="w-auto mx-auto ml-6 mr-6">
        <h2 class="text-4xl sm:text-4xl font-bold text-gray-900 text-transparent bg-gradient-to-r from-orange-500 to-blue-500 bg-clip-text mb-10 text-center">Awards & Certifications</h2>
        <div class="flex flex-wrap justify-center gap-8">
          <div class="flex flex-col items-center">
            <img src="images/award.png" alt="Award 1" class="w-24 h-24 object-contain mb-2">
            <span class="font-semibold text-gray-700">Best CRM 2024</span>
          </div>
          <div class="flex flex-col items-center">
            <img src="images/award2.png" alt="Award 2" class="w-24 h-24 object-contain mb-2">
            <span class="font-semibold text-gray-700">Top SaaS Solution</span>
          </div>
          <div class="flex flex-col items-center">
            <img src="images/award3.png" alt="Award 3" class="w-24 h-24 object-contain mb-2">
            <span class="font-semibold text-gray-700">ISO 27001 Certified</span>
          </div>
        </div>
      </div>
    </section>
    <!-- Related Content Sections End -->

    <!-- CTA Section -->
    <section class="pt-4 sm:pt-6 lg:pt-8 pb-16 sm:pb-20 lg:pb-24">
      <div class="w-full mx-auto text-center px-4 sm:px-6 md:px-8">
          <div class="glass-effect shadow-2xl rounded-3xl p-6 sm:p-10 md:p-12" data-aos="fade-up">
              <h2 class="text-2xl sm:text-3xl md:text-5xl bg-gradient-to-r from-blue-500 to-orange-500 bg-clip-text text-transparent font-bold mb-4 sm:mb-6">
                  Ready to transform your customer relationships?
              </h2>
              <p class="text-base sm:text-lg md:text-xl text-gray-600 mb-6 sm:mb-8">
                  Join thousands of businesses that trust LeadFlow Pro to manage their most valuable prospects.
              </p>
              <div class="flex flex-col sm:flex-row gap-4 justify-center">
                  <button class="bg-gradient-to-r from-gray-300 to-orange-500 hover:from-blue-500 hover:to-purple-700 hover:text-white px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-semibold text-base sm:text-lg transition-all transform hover:scale-105">
                      Start Free Trial
                  </button>
                  <a href="contact.html" class="border border-gray-800 bg-gradient-to-l from-gray-100 to-gray-300 hover:from-blue-500 hover:to-purple-700 hover:text-white px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-semibold text-base sm:text-lg transition-all hover:scale-105">
                      Contact Us
                  </a>
              </div>
          </div>
      </div>
  </section>


    <!-- Footer -->
<footer class="bg-black text-white px-6 sm:px-8 lg:px-12 w-full text-sm sm:text-base">
  <div class="w-full">
    <div class="py-16 grid grid-cols-1 md:grid-cols-4 gap-10 text-center md:text-left">
      <!-- Logo & Description -->
      <div class="flex flex-col items-center md:items-start space-y-6">
        <img src="images/logo.png" alt="Logo" class="w-40 sm:w-48 h-auto bg-white" />
        <p class="text-gray-400 text-sm sm:text-base max-w-sm text-center md:text-left">
          Effortless payroll, seamless success – simplify your pay process today.
        </p>
        <div class="flex gap-5 justify-center md:justify-start">
          <!-- Social Icons -->
          <a href="#" class="text-gray-400 hover:text-white transition">
            <!-- Facebook -->
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"/>
            </svg>
          </a>
          <!-- Twitter -->
          <a href="#" class="text-gray-400 hover:text-white transition">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"/>
            </svg>
          </a>
          <!-- Instagram -->
          <a href="#" class="text-gray-400 hover:text-white transition">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M7.75 2h8.5A5.75 5.75 0 0122 7.75v8.5A5.75 5.75 0 0116.25 22h-8.5A5.75 5.75 0 012 16.25v-8.5A5.75 5.75 0 017.75 2zm0 1.5A4.25 4.25 0 003.5 7.75v8.5A4.25 4.25 0 007.75 20.5h8.5a4.25 4.25 0 004.25-4.25v-8.5A4.25 4.25 0 0016.25 3.5h-8.5zm8.75 2a1 1 0 110 2 1 1 0 010-2zM12 7a5 5 0 110 10 5 5 0 010-10zm0 1.5a3.5 3.5 0 100 7 3.5 3.5 0 000-7z"/>
            </svg>
          </a>
          <!-- LinkedIn -->
          <a href="#" class="text-gray-400 hover:text-white transition">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M4.98 3.5C4.98 4.61 4.1 5.5 3 5.5S1.02 4.61 1.02 3.5 1.9 1.5 3 1.5 4.98 2.39 4.98 3.5zM.5 6h5V20h-5V6zm7.5 0h4.7v1.785h.066c.655-1.24 2.255-2.535 4.634-2.535 4.953 0 5.867 3.26 5.867 7.5V20h-5v-6.417c0-1.531-.027-3.5-2.134-3.5-2.138 0-2.466 1.668-2.466 3.39V20h-5V6z"/>
            </svg>
          </a>
        </div>
      </div>
      <!-- Quick Links -->
      <div class="space-y-4">
        <h3 class="text-base font-extrabold">Quick Links</h3>
        <ul class="space-y-2 text-gray-400 text-sm sm:text-base">
          <li><a href="index.html" class="hover:text-white">Home</a></li>
          <li><a href="about.html" class="hover:text-white">About Us</a></li>
          <li><a href="products.html" class="hover:text-white">Products</a></li>
          <li><a href="contact.html" class="hover:text-white">Contact Us</a></li>
          <li><a href="login.html" class="hover:text-white">Login</a></li>
        </ul>
      </div>
      <!-- Products -->
      <div class="space-y-4">
        <h3 class="text-base font-extrabold">Products</h3>
        <ul class="space-y-2 text-gray-400 text-sm sm:text-base">
          <li><a href="leadmanagement.html" class="hover:text-white">Lead Management</a></li>
          <li><a href="salemanagement.html" class="hover:text-white">Sales Management</a></li>
          <li><a href="analytics.html" class="hover:text-white">Advance Analytics</a></li>
          <li><a href="customersupport.html" class="hover:text-white">Customer Management</a></li>
          <li><a href="integration.html" class="hover:text-white">Integration</a></li>
        </ul>
      </div>
      <!-- Contact -->
      <div class="space-y-4">
        <h3 class="text-lg font-extrabold">Contact</h3>
        <ul class="space-y-3 text-gray-400 text-sm">
          <li class="flex items-center justify-center md:justify-start gap-2">
            <svg class="w-4 h-4" stroke="currentColor" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            <a href="mailto:<EMAIL>" class="hover:text-white"><EMAIL></a>
          </li>
          <li class="flex items-center justify-center md:justify-start gap-2">
            <svg class="w-4 h-4" stroke="currentColor" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
            </svg>
            +1 (555) 482-9316
          </li>
          <li class="flex items-start justify-center md:justify-start gap-2">
            <svg class="w-4 h-4" stroke="currentColor" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            <span>1234 Elm Street, Suite 567,<br />Springfield, IL 62701, USA</span>
          </li>
        </ul>
      </div>
    </div>
    <!-- Bottom -->
    <div class="border-t border-gray-800 py-4 flex flex-col sm:flex-row justify-between items-center text-gray-400 text-sm sm:text-base gap-4 text-center">
      <p>© 2025 LMS. All rights reserved.</p>
      <div class="flex gap-4">
        <a href="#" class="hover:text-white">Terms & Conditions</a>
        <a href="#" class="hover:text-white">Privacy Policy</a>
      </div>
    </div>
  </div>
</footer>




<script>
  // Simple dropdown toggle for Features
  document.addEventListener('DOMContentLoaded', function () {
    const btn = document.getElementById('featuresDropdownBtn');
    const menu = document.getElementById('featuresDropdownMenu');
    btn.addEventListener('click', function (e) {
      e.stopPropagation();
      menu.classList.toggle('hidden');
    });
    // Close dropdown when clicking outside
    document.addEventListener('click', function (e) {
      if (!btn.contains(e.target) && !menu.contains(e.target)) {
        menu.classList.add('hidden');
      }
    });
  });

  // Mobile menu toggle
  document.addEventListener('DOMContentLoaded', function () {
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    const mobileMenu = document.getElementById('mobileMenu');
    mobileMenuBtn.addEventListener('click', function (e) {
      e.stopPropagation();
      mobileMenu.classList.toggle('hidden');
      mobileMenuBtn.classList.toggle('open'); // Toggle open class for X animation
    });
    document.addEventListener('click', function (e) {
      if (!mobileMenu.contains(e.target) && !mobileMenuBtn.contains(e.target)) {
        mobileMenu.classList.add('hidden');
        mobileMenuBtn.classList.remove('open'); // Remove open class if clicking outside
      }
    });

    // Mobile features dropdown
    const mobileFeaturesBtn = document.getElementById('mobileFeaturesBtn');
    const mobileFeaturesMenu = document.getElementById('mobileFeaturesMenu');
    mobileFeaturesBtn.addEventListener('click', function (e) {
      e.stopPropagation();
      mobileFeaturesMenu.classList.toggle('hidden');
    });
    document.addEventListener('click', function (e) {
      if (!mobileFeaturesMenu.contains(e.target) && !mobileFeaturesBtn.contains(e.target)) {
        mobileFeaturesMenu.classList.add('hidden');
      }
    });
  });
</script>

<script src="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js"></script>
<script>
  AOS.init();
</script>
</body>

</html>