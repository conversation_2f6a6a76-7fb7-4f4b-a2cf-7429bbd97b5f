<?php
// Include database configuration
$conn = require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if the user is logged in and has admin role
ensure_session_started();
if (!isset($_SESSION["user_id"]) || !isset($_SESSION["role"]) || $_SESSION["role"] !== "admin") {
    header("location: auth/unauthorized.php");
    exit;
}

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: employees.php?error=1&message=" . urlencode("No employee ID provided"));
    exit;
}

$employee_id = intval($_GET['id']);

// Handle AJAX request to check username uniqueness
if (isset($_POST['check_username']) && $_POST['check_username'] === 'true' && isset($_POST['username']) && isset($_POST['employee_id'])) {
    $username = trim($_POST['username']);
    $check_employee_id = intval($_POST['employee_id']);
    $exists = false;
    
    // Check if username already exists (excluding the current employee)
    $sql = "SELECT id FROM employees WHERE username = ? AND id != ?";
    if ($stmt = mysqli_prepare($conn, $sql)) {
        mysqli_stmt_bind_param($stmt, "si", $username, $check_employee_id);
        mysqli_stmt_execute($stmt);
        mysqli_stmt_store_result($stmt);
        
        if (mysqli_stmt_num_rows($stmt) > 0) {
            $exists = true;
        }
        
        mysqli_stmt_close($stmt);
    }
    
    // Return JSON response
    header('Content-Type: application/json');
    echo json_encode(['exists' => $exists]);
    exit;
}

// Handle AJAX request to check email uniqueness
if (isset($_POST['check_email']) && $_POST['check_email'] === 'true' && isset($_POST['email']) && isset($_POST['employee_id'])) {
    $email = trim($_POST['email']);
    $check_employee_id = intval($_POST['employee_id']);
    $exists = false;
    
    // Check if email already exists (excluding the current employee)
    $sql = "SELECT id FROM employees WHERE email = ? AND id != ?";
    if ($stmt = mysqli_prepare($conn, $sql)) {
        mysqli_stmt_bind_param($stmt, "si", $email, $check_employee_id);
        mysqli_stmt_execute($stmt);
        mysqli_stmt_store_result($stmt);
        
        if (mysqli_stmt_num_rows($stmt) > 0) {
            $exists = true;
        }
        
        mysqli_stmt_close($stmt);
    }
    
    // Return JSON response
    header('Content-Type: application/json');
    echo json_encode(['exists' => $exists]);
    exit;
}

// Define variables and initialize with empty values
$username = $email = $role = $first_name = $last_name = "";
$username_err = $email_err = $role_err = $first_name_err = $last_name_err = "";

// Processing form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['action']) && $_POST['action'] == 'update_employee') {
    // Validate username
    if (empty(trim($_POST["username"]))) {
        $username_err = "Please enter a username.";
    } else {
        $param_username = trim($_POST["username"]);
        
        // Check username format requirements
        $underscore_count = substr_count($param_username, '_');
        $number_count = preg_match_all('/\d/', $param_username);
        
        if ($underscore_count < 1) {
            $username_err = "Username must contain at least one underscore (_).";
        } elseif ($number_count < 2) {
            $username_err = "Username must contain at least 2 numbers.";
        } else {
            // Check if username exists (excluding the current employee)
            $sql = "SELECT id FROM employees WHERE username = ? AND id != ?";
            $stmt = mysqli_prepare($conn, $sql);
            mysqli_stmt_bind_param($stmt, "si", $param_username, $employee_id);
            mysqli_stmt_execute($stmt);
            mysqli_stmt_store_result($stmt);
            
            if (mysqli_stmt_num_rows($stmt) == 1) {
                $username_err = "This username is already taken.";
            } else {
                $username = $param_username;
            }
            mysqli_stmt_close($stmt);
        }
    }
    
    // Validate email
    if (empty(trim($_POST["email"]))) {
        $email_err = "Please enter an email.";
    } else {
        // Check if email exists (excluding the current employee)
        $sql = "SELECT id FROM employees WHERE email = ? AND id != ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "si", $param_email, $employee_id);
        $param_email = trim($_POST["email"]);
        mysqli_stmt_execute($stmt);
        mysqli_stmt_store_result($stmt);
        
        if (mysqli_stmt_num_rows($stmt) == 1) {
            $email_err = "This email is already taken.";
        } else {
            $email = trim($_POST["email"]);
        }
        mysqli_stmt_close($stmt);
    }
    
    // Validate role
    if (empty(trim($_POST["role"]))) {
        $role_err = "Please select a role.";
    } else {
        $role = trim($_POST["role"]);
    }
    
    // Validate first name
    if (empty(trim($_POST["first_name"]))) {
        $first_name_err = "Please enter first name.";
    } else {
        $first_name = trim($_POST["first_name"]);
    }
    
    // Validate last name
    if (empty(trim($_POST["last_name"]))) {
        $last_name_err = "Please enter last name.";
    } else {
        $last_name = trim($_POST["last_name"]);
    }
    
    // Check input errors before updating the database
    if (empty($username_err) && empty($email_err) && empty($role_err) && empty($first_name_err) && empty($last_name_err)) {
        // Prepare an update statement
        $sql = "UPDATE employees SET username = ?, email = ?, role = ?, first_name = ?, last_name = ? WHERE id = ?";
        
        if ($stmt = mysqli_prepare($conn, $sql)) {
            // Bind variables to the prepared statement as parameters
            mysqli_stmt_bind_param($stmt, "sssssi", $param_username, $param_email, $param_role, $param_first_name, $param_last_name, $param_id);
            
            // Set parameters
            $param_username = $username;
            $param_email = $email;
            $param_role = $role;
            $param_first_name = $first_name;
            $param_last_name = $last_name;
            $param_id = $employee_id;
            
            // Attempt to execute the prepared statement
            if (mysqli_stmt_execute($stmt)) {
                // Records updated successfully. Redirect to employees page
                header("location: employees.php?success=1&message=" . urlencode("Employee updated successfully"));
                exit();
            } else {
                echo "Oops! Something went wrong. Please try again later.";
            }
            
            // Close statement
            mysqli_stmt_close($stmt);
        }
    }
}

// Get employee data
$sql = "SELECT username, email, role, first_name, last_name FROM employees WHERE id = ?";
if ($stmt = mysqli_prepare($conn, $sql)) {
    mysqli_stmt_bind_param($stmt, "i", $param_id);
    $param_id = $employee_id;
    
    if (mysqli_stmt_execute($stmt)) {
        $result = mysqli_stmt_get_result($stmt);
        
        if (mysqli_num_rows($result) == 1) {
            $row = mysqli_fetch_array($result, MYSQLI_ASSOC);
            
            // Retrieve individual field value
            $username = $row["username"];
            $email = $row["email"];
            $role = $row["role"];
            $first_name = $row["first_name"];
            $last_name = $row["last_name"];
        } else {
            // No valid employee found, redirect to employees page
            header("location: employees.php?error=1&message=" . urlencode("Employee not found"));
            exit();
        }
    } else {
        echo "Oops! Something went wrong. Please try again later.";
    }
    
    mysqli_stmt_close($stmt);
}

// Close connection
mysqli_close($conn);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="img/minilogo.jpg" type="image/x-icon">
    <title>Edit Employee - CRM System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .text-color { color: #FF6500; }
        .bg-color { background-color: #FF6500; }
    </style>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const usernameInput = document.getElementById('username');
            const emailInput = document.getElementById('email');
            const employeeId = <?php echo $employee_id; ?>;
            
            // Username validation
            if (usernameInput) {
                function validateUsername(username) {
                    const underscoreCount = (username.match(/_/g) || []).length;
                    const numberCount = (username.match(/\d/g) || []).length;
                    
                    if (underscoreCount < 1) {
                        return { valid: false, message: 'Username must contain at least one underscore (_).' };
                    }
                    if (numberCount < 2) {
                        return { valid: false, message: 'Username must contain at least 2 numbers.' };
                    }
                    return { valid: true, message: '' };
                }
                
                function checkUsernameUniqueness(username) {
                    const formData = new FormData();
                    formData.append('username', username);
                    formData.append('employee_id', employeeId);
                    formData.append('check_username', 'true');
                    
                    return fetch(window.location.href, {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => data.exists);
                }
                
                function updateUsernameError(message) {
                    const errorP = usernameInput.parentNode.querySelector('.text-red-600');
                    if (errorP) {
                        errorP.textContent = message;
                    }
                    
                    if (message) {
                        usernameInput.classList.add('border-red-500');
                        usernameInput.setCustomValidity(message);
                    } else {
                        usernameInput.classList.remove('border-red-500');
                        usernameInput.setCustomValidity('');
                    }
                }
                
                let usernameDebounceTimer;
                usernameInput.addEventListener('input', function() {
                    clearTimeout(usernameDebounceTimer);
                    const username = this.value.trim();
                    
                    // Clear previous errors immediately
                    updateUsernameError('');
                    
                    if (username === '') {
                        return;
                    }
                    
                    // Validate format first
                    const formatValidation = validateUsername(username);
                    if (!formatValidation.valid) {
                        updateUsernameError(formatValidation.message);
                        return;
                    }
                    
                    // Check uniqueness after a delay
                    usernameDebounceTimer = setTimeout(() => {
                        checkUsernameUniqueness(username)
                            .then(exists => {
                                if (exists) {
                                    updateUsernameError('This username is already taken.');
                                }
                            })
                            .catch(error => {
                                console.error('Error checking username uniqueness:', error);
                            });
                    }, 500);
                });
                
                usernameInput.addEventListener('blur', function() {
                    const username = this.value.trim();
                    if (username === '') return;
                    
                    const formatValidation = validateUsername(username);
                    if (!formatValidation.valid) {
                        updateUsernameError(formatValidation.message);
                        return;
                    }
                    
                    checkUsernameUniqueness(username)
                        .then(exists => {
                            if (exists) {
                                updateUsernameError('This username is already taken.');
                            }
                        })
                        .catch(error => {
                            console.error('Error checking username uniqueness:', error);
                        });
                });
            }
            
            // Email validation
            if (emailInput) {
                function checkEmailUniqueness(email) {
                    const formData = new FormData();
                    formData.append('email', email);
                    formData.append('employee_id', employeeId);
                    formData.append('check_email', 'true');
                    
                    return fetch(window.location.href, {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => data.exists);
                }
                
                function updateEmailError(message) {
                    const errorP = emailInput.parentNode.querySelector('.text-red-600');
                    if (errorP) {
                        errorP.textContent = message;
                    }
                    
                    if (message) {
                        emailInput.classList.add('border-red-500');
                        emailInput.setCustomValidity(message);
                    } else {
                        emailInput.classList.remove('border-red-500');
                        emailInput.setCustomValidity('');
                    }
                }
                
                let emailDebounceTimer;
                emailInput.addEventListener('input', function() {
                    clearTimeout(emailDebounceTimer);
                    const email = this.value.trim();
                    
                    // Clear previous errors immediately
                    updateEmailError('');
                    
                    if (email === '' || !this.checkValidity()) {
                        return;
                    }
                    
                    // Check uniqueness after a delay
                    emailDebounceTimer = setTimeout(() => {
                        checkEmailUniqueness(email)
                            .then(exists => {
                                if (exists) {
                                    updateEmailError('This email is already taken.');
                                }
                            })
                            .catch(error => {
                                console.error('Error checking email uniqueness:', error);
                            });
                    }, 500);
                });
                
                emailInput.addEventListener('blur', function() {
                    const email = this.value.trim();
                    if (email === '' || !this.checkValidity()) return;
                    
                    checkEmailUniqueness(email)
                        .then(exists => {
                            if (exists) {
                                updateEmailError('This email is already taken.');
                            }
                        })
                        .catch(error => {
                            console.error('Error checking email uniqueness:', error);
                        });
                });
            }
        });
    </script>
</head>
<body class="bg-gray-100">

<?php include 'includes/navigation.php'; ?>

<!-- Main Content -->
<div class="ml-0 min-[870px]:ml-60 min-h-screen flex flex-col">
    <header class="bg-[#1E3E62] p-4 flex justify-between z-10 flex-col">
        <h1 class="text-2xl font-bold text-white mx-10 my-2">Edit Employee</h1>
            </header>
    
    <main class="flex-1 p-10">
        <div class="max-w-2xl mx-auto">
            <!-- Back Button -->
            <div class="mb-6">
                <a href="employees.php" class="text-blue-600 hover:text-blue-800">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Employees
                </a>
            </div>

            <!-- Edit Employee Form -->
            <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                <div class="px-6 py-5 border-b border-gray-200 sm:px-8" style="background: linear-gradient(to right, #1E3E62, #0B192C);">
                    <h3 class="text-lg leading-6 font-medium text-white">
                        Employee Information
                    </h3>
                    <p class="mt-1 max-w-2xl text-sm text-white">
                        Update the employee's details and role
                    </p>
                </div>
                <div class="px-4 py-5 sm:p-6">
                    <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]) . "?id=" . $employee_id; ?>" method="post" class="space-y-4">
                        <input type="hidden" name="action" value="update_employee">
                        
                        <div class="grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-2">
                            <div>
                                <label for="first_name" class="block text-sm font-medium text-gray-700">First Name <span class="text-red-500">*</span></label>
                                <input type="text" name="first_name" id="first_name" value="<?php echo htmlspecialchars($first_name); ?>" 
                                       class="mt-1 block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm <?php echo (!empty($first_name_err)) ? 'border-red-500' : ''; ?>" required>
                                <?php if (!empty($first_name_err)): ?>
                                    <p class="mt-1 text-sm text-red-600"><?php echo $first_name_err; ?></p>
                                <?php endif; ?>
                            </div>
                            
                            <div>
                                <label for="last_name" class="block text-sm font-medium text-gray-700">Last Name <span class="text-red-500">*</span></label>
                                <input type="text" name="last_name" id="last_name" value="<?php echo htmlspecialchars($last_name); ?>" 
                                       class="mt-1 block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm <?php echo (!empty($last_name_err)) ? 'border-red-500' : ''; ?>" required>
                                <?php if (!empty($last_name_err)): ?>
                                    <p class="mt-1 text-sm text-red-600"><?php echo $last_name_err; ?></p>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div>
                            <label for="username" class="block text-sm font-medium text-gray-700">Username <span class="text-red-500">*</span></label>
                            <input type="text" name="username" id="username" value="<?php echo htmlspecialchars($username); ?>" 
                                   class="mt-1 block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm <?php echo (!empty($username_err)) ? 'border-red-500' : ''; ?>" required>
                            <?php if (!empty($username_err)): ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo $username_err; ?></p>
                            <?php else: ?>
                                <p class="mt-1 text-sm text-red-600"></p>
                            <?php endif; ?>
                            <p class="text-xs text-gray-500 mt-1">Username must contain at least one underscore (_) and at least 2 numbers</p>
                        </div>
                        
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700">Email <span class="text-red-500">*</span></label>
                            <input type="email" name="email" id="email" value="<?php echo htmlspecialchars($email); ?>" 
                                   class="mt-1 block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm <?php echo (!empty($email_err)) ? 'border-red-500' : ''; ?>" required>
                            <?php if (!empty($email_err)): ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo $email_err; ?></p>
                            <?php else: ?>
                                <p class="mt-1 text-sm text-red-600"></p>
                            <?php endif; ?>
                        </div>
                        
                        <div>
                            <label for="role" class="block text-sm font-medium text-gray-700">Role <span class="text-red-500">*</span></label>
                            <select name="role" id="role" class="mt-1 block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm <?php echo (!empty($role_err)) ? 'border-red-500' : ''; ?>" required>
                                <option value="">Select a role</option>
                                <option value="admin" <?php echo ($role == 'admin') ? 'selected' : ''; ?>>Administrator</option>
                                <option value="manager" <?php echo ($role == 'manager') ? 'selected' : ''; ?>>Manager</option>
                                <option value="sales_rep" <?php echo ($role == 'sales_rep') ? 'selected' : ''; ?>>Sales Representative</option>
                            </select>
                            <?php if (!empty($role_err)): ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo $role_err; ?></p>
                            <?php endif; ?>
                        </div>
                        
                        <div class="flex justify-end space-x-3 pt-4">
                            <a href="employees.php" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                Cancel
                            </a>
                            <button type="submit" class="px-4 py-2 bg-blue-600 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" title="Update Employee">
                                <i class="fas fa-save"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>
</div>

</body>
</html>
