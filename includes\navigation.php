<?php
// This file contains the common navigation sidebar for all dashboard pages

// Determine if the current script is in a subfolder (like /auth/ or /includes/)
$script_path = $_SERVER['SCRIPT_NAME'];
$is_in_subfolder = strpos($script_path, '/auth/') !== false || strpos($script_path, '/includes/') !== false;

// Helper for path prefix
$prefix = $is_in_subfolder ? '../' : '';

// Include notification helper for session-based notifications
require_once __DIR__ . '/notification_helper.php';

// Get unread notification count from session
$unread_count = get_unread_notification_count();
?>
<!-- Mobile/Tablet Menu Toggle Button -->
<button id="menu-toggle" class="fixed top-4 right-4 z-50 bg-[#0b192c] text-white px-3 py-1 shadow-lg hover:bg-[#1e3e62] transition-colors" onclick="document.getElementById('sidebar').classList.remove('-translate-x-full'); this.classList.add('hidden');">
  <i class="fas fa-bars text-xl"></i>
</button>

<style>
@media (min-width: 870px) {
  #menu-toggle {
    display: none !important;
  }
}
#menu-toggle.hidden {
  display: none !important;
}
</style>

<!-- Sidebar -->
<?php if (has_role("sales_rep")) : ?>
<!-- Sales Representative Sidebar -->
<div id="sidebar" class="fixed left-0 top-0 w-56 min-[870px]:w-60 h-screen bg-[#0b192C] flex flex-col justify-between items-center py-2 min-[870px]:py-4 z-20 transform -translate-x-full min-[870px]:translate-x-0 transition-transform duration-300 ease-in-out">
    <!-- Close button for mobile/tablet -->
    <button class="close-menu-btn absolute top-2 right-2 text-white min-[870px]:hidden hover:text-orange-500 transition-colors" onclick="document.getElementById('sidebar').classList.add('-translate-x-full'); document.getElementById('menu-toggle').classList.remove('hidden');">
      <i class="fas fa-times text-xl"></i>
    </button>
    
    <div class="mb-6 md:mb-8 lg:mb-10 mt-8 md:mt-0">
      <img src="<?php echo $prefix; ?>img/logo.png" alt="Logo" class="w-32 md:w-36 lg:w-40" />
    </div>
    <nav class="flex flex-col gap-4 md:gap-5 lg:gap-6">
      <a href="<?php echo $prefix; ?>sales_rep_dashboard.php" class="flex items-center gap-2 <?php echo is_active_page('sales_rep_dashboard.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <img src="<?php echo $prefix; ?>img/dashboard.png" alt="Dashboard" class="w-5 h-5 md:w-6 md:h-6" />
        <span class="text-base md:text-lg font-medium">Dashboard</span>
      </a>
      <a href="<?php echo $prefix; ?>leads.php" class="flex items-center gap-2 <?php echo is_active_page('leads.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <img src="<?php echo $prefix; ?>img/leads.png" alt="My Leads" class="w-5 h-5 md:w-6 md:h-6" />
        <span class="text-base md:text-lg font-medium">My Leads</span>
      </a>
      <a href="<?php echo $prefix; ?>my_contacts.php" class="flex items-center gap-2 <?php echo is_active_page('my_contacts.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <i class="fas fa-address-book w-5 h-5 md:w-6 md:h-6 flex items-center justify-center"></i>
        <span class="text-base md:text-lg font-medium">My Contacts</span>
      </a>
      <a href="<?php echo $prefix; ?>my_sales.php" class="flex items-center gap-2 <?php echo is_active_page('my_sales.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <img src="<?php echo $prefix; ?>img/sales.png" alt="My Sales" class="w-5 h-5 md:w-6 md:h-6" />
        <span class="text-base md:text-lg font-medium">My Sales</span>
      </a>
      <a href="<?php echo $prefix; ?>create_sale.php" class="flex items-center gap-2 <?php echo is_active_page('create_sale.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <img src="<?php echo $prefix; ?>img/sales.png" alt="Create Sale" class="w-5 h-5 md:w-6 md:h-6" />
        <span class="text-base md:text-lg font-medium">Create Sale</span>
      </a>
      <a href="<?php echo $prefix; ?>products.php" class="flex items-center gap-2 <?php echo is_active_page('products.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <img src="<?php echo $prefix; ?>img/products.png" alt="Products" class="w-5 h-5 md:w-6 md:h-6" />
        <span class="text-base md:text-lg font-medium">Products</span>
      </a>
      <?php if (has_role("admin")) : ?>
      <a href="<?php echo $prefix; ?>employees.php" class="flex items-center gap-2 <?php echo is_active_page('employees.php') || is_active_page('edit_employee.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <i class="fas fa-users w-5 h-5 md:w-6 md:h-6 flex items-center justify-center"></i>
        <span class="text-base md:text-lg font-medium">Employees</span>
      </a>
      <?php endif; ?>
    </nav>
    <div class="flex flex-col items-center mb-2">
      <div class="flex items-center gap-2 bg-[#0a1525] rounded-lg p-2">
        <div class="w-8 h-8 rounded-full bg-white flex items-center justify-center">
          <img src="<?php echo $prefix; ?>img/user.png" alt="User" class="w-5 h-5" />
        </div>
        <div class="flex flex-col">
          <p class="font-semibold text-white text-xs leading-tight truncate max-w-[100px]">
            <?php echo htmlspecialchars($_SESSION["first_name"] . " " . $_SESSION["last_name"]); ?>
          </p>
          <p class="text-[10px] text-gray-300 leading-tight"><?php echo htmlspecialchars(ucfirst($_SESSION["role"])); ?></p>
        </div>
      </div>
      <a href="<?php echo $prefix; ?>auth/logout.php" id="logout-btn" class="mt-2 bg-[#FF6500] text-white px-2 py-1 rounded-md flex items-center gap-1 hover:bg-orange-600 text-xs">
        <i class="fas fa-sign-out-alt text-[10px]"></i>
        <span class="font-medium text-[10px]">LOG OUT</span>
      </a>
    </div>
</div>
<?php else : ?>
<!-- Admin Sidebar -->
<div id="sidebar" class="fixed left-0 top-0 w-56 min-[870px]:w-60 h-screen bg-[#0b192C] flex flex-col justify-between items-center py-2 min-[870px]:py-4 z-20 transform -translate-x-full min-[870px]:translate-x-0 transition-transform duration-300 ease-in-out">
    <button class="close-menu-btn absolute top-2 right-2 text-white min-[870px]:hidden p-2 hover:text-orange-500 transition-colors" onclick="document.getElementById('sidebar').classList.add('-translate-x-full'); document.getElementById('menu-toggle').classList.remove('hidden');">
      <i class="fas fa-times text-xl"></i>
    </button>
    <div class="mb-6 md:mb-8 lg:mb-10 mt-8 md:mt-0">
      <img src="<?php echo $prefix; ?>img/logo.png" alt="Logo" class="w-32 md:w-36 lg:w-40" />
    </div>
    <nav class="flex flex-col gap-4 md:gap-5 lg:gap-6">
      <a href="<?php echo $prefix; ?>admin.php" class="flex items-center gap-2 <?php echo is_active_page('admin.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <img src="<?php echo $prefix; ?>img/dashboard.png" alt="Admin" class="w-5 h-5 md:w-6 md:h-6" />
        <span class="text-base md:text-lg font-medium">Admin</span>
      </a>
      <a href="<?php echo $prefix; ?>leads.php" class="flex items-center gap-2 <?php echo is_active_page('leads.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <img src="<?php echo $prefix; ?>img/leads.png" alt="Leads" class="w-5 h-5 md:w-6 md:h-6" />
        <span class="text-base md:text-lg font-medium">Leads</span>
      </a>
      <a href="<?php echo $prefix; ?>contacts.php" class="flex items-center gap-2 <?php echo is_active_page('contacts.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <i class="fas fa-address-book w-5 h-5 md:w-6 md:h-6 flex items-center justify-center"></i>
        <span class="text-base md:text-lg font-medium">Contacts</span>
      </a>
      <a href="<?php echo $prefix; ?>sales.php" class="flex items-center gap-2 <?php echo is_active_page('sales.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <img src="<?php echo $prefix; ?>img/sales.png" alt="Sales" class="w-5 h-5 md:w-6 md:h-6" />
        <span class="text-base md:text-lg font-medium">Sales</span>
      </a>
      <a href="<?php echo $prefix; ?>products.php" class="flex items-center gap-2 <?php echo is_active_page('products.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <img src="<?php echo $prefix; ?>img/products.png" alt="Products" class="w-5 h-5 md:w-6 md:h-6" />
        <span class="text-base md:text-lg font-medium">Products</span>
      </a>
      <?php if (has_role("admin")) : ?>
      <a href="<?php echo $prefix; ?>employees.php" class="flex items-center gap-2 <?php echo is_active_page('employees.php') || is_active_page('edit_employee.php') ? 'text-orange-500 font-bold' : 'text-white hover:text-orange-500'; ?>">
        <i class="fas fa-users w-5 h-5 md:w-6 md:h-6 flex items-center justify-center"></i>
        <span class="text-base md:text-lg font-medium">Employees</span>
      </a>
      <?php endif; ?>
    </nav>
    <div class="flex flex-col items-center mb-2">
      <div class="flex items-center gap-2 bg-[#0a1525] rounded-lg p-2">
        <div class="w-8 h-8 rounded-full bg-white flex items-center justify-center">
          <img src="<?php echo $prefix; ?>img/user.png" alt="User" class="w-5 h-5" />
        </div>
        <div class="flex flex-col">
          <p class="font-semibold text-white text-xs leading-tight truncate max-w-[100px]">
            <?php echo htmlspecialchars($_SESSION["first_name"] . " " . $_SESSION["last_name"]); ?>
          </p>
          <p class="text-[10px] text-gray-300 leading-tight"><?php echo htmlspecialchars(ucfirst($_SESSION["role"])); ?></p>
        </div>
      </div>
      <a href="<?php echo $prefix; ?>auth/logout.php" id="logout-btn" class="mt-2 bg-[#FF6500] text-white px-2 py-1 rounded-md flex items-center gap-1 hover:bg-orange-600 text-xs">
        <i class="fas fa-sign-out-alt text-[10px]"></i>
        <span class="font-medium text-[10px]">LOG OUT</span>
      </a>
    </div>
</div>
<?php endif; ?>
