<?php
// Get database connection using reliable method
$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';
require_once 'includes/validation.php';

// Check if database is set up first
require_once 'check_database_setup.php';
redirect_to_setup_if_needed();

// Check if the user is logged in
ensure_session_started();
require_login();

// Check if user is admin or manager
$is_sales_rep = isset($_SESSION['role']) && $_SESSION['role'] === 'sales_rep';
if ($is_sales_rep) {
    header("Location: leads.php");
    exit;
}

$error_message = '';
$success_message = '';

// Handle file upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['csv_file'])) {
    $file = $_FILES['csv_file'];
    
    if ($file['error'] === UPLOAD_ERR_OK) {
        $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        
        if ($file_extension === 'csv') {
            $handle = fopen($file['tmp_name'], 'r');
            
            if ($handle !== FALSE) {
                $header = fgetcsv($handle); // Read header row
                $imported_count = 0;
                $error_count = 0;
                $errors = [];
                
                while (($data = fgetcsv($handle)) !== FALSE) {
                    if (count($data) >= 3) { // At least first_name, last_name, email
                        $first_name = sanitize_input($data[0]);
                        $last_name = sanitize_input($data[1]);
                        $email = sanitize_input($data[2]);
                        $phone = isset($data[3]) ? sanitize_input($data[3]) : '';
                        $company = isset($data[4]) ? sanitize_input($data[4]) : '';
                        $customer_interest = isset($data[5]) ? sanitize_input($data[5]) : '';
                        $source = isset($data[6]) ? sanitize_input($data[6]) : 'Import';
                        
                        // Validate required fields
                        if (!empty($first_name) && !empty($last_name) && !empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL)) {
                            // Check if email already exists
                            $check_sql = "SELECT id FROM leads WHERE email = ? UNION SELECT id FROM contacts WHERE email = ?";
                            $check_stmt = mysqli_prepare($conn, $check_sql);
                            mysqli_stmt_bind_param($check_stmt, "ss", $email, $email);
                            mysqli_stmt_execute($check_stmt);
                            $check_result = mysqli_stmt_get_result($check_stmt);
                            
                            if (mysqli_num_rows($check_result) == 0) {
                                // Insert new lead
                                $insert_sql = "INSERT INTO leads (first_name, last_name, email, phone, company, customer_interest, source, created_by, created_at) 
                                              VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";
                                $insert_stmt = mysqli_prepare($conn, $insert_sql);
                                mysqli_stmt_bind_param($insert_stmt, "sssssssi", 
                                    $first_name, $last_name, $email, $phone, $company, $customer_interest, $source, $_SESSION['user_id']);
                                
                                if (mysqli_stmt_execute($insert_stmt)) {
                                    $imported_count++;
                                } else {
                                    $error_count++;
                                    $errors[] = "Error importing $email: " . mysqli_error($conn);
                                }
                            } else {
                                $error_count++;
                                $errors[] = "Email $email already exists";
                            }
                        } else {
                            $error_count++;
                            $errors[] = "Invalid data for row: " . implode(', ', $data);
                        }
                    }
                }
                
                fclose($handle);
                
                if ($imported_count > 0) {
                    $success_message = "$imported_count lead(s) imported successfully.";
                    if ($error_count > 0) {
                        $success_message .= " $error_count row(s) had errors.";
                    }
                } else {
                    $error_message = "No leads were imported. " . ($error_count > 0 ? "$error_count row(s) had errors." : "");
                }
                
                if (!empty($errors) && count($errors) <= 10) {
                    $error_message .= "<br><br>Errors:<br>" . implode("<br>", array_slice($errors, 0, 10));
                }
            } else {
                $error_message = "Error reading the CSV file.";
            }
        } else {
            $error_message = "Please upload a CSV file.";
        }
    } else {
        $error_message = "Error uploading file.";
    }
}

$page_title = "Import Leads";
include __DIR__ . '/includes/header.php';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="img/minilogo.jpg" type="image/x-icon">
    <title>Import Leads - CRM System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">

<?php include 'includes/navigation.php'; ?>

<!-- Main Content -->
<div class="ml-0 min-[870px]:ml-60 min-h-screen flex flex-col">
    <header class="bg-[#1E3E62] p-4 flex justify-between items-center z-10">
        <h1 class="text-2xl font-bold text-white mx-10 my-2">Import Leads</h1>
        <div class="mx-10">
            <?php include 'includes/top_notification.php'; ?>
        </div>
    </header>

    <main class="flex-1 p-10">
        <!-- Back Button -->
        <div class="mb-6">
            <a href="leads.php" class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Leads
            </a>
        </div>

        <!-- Success/Error Messages -->
        <?php if (!empty($success_message)): ?>
            <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                <?php echo $success_message; ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($error_message)): ?>
            <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                <?php echo $error_message; ?>
            </div>
        <?php endif; ?>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Upload Form -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Upload CSV File</h2>
                
                <form method="POST" enctype="multipart/form-data" class="space-y-4">
                    <div>
                        <label for="csv_file" class="block text-sm font-medium text-gray-700 mb-2">
                            Select CSV File *
                        </label>
                        <input type="file" id="csv_file" name="csv_file" accept=".csv" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <p class="text-sm text-gray-500 mt-1">Maximum file size: 5MB</p>
                    </div>
                    
                    <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors">
                        <i class="fas fa-upload mr-2"></i>
                        Import Leads
                    </button>
                </form>
            </div>

            <!-- Instructions -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">CSV Format Instructions</h2>
                
                <div class="space-y-4">
                    <div>
                        <h3 class="font-medium text-gray-700 mb-2">Required Columns (in order):</h3>
                        <ol class="list-decimal list-inside text-sm text-gray-600 space-y-1">
                            <li>First Name *</li>
                            <li>Last Name *</li>
                            <li>Email *</li>
                            <li>Phone (optional)</li>
                            <li>Company (optional)</li>
                            <li>Interest (optional)</li>
                            <li>Source (optional)</li>
                        </ol>
                    </div>
                    
                    <div>
                        <h3 class="font-medium text-gray-700 mb-2">Sample CSV Format:</h3>
                        <div class="bg-gray-100 p-3 rounded text-sm font-mono">
                            First Name,Last Name,Email,Phone,Company,Interest,Source<br>
                            John,Doe,<EMAIL>,9876543210,ABC Corp,Basic Insurance,Referral<br>
                            Jane,Smith,<EMAIL>,8765432109,XYZ Ltd,Premium Plan,Web Form
                        </div>
                    </div>
                    
                    <div class="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                        <h4 class="font-medium text-yellow-800 mb-1">Important Notes:</h4>
                        <ul class="text-sm text-yellow-700 space-y-1">
                            <li>• Duplicate emails will be skipped</li>
                            <li>• Invalid email formats will be rejected</li>
                            <li>• All imported leads will have status "new"</li>
                            <li>• Source will default to "Import" if not specified</li>
                        </ul>
                    </div>
                </div>
                
                <div class="mt-6">
                    <a href="templates/leads_import_template.csv" download 
                       class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
                        <i class="fas fa-download mr-2"></i>
                        Download Template
                    </a>
                </div>
            </div>
        </div>
    </main>
</div>

</body>
</html>
