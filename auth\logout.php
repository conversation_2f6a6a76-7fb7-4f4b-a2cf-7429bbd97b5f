<?php
// Include database configuration and functions
$conn = require_once '../config/database.php';
require_once '../includes/functions.php';

// Initialize the session
ensure_session_started();

// Clear remember token and expiry in database if user is logged in
if (isset($_SESSION['user_id'])) {
    $user_id = $_SESSION['user_id'];
    
    // Use the enhanced clear_remember_cookie function to clear both cookies and database
    clear_remember_cookie($user_id, $conn);
} else {
    // If no user is logged in, just clear the cookies
    clear_remember_cookie();
}
 
// Unset all of the session variables
$_SESSION = array();
 
// Destroy the session
session_destroy();
 
// Redirect to login page
header("location: login.php");
exit;
?>
