<?php
// Include database configuration
$conn = require_once '../config/database.php';
require_once '../includes/functions.php';

// Initialize variables
$token = $email = "";
$token_err = $password_err = $confirm_password_err = $success_msg = "";
$token_valid = false;
$user_id = null;

// Check if token and email are provided in the URL
if (isset($_GET['token']) && isset($_GET['email'])) {
    $token = $_GET['token'];
    $email = $_GET['email'];
    
    // Verify the token and email
    $sql = "SELECT id, reset_token, reset_token_expiry FROM employees WHERE email = ?";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "s", $email);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if (mysqli_num_rows($result) == 1) {
        $user = mysqli_fetch_assoc($result);
        
        // Check if token is expired
        if (strtotime($user['reset_token_expiry']) < time()) {
            $token_err = "Password reset link has expired. Please request a new one.";
        } else if ($user['reset_token'] && password_verify($token, $user['reset_token'])) {
            // Token is valid
            $token_valid = true;
            $user_id = $user['id'];
        } else {
            $token_err = "Invalid password reset link.";
        }
    } else {
        $token_err = "Invalid password reset link.";
    }
    
    mysqli_stmt_close($stmt);
} else {
    $token_err = "Invalid password reset request.";
}

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST" && $token_valid) {
    // Validate password
    if (empty(trim($_POST["password"]))) {
        $password_err = "Please enter a password.";     
    } else {
        $password_result = sanitize_and_validate(trim($_POST["password"]), 'password');
        if (!$password_result['valid']) {
            $password_err = "Password must have at least 6 characters with at least one uppercase letter, one lowercase letter, one number, and one special character. No 4 or more consecutive digits allowed.";
        } else {
            $password = $password_result['value'];
        }
    }
    
    // Validate confirm password
    if (empty(trim($_POST["confirm_password"]))) {
        $confirm_password_err = "Please confirm password.";     
    } else {
        $confirm_password = trim($_POST["confirm_password"]);
        if (empty($password_err) && ($password != $confirm_password)) {
            $confirm_password_err = "Password did not match.";
        }
    }
    
    // Check input errors before updating the database
    if (empty($password_err) && empty($confirm_password_err)) {
        // Update password and clear reset token
        $sql = "UPDATE employees SET password = ?, reset_token = NULL, reset_token_expiry = NULL WHERE id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        $param_password = password_hash($password, PASSWORD_DEFAULT);
        mysqli_stmt_bind_param($stmt, "si", $param_password, $user_id);
        
        if (mysqli_stmt_execute($stmt)) {
            // Get user email for confirmation message
            $user_email = $email;
            
            // Send confirmation email
            require_once '../includes/phpmailer_config.php';
            
            $subject = "Your Password Has Been Changed - CRM System";
            $body = '
            <html>
            <head>
                <title>Password Change Successful</title>
            </head>
            <body>
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
                    <h2 style="color: #FF6500;">Password Change Successful</h2>
                    <p>Hello,</p>
                    <p>Your password for the CRM System has been successfully changed.</p>
                    <p>If you did not make this change, please contact support immediately as your account may have been compromised.</p>
                    <p style="text-align: center; margin: 30px 0;">
                        <a href="http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) . '/login.php" style="background-color: #FF6500; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block; font-weight: bold;">Login to Your Account</a>
                    </p>
                    <p style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 12px; color: #777;">
                        This is an automated message, please do not reply.
                    </p>
                </div>
            </body>
            </html>';
            
            // Send email (don't block the user experience if email fails)
            $email_result = send_email($user_email, $subject, $body);
            
            // Log any email sending errors
            if (!$email_result['success']) {
                error_log("Failed to send password reset confirmation email to {$user_email}: " . $email_result['message']);
            }
            
            $success_msg = "Your password has been changed successfully. You can now <a href='login.php' class='text-blue-500 underline'>login</a> with your new password.";
        } else {
            $token_err = "Oops! Something went wrong. Please try again later.";
        }
        
        mysqli_stmt_close($stmt);
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - CRM System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .text-color{ color: #FF6500; }
        .button-hover { transition: background-color 0.3s ease, transform 0.2s ease; }
        .button-hover:hover { background-color: #e55b00; transform: scale(1.05); }
    </style>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const passwordInput = document.getElementById('password');
            const strengthMeter = document.getElementById('password-strength-meter');
            const strengthText = document.getElementById('password-strength-text');
            
            if (passwordInput && strengthMeter && strengthText) {
                passwordInput.addEventListener('input', function() {
                    const password = passwordInput.value;
                    const strength = checkPasswordStrength(password);
                    
                    // Update the strength meter
                    let meterWidth = (strength.score / 4) * 100;
                    strengthMeter.style.width = meterWidth + '%';
                    
                    // Update color based on strength
                    let color;
                    switch(strength.score) {
                        case 0: color = '#EF4444'; break; // red-500
                        case 1: color = '#F59E0B'; break; // amber-500
                        case 2: color = '#F59E0B'; break; // amber-500
                        case 3: color = '#10B981'; break; // emerald-500
                        case 4: color = '#059669'; break; // emerald-600
                        default: color = '#6B7280'; // gray-500
                    }
                    strengthMeter.style.backgroundColor = color;
                    
                    // Update text
                    strengthText.textContent = strength.feedback;
                    strengthText.style.color = color;
                });
            }
            
            // Function to check password strength
            function checkPasswordStrength(password) {
                let score = 0;
                let feedback = '';
                
                // Check for 4 or more consecutive digits
                if (/\d{4,}/.test(password)) {
                    return { score: 0, feedback: 'No 4+ consecutive digits allowed' };
                }
                
                // Length check
                if (password.length < 6) {
                    return { score: 0, feedback: 'Very weak' };
                } else if (password.length >= 10) {
                    score += 1;
                }
                
                // Complexity checks
                if (/[a-z]/.test(password)) score += 0.5;
                if (/[A-Z]/.test(password)) score += 0.5;
                if (/\d/.test(password)) score += 1;
                if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) score += 1;
                
                // Variety check
                const uniqueChars = new Set(password.split('')).size;
                if (uniqueChars > 7) score += 1;
                
                // Round the score and cap it at 4
                score = Math.min(4, Math.round(score));
                
                // Generate feedback based on score
                switch (score) {
                    case 0:
                        feedback = 'Very weak';
                        break;
                    case 1:
                        feedback = 'Weak';
                        break;
                    case 2:
                        feedback = 'Fair';
                        break;
                    case 3:
                        feedback = 'Good';
                        break;
                    case 4:
                        feedback = 'Strong';
                        break;
                }
                
                return { score, feedback };
            }
        });
    </script>
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center">
    <div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-md">
        <div class="text-center mb-8">
            <img src="../img/logo.png" alt="Logo" class="w-40 mx-auto rounded-lg">
            <h2 class="text-2xl font-bold mt-4 text-[#0b192C]">Set New Password</h2>
            <p class="text-gray-600 mt-2">Create a new password for your account</p>
        </div>

        <?php if (!empty($token_err)) : ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                <span class="block sm:inline"><?php echo $token_err; ?></span>
            </div>
            <div class="mt-6 text-center">
                <a href="forgot_password.php" class="text-sm text-[#FF6500] hover:underline">Request a new password reset</a>
            </div>
        <?php endif; ?>

        <?php if (!empty($success_msg)) : ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4 overflow-hidden" role="alert">
                <div class="block sm:inline break-words"><?php echo $success_msg; ?></div>
            </div>
        <?php endif; ?>

        <?php if ($token_valid && empty($success_msg)) : ?>
            <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]) . '?token=' . urlencode($token) . '&email=' . urlencode($email); ?>" method="post">
                <div class="mb-4">
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-1">New Password <span class="text-red-500">*</span></label>
                    <input type="password" name="password" id="password" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6500] <?php echo (!empty($password_err)) ? 'border-red-500' : ''; ?>" required>
                    <span class="text-red-500 text-xs"><?php echo $password_err; ?></span>
                    
                    <!-- Password strength meter -->
                    <div class="mt-2">
                        <div class="flex items-center">
                            <div class="w-full bg-gray-200 rounded-full h-2.5 mr-2">
                                <div class="bg-gray-500 h-2.5 rounded-full" id="password-strength-meter" style="width: 0%"></div>
                            </div>
                            <span class="text-xs text-gray-500" id="password-strength-text">Password strength</span>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Password must contain at least 6 characters with uppercase, lowercase, number, and special character. No 4 or more consecutive digits allowed.</p>
                    </div>
                </div>
                <div class="mb-6">
                    <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-1">Confirm New Password <span class="text-red-500">*</span></label>
                    <input type="password" name="confirm_password" id="confirm_password" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6500] <?php echo (!empty($confirm_password_err)) ? 'border-red-500' : ''; ?>" required>
                    <span class="text-red-500 text-xs"><?php echo $confirm_password_err; ?></span>
                </div>
                <button type="submit" class="w-full bg-[#0b192c] text-white py-2 px-4 rounded-sm hover:bg-[#1e3e62]">
                    Set New Password
                </button>
            </form>
        <?php endif; ?>
        
        <?php if (empty($token_err) && empty($success_msg)) : ?>
            <div class="mt-6 text-center">
                <a href="login.php" class="text-sm text-[#FF6500] hover:underline">Back to Login</a>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
