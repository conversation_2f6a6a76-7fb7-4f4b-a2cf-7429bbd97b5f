<?php
/**
 * Session-based notification helper functions
 */

/**
 * Add a notification to the session
 * 
 * @param string $title Notification title
 * @param string $message Notification message
 * @param string $type Notification type (info, success, warning, error)
 * @param string|null $link Optional link for the notification
 */
function add_notification($title, $message, $type = 'info', $link = null) {
    // Ensure session is started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // Initialize notifications array if not set
    if (!isset($_SESSION['notifications'])) {
        $_SESSION['notifications'] = [];
    }
    
    // Create notification
    $notification = [
        'id' => uniqid(),
        'title' => $title,
        'message' => $message,
        'type' => $type,
        'link' => $link,
        'is_read' => false,
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    // Add to beginning of array (newest first)
    array_unshift($_SESSION['notifications'], $notification);
    
    // Keep only last 20 notifications to prevent session bloat
    if (count($_SESSION['notifications']) > 20) {
        $_SESSION['notifications'] = array_slice($_SESSION['notifications'], 0, 20);
    }
}

/**
 * Add a success notification
 */
function add_success_notification($title, $message, $link = null) {
    add_notification($title, $message, 'success', $link);
}

/**
 * Add an info notification
 */
function add_info_notification($title, $message, $link = null) {
    add_notification($title, $message, 'info', $link);
}

/**
 * Add a warning notification
 */
function add_warning_notification($title, $message, $link = null) {
    add_notification($title, $message, 'warning', $link);
}

/**
 * Add an error notification
 */
function add_error_notification($title, $message, $link = null) {
    add_notification($title, $message, 'error', $link);
}

/**
 * Get unread notification count
 */
function get_unread_notification_count() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    if (!isset($_SESSION['notifications'])) {
        return 0;
    }
    
    $count = 0;
    foreach ($_SESSION['notifications'] as $notification) {
        if (!$notification['is_read']) {
            $count++;
        }
    }
    
    return $count;
}

/**
 * Mark all notifications as read
 */
function mark_all_notifications_read() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    if (isset($_SESSION['notifications'])) {
        foreach ($_SESSION['notifications'] as &$notification) {
            $notification['is_read'] = true;
        }
    }
}

/**
 * Clear all notifications
 */
function clear_all_notifications() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    $_SESSION['notifications'] = [];
}

/**
 * Get all notifications
 */
function get_all_notifications() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    return $_SESSION['notifications'] ?? [];
}
?>
