<?php
// Get database connection using reliable method
$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';

// Check if database is set up first
require_once 'check_database_setup.php';
redirect_to_setup_if_needed();

// Check if the user is logged in and has admin role
ensure_session_started();
require_any_role(["admin", "manager"]);

$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_sample_data'])) {
    try {
        // Start transaction
        mysqli_begin_transaction($conn);
        
        // Add 10 fake products
        $products = [
            ['Life Insurance Premium Plan', 25000, 'Insurance'],
            ['Health Insurance Family Cover', 18000, 'Insurance'],
            ['Term Life Insurance', 12000, 'Insurance'],
            ['Car Insurance Comprehensive', 15000, 'Insurance'],
            ['Home Insurance Protection', 8000, 'Insurance'],
            ['Travel Insurance Annual', 3500, 'Insurance'],
            ['Business Insurance Package', 45000, 'Insurance'],
            ['Personal Accident Insurance', 5000, 'Insurance'],
            ['Critical Illness Cover', 22000, 'Insurance'],
            ['Child Education Plan', 35000, 'Insurance']
        ];
        
        $product_ids = [];
        foreach ($products as $product) {
            $sql = "INSERT INTO products (name, price, category) VALUES (?, ?, ?)";
            $stmt = mysqli_prepare($conn, $sql);
            mysqli_stmt_bind_param($stmt, "sds", $product[0], $product[1], $product[2]);
            if (!mysqli_stmt_execute($stmt)) {
                throw new Exception("Error adding product: " . mysqli_error($conn));
            }
            $product_ids[] = mysqli_insert_id($conn);
        }
        
        // Add some sample contacts first (needed for sales)
        $contacts = [
            ['Rajesh', 'Kumar', '<EMAIL>', '**********', '123 MG Road, Mumbai', 'Website', 'Life Insurance'],
            ['Priya', 'Sharma', '<EMAIL>', '**********', '456 Park Street, Delhi', 'Referral', 'Health Insurance'],
            ['Amit', 'Patel', '<EMAIL>', '**********', '789 Brigade Road, Bangalore', 'Cold Call', 'Car Insurance'],
            ['Sunita', 'Singh', '<EMAIL>', '**********', '321 Civil Lines, Pune', 'Social Media', 'Home Insurance'],
            ['Vikram', 'Gupta', '<EMAIL>', '**********', '654 Sector 17, Chandigarh', 'Website', 'Business Insurance']
        ];
        
        $contact_ids = [];
        foreach ($contacts as $contact) {
            // Check if contact already exists
            $check_sql = "SELECT id FROM contacts WHERE email = ?";
            $check_stmt = mysqli_prepare($conn, $check_sql);
            mysqli_stmt_bind_param($check_stmt, "s", $contact[2]);
            mysqli_stmt_execute($check_stmt);
            $check_result = mysqli_stmt_get_result($check_stmt);
            
            if (mysqli_num_rows($check_result) > 0) {
                // Contact exists, get the ID
                $existing_contact = mysqli_fetch_assoc($check_result);
                $contact_ids[] = $existing_contact['id'];
            } else {
                // Add new contact
                $sql = "INSERT INTO contacts (first_name, last_name, email, phone, address, source, customer_interest, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
                $stmt = mysqli_prepare($conn, $sql);
                $created_by = $_SESSION['user_id'];
                mysqli_stmt_bind_param($stmt, "sssssssi", $contact[0], $contact[1], $contact[2], $contact[3], $contact[4], $contact[5], $contact[6], $created_by);
                if (!mysqli_stmt_execute($stmt)) {
                    throw new Exception("Error adding contact: " . mysqli_error($conn));
                }
                $contact_ids[] = mysqli_insert_id($conn);
            }
        }
        
        // Add 5 sample sales records
        $sales = [
            [0, 0, '2024-01-15', 'UPI', 'Customer was very interested in comprehensive life coverage', 1, 25000, 4500, 'INV-********-1001', 'paid'],
            [1, 1, '2024-01-18', 'Credit Card', 'Family health coverage for 4 members', 1, 18000, 3240, 'INV-********-1002', 'paid'],
            [2, 2, '2024-01-22', 'Bank Transfer', 'Comprehensive car insurance with zero depreciation', 1, 15000, 2700, 'INV-********-1003', 'paid'],
            [3, 3, '2024-01-25', 'Cash', 'Home insurance for new apartment', 1, 8000, 1440, 'INV-********-1004', 'pending'],
            [4, 4, '2024-01-28', 'UPI', 'Business insurance for small manufacturing unit', 1, 45000, 8100, 'INV-********-1005', 'paid']
        ];
        
        foreach ($sales as $sale) {
            $contact_id = $contact_ids[$sale[0]];
            $product_id = $product_ids[$sale[1]];
            $sale_date = $sale[2];
            $payment_method = $sale[3];
            $notes = $sale[4];
            $quantity = $sale[5];
            $unit_price = $sale[6];
            $tax = $sale[7];
            $invoice_number = $sale[8];
            $invoice_status = $sale[9];
            
            $subtotal = $quantity * $unit_price;
            $total_amount = $subtotal + $tax;
            
            $sql = "INSERT INTO sales (
                contact_id, 
                sales_rep_id, 
                sale_date, 
                payment_method, 
                notes, 
                product_id, 
                quantity, 
                unit_price, 
                subtotal, 
                tax, 
                total_amount, 
                invoice_number, 
                invoice_status, 
                invoice_date
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = mysqli_prepare($conn, $sql);
            $sales_rep_id = $_SESSION['user_id'];
            mysqli_stmt_bind_param($stmt, "iisssiiddddsss", 
                $contact_id, $sales_rep_id, $sale_date, $payment_method, $notes, 
                $product_id, $quantity, $unit_price, $subtotal, $tax, $total_amount, 
                $invoice_number, $invoice_status, $sale_date
            );
            
            if (!mysqli_stmt_execute($stmt)) {
                throw new Exception("Error adding sale: " . mysqli_error($conn));
            }
        }
        
        // Commit transaction
        mysqli_commit($conn);
        $success_message = "Successfully added 10 products, 5 contacts, and 5 sales records!";
        
    } catch (Exception $e) {
        // Rollback transaction on error
        mysqli_rollback($conn);
        $error_message = $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="img/minilogo.jpg" type="image/x-icon">
    <title>Add Sample Data - CRM System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .text-color { color: #FF6500; }
        .bg-color { background-color: #FF6500; }
    </style>
</head>
<body class="bg-gray-100">

<?php include 'includes/navigation.php'; ?>

<!-- Main Content -->
<div class="ml-0 min-[870px]:ml-60 min-h-screen flex flex-col">
    <header class="bg-[#1E3E62] p-4 flex justify-between z-10 flex-col">
        <h1 class="text-2xl font-bold text-white mx-10 my-2">Add Sample Data</h1>
    </header>
    
    <main class="flex-1 p-10">
        <!-- Success/Error Messages -->
        <?php if (!empty($success_message)): ?>
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
            <div class="flex items-center">
                <i class="fas fa-check-circle mr-2"></i>
                <?php echo $success_message; ?>
            </div>
        </div>
        <?php endif; ?>

        <?php if (!empty($error_message)): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <div class="flex items-center">
                <i class="fas fa-exclamation-circle mr-2"></i>
                <?php echo $error_message; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Sample Data Information -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Add Sample Data to Your CRM</h2>
            <p class="text-gray-600 mb-4">
                This will add sample data to help you test and explore the CRM system features:
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div class="bg-blue-50 p-4 rounded-lg">
                    <h3 class="font-semibold text-blue-800 mb-2">
                        <i class="fas fa-box mr-2"></i>10 Products
                    </h3>
                    <ul class="text-sm text-blue-700 space-y-1">
                        <li>• Life Insurance Plans</li>
                        <li>• Health Insurance</li>
                        <li>• Car Insurance</li>
                        <li>• Home Insurance</li>
                        <li>• Business Insurance</li>
                        <li>• And more...</li>
                    </ul>
                </div>
                
                <div class="bg-green-50 p-4 rounded-lg">
                    <h3 class="font-semibold text-green-800 mb-2">
                        <i class="fas fa-users mr-2"></i>5 Contacts
                    </h3>
                    <ul class="text-sm text-green-700 space-y-1">
                        <li>• Rajesh Kumar</li>
                        <li>• Priya Sharma</li>
                        <li>• Amit Patel</li>
                        <li>• Sunita Singh</li>
                        <li>• Vikram Gupta</li>
                    </ul>
                </div>
                
                <div class="bg-purple-50 p-4 rounded-lg">
                    <h3 class="font-semibold text-purple-800 mb-2">
                        <i class="fas fa-chart-line mr-2"></i>5 Sales
                    </h3>
                    <ul class="text-sm text-purple-700 space-y-1">
                        <li>• Complete sales records</li>
                        <li>• Different payment methods</li>
                        <li>• Invoice numbers</li>
                        <li>• Various amounts</li>
                        <li>• Recent dates</li>
                    </ul>
                </div>
            </div>
            
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-triangle text-yellow-600 mr-2"></i>
                    <p class="text-yellow-800 text-sm">
                        <strong>Note:</strong> This will add sample data to your database. If contacts with the same email already exist, they will be reused for sales records.
                    </p>
                </div>
            </div>
            
            <form method="POST" onsubmit="return confirm('Are you sure you want to add sample data to your CRM?');">
                <button type="submit" name="add_sample_data" class="px-6 py-3 bg-[#0b192c] text-white rounded-sm hover:bg-[#1e3e62] transition-colors">
                    <i class="fas fa-plus mr-2"></i>Add Sample Data
                </button>
                <a href="admin.php" class="ml-4 px-6 py-3 bg-gray-300 text-gray-700 rounded-sm hover:bg-gray-400 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Admin
                </a>
            </form>
        </div>
    </main>
</div>

</body>
</html>