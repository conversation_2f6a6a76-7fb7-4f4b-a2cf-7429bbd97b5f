<?php
// Include database configuration
$conn = require_once '../config/database.php';
require_once '../includes/functions.php';

// Check if the user is already logged in and is an admin
ensure_session_started();
if (!isset($_SESSION["user_id"]) || !isset($_SESSION["role"]) || $_SESSION["role"] !== "admin") {
    header("location: unauthorized.php");
    exit;
}

// Handle AJAX request to check email uniqueness
if (isset($_POST['check_email']) && $_POST['check_email'] === 'true' && isset($_POST['email'])) {
    $email = trim($_POST['email']);
    $exists = false;
    
    // Check if email already exists in the database
    $sql = "SELECT id FROM employees WHERE email = ?";
    if ($stmt = mysqli_prepare($conn, $sql)) {
        mysqli_stmt_bind_param($stmt, "s", $email);
        mysqli_stmt_execute($stmt);
        mysqli_stmt_store_result($stmt);
        
        if (mysqli_stmt_num_rows($stmt) > 0) {
            $exists = true;
        }
        
        mysqli_stmt_close($stmt);
    }
    
    // Return JSON response
    header('Content-Type: application/json');
    echo json_encode(['exists' => $exists]);
    exit;
}
// Handle AJAX request to check username uniqueness
if (isset($_POST['check_username']) && $_POST['check_username'] === 'true' && isset($_POST['username'])) {
    $username = trim($_POST['username']);
    $exists = false;
    
    // Check if username already exists in the database
    $sql = "SELECT id FROM employees WHERE username = ?";
    if ($stmt = mysqli_prepare($conn, $sql)) {
        mysqli_stmt_bind_param($stmt, "s", $username);
        mysqli_stmt_execute($stmt);
        mysqli_stmt_store_result($stmt);
        
        if (mysqli_stmt_num_rows($stmt) > 0) {
            $exists = true;
        }
        
        mysqli_stmt_close($stmt);
    }
    
    // Return JSON response
    header('Content-Type: application/json');
    echo json_encode(['exists' => $exists]);
    exit;
}

// Define variables and initialize with empty values
$username = $password = $confirm_password = $email = $role = $first_name = $last_name = "";
$username_err = $password_err = $confirm_password_err = $email_err = $role_err = $first_name_err = $last_name_err = "";

// Processing form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate username
    if (empty(trim($_POST["username"]))) {
        $username_err = "Please enter a username.";
    } else {
        $param_username = trim($_POST["username"]);
        
        // Check username format requirements
        $underscore_count = substr_count($param_username, '_');
        $number_count = preg_match_all('/\d/', $param_username);
        
        if ($underscore_count < 1) {
            $username_err = "Username must contain at least one underscore (_).";
        } elseif ($number_count < 2) {
            $username_err = "Username must contain at least 2 numbers.";
        } else {
            // Check if username already exists
            $sql = "SELECT id FROM employees WHERE username = ?";
            
            if ($stmt = mysqli_prepare($conn, $sql)) {
                // Bind variables to the prepared statement as parameters
                mysqli_stmt_bind_param($stmt, "s", $param_username);
                
                // Attempt to execute the prepared statement
                if (mysqli_stmt_execute($stmt)) {
                    // Store result
                    mysqli_stmt_store_result($stmt);
                    
                    if (mysqli_stmt_num_rows($stmt) == 1) {
                        $username_err = "This username is already taken.";
                    } else {
                        $username = $param_username;
                    }
                } else {
                    echo "Oops! Something went wrong. Please try again later.";
                }

                // Close statement
                mysqli_stmt_close($stmt);
            }
        }
    }
    
    // Basic email validation with uniqueness check
    if (empty(trim($_POST["email"]))) {
        $email_err = "Please enter an email.";
    } else {
        $email = trim($_POST["email"]);
        
        // Check if email is already in use
        $sql = "SELECT id FROM employees WHERE email = ?";
        
        if ($stmt = mysqli_prepare($conn, $sql)) {
            mysqli_stmt_bind_param($stmt, "s", $email);
            mysqli_stmt_execute($stmt);
            mysqli_stmt_store_result($stmt);
            
            if (mysqli_stmt_num_rows($stmt) > 0) {
                $email_err = "This email already exists. Please use a different email.";
            }
            
            mysqli_stmt_close($stmt);
        }
    }
    
    // Validate password
    if (empty(trim($_POST["password"]))) {
        $password_err = "Please enter a password.";     
    } else {
        $password_result = sanitize_and_validate(trim($_POST["password"]), 'password');
        if (!$password_result['valid']) {
            $password_err = "Password must be at least 6 characters with at least one uppercase letter, one lowercase letter, one number, and one special character. No 4 or more consecutive digits allowed.";
        } else {
            $password = $password_result['value'];
        }
    }
    
    // Validate confirm password
    if (empty(trim($_POST["confirm_password"]))) {
        $confirm_password_err = "Please confirm password.";     
    } else {
        $confirm_password = trim($_POST["confirm_password"]);
        if (empty($password_err) && ($password != $confirm_password)) {
            $confirm_password_err = "Password did not match.";
        }
    }
    
    // Validate role
    if (empty(trim($_POST["role"]))) {
        $role_err = "Please select a role.";
    } else {
        $selected_role = trim($_POST["role"]);
        
        // Check if trying to create an admin and if an admin already exists
        if ($selected_role === "admin") {
            $admin_check_sql = "SELECT COUNT(*) as admin_count FROM employees WHERE role = 'admin'";
            $admin_result = mysqli_query($conn, $admin_check_sql);
            $admin_count = mysqli_fetch_assoc($admin_result)['admin_count'];
            
            if ($admin_count > 0) {
                $role_err = "Only one admin account is allowed in the system.";
            } else {
                $role = $selected_role;
            }
        } else {
            $role = $selected_role;
        }
    }
    
    // Validate first name
    if (empty(trim($_POST["first_name"]))) {
        $first_name_err = "Please enter first name.";
    } else {
        $first_name_result = sanitize_and_validate(trim($_POST["first_name"]), 'name');
        if (!$first_name_result['valid']) {
            $first_name_err = "First name should contain only letters and spaces.";
        } else {
            $first_name = $first_name_result['value'];
        }
    }
    
    // Validate last name
    if (empty(trim($_POST["last_name"]))) {
        $last_name_err = "Please enter last name.";
    } else {
        $last_name_result = sanitize_and_validate(trim($_POST["last_name"]), 'name');
        if (!$last_name_result['valid']) {
            $last_name_err = "Last name should contain only letters and spaces.";
        } else {
            $last_name = $last_name_result['value'];
        }
    }
    
    // Check input errors before inserting in database
    if (empty($username_err) && empty($password_err) && empty($confirm_password_err) && empty($email_err) && empty($role_err) && empty($first_name_err) && empty($last_name_err)) {
        
        // Prepare an insert statement
        $sql = "INSERT INTO employees (username, password, email, role, first_name, last_name) VALUES (?, ?, ?, ?, ?, ?)";
         
        if ($stmt = mysqli_prepare($conn, $sql)) {
            // Bind variables to the prepared statement as parameters
            mysqli_stmt_bind_param($stmt, "ssssss", $param_username, $param_password, $param_email, $param_role, $param_first_name, $param_last_name);
            
            // Set parameters
            $param_username = $username;
            $param_password = password_hash($password, PASSWORD_DEFAULT); // Creates a password hash
            $param_email = $email;
            $param_role = $role;
            $param_first_name = $first_name;
            $param_last_name = $last_name;
            
            // Attempt to execute the prepared statement
            if (mysqli_stmt_execute($stmt)) {
                // Redirect to employee management page
                header("location: ../employees.php");
                exit();
            } else {
                echo "Oops! Something went wrong. Please try again later.";
            }

            // Close statement
            mysqli_stmt_close($stmt);
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="../img/minilogo.jpg" type="image/x-icon">
    <title>Register Employee - CRM System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .text-color{ color: #FF6500; }
        .button-hover { transition: background-color 0.3s ease, transform 0.2s ease; }
        .button-hover:hover { background-color: #e55b00; transform: scale(1.05); }
    </style>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const passwordInput = document.getElementById('password');
            const strengthMeter = document.getElementById('password-strength-meter');
            const strengthText = document.getElementById('password-strength-text');
            const emailInput = document.getElementById('email');
            
            // Email uniqueness check
            if (emailInput) {
                function checkEmailUniqueness(email) {
                    const formData = new FormData();
                    formData.append('email', email);
                    formData.append('check_email', 'true');
                    
                    return fetch(window.location.href, {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => data.exists);
                }
                
                function updateEmailError(message) {
                    const errorSpan = emailInput.parentNode.querySelector('.text-red-500');
                    if (errorSpan) {
                        errorSpan.textContent = message;
                    }
                    
                    if (message) {
                        emailInput.classList.add('border-red-500');
                        emailInput.setCustomValidity(message);
                    } else {
                        emailInput.classList.remove('border-red-500');
                        emailInput.setCustomValidity('');
                    }
                }
                
                let emailDebounceTimer;
                emailInput.addEventListener('input', function() {
                    clearTimeout(emailDebounceTimer);
                    const email = this.value.trim();
                    
                    // Clear previous errors immediately
                    updateEmailError('');
                    
                    if (email === '' || !this.checkValidity()) {
                        return;
                    }
                    
                    // Check uniqueness after a delay
                    emailDebounceTimer = setTimeout(() => {
                        checkEmailUniqueness(email)
                            .then(exists => {
                                if (exists) {
                                    updateEmailError('This email already exists. Please use a different email.');
                                }
                            })
                            .catch(error => {
                                console.error('Error checking email uniqueness:', error);
                            });
                    }, 500);
                });
                
                emailInput.addEventListener('blur', function() {
                    const email = this.value.trim();
                    if (email === '' || !this.checkValidity()) return;
                    
                    checkEmailUniqueness(email)
                        .then(exists => {
                            if (exists) {
                                updateEmailError('This email already exists. Please use a different email.');
                            }
                        })
                        .catch(error => {
                            console.error('Error checking email uniqueness:', error);
                        });
                });
            }
            
            // Password strength meter
            if (passwordInput && strengthMeter && strengthText) {
                passwordInput.addEventListener('input', function() {
                    const password = passwordInput.value;
                    const strength = checkPasswordStrength(password);
                    
                    // Update the strength meter
                    let meterWidth = (strength.score / 4) * 100;
                    strengthMeter.style.width = meterWidth + '%';
                    
                    // Update color based on strength
                    let color;
                    switch(strength.score) {
                        case 0: color = '#EF4444'; break; // red-500
                        case 1: color = '#F59E0B'; break; // amber-500
                        case 2: color = '#F59E0B'; break; // amber-500
                        case 3: color = '#10B981'; break; // emerald-500
                        case 4: color = '#059669'; break; // emerald-600
                        default: color = '#6B7280'; // gray-500
                    }
                    strengthMeter.style.backgroundColor = color;
                    
                    // Update text
                    strengthText.textContent = strength.feedback;
                    strengthText.style.color = color;
                });
            }
            
            // Function to check password strength
            function checkPasswordStrength(password) {
                let score = 0;
                let feedback = '';
                
                // Check for 4 or more consecutive digits
                if (/\d{4,}/.test(password)) {
                    return { score: 0, feedback: 'No 4+ consecutive digits allowed' };
                }
                
                // Length check
                if (password.length < 6) {
                    return { score: 0, feedback: 'Very weak' };
                } else if (password.length >= 10) {
                    score += 1;
                }
                
                // Complexity checks
                if (/[a-z]/.test(password)) score += 0.5;
                if (/[A-Z]/.test(password)) score += 0.5;
                if (/\d/.test(password)) score += 1;
                if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) score += 1;
                
                // Variety check
                const uniqueChars = new Set(password.split('')).size;
                if (uniqueChars > 7) score += 1;
                
                // Round the score and cap it at 4
                score = Math.min(4, Math.round(score));
                
                // Generate feedback based on score
                switch (score) {
                    case 0:
                        feedback = 'Very weak';
                        break;
                    case 1:
                        feedback = 'Weak';
                        break;
                    case 2:
                        feedback = 'Fair';
                        break;
                    case 3:
                        feedback = 'Good';
                        break;
                    case 4:
                        feedback = 'Strong';
                        break;
                }
                
                return { score, feedback };
            }
        });
    </script>
</head>
<body class="bg-gray-100 min-h-screen">
    <?php include '../includes/navigation.php'; ?>
    <!-- Main Content -->
    <div class="ml-56 md:ml-60 min-h-screen flex flex-col">
        <header class="bg-[#1E3E62] p-4 flex justify-between z-10 flex-col">
            <h1 class="text-2xl font-bold text-white mx-10 my-2">Register New Employee</h1>
                    </header>
        <main class="flex-1 p-2">
            <div class="bg-white shadow-md rounded-lg p-6 max-w-2xl mx-auto border border-gray-200">
                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="username" class="block text-sm font-medium text-gray-700">Username <span class="text-red-500">*</span></label>
                            <input type="text" name="username" id="username" class="mt-1 block w-full rounded-sm border border-gray-300 p-1 shadow-sm focus:border-[#FF6500]  focus:ring-opacity-50 <?php echo (!empty($username_err)) ? 'border-red-500' : ''; ?>" value="<?php echo $username; ?>" required>
                            <span class="text-red-500 text-xs"><?php echo $username_err; ?></span>
                            <p class="text-xs text-gray-500 mt-1">Username must contain at least one underscore (_) and at least 2 numbers</p>
                        </div>
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700">Email <span class="text-red-500">*</span></label>
                            <input type="email" name="email" id="email" class="mt-1 block w-full rounded-sm border border-gray-300 p-1 shadow-sm focus:border-[#FF6500]  focus:ring-opacity-50 <?php echo (!empty($email_err)) ? 'border-red-500' : ''; ?>" value="<?php echo $email; ?>" required>
                            <span class="text-red-500 text-xs"><?php echo $email_err; ?></span>
                            <p class="text-xs text-gray-500 mt-1"></p>
                        </div>
                        <div>
                            <label for="first_name" class="block text-sm font-medium text-gray-700">First Name <span class="text-red-500">*</span></label>
                            <input type="text" name="first_name" id="first_name" class="mt-1 block w-full rounded-sm border border-gray-300 p-1 shadow-sm focus:border-[#FF6500]  focus:ring-opacity-50 <?php echo (!empty($first_name_err)) ? 'border-red-500' : ''; ?>" value="<?php echo $first_name; ?>" required>
                            <span class="text-red-500 text-xs"><?php echo $first_name_err; ?></span>
                        </div>
                        <div>
                            <label for="last_name" class="block text-sm font-medium text-gray-700">Last Name <span class="text-red-500">*</span></label>
                            <input type="text" name="last_name" id="last_name" class="mt-1 block w-full rounded-sm border border-gray-300 p-1 shadow-sm focus:border-[#FF6500]  focus:ring-opacity-50 <?php echo (!empty($last_name_err)) ? 'border-red-500' : ''; ?>" value="<?php echo $last_name; ?>" required>
                            <span class="text-red-500 text-xs"><?php echo $last_name_err; ?></span>
                        </div>
                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700">Password <span class="text-red-500">*</span></label>
                            <input type="password" name="password" id="password" class="mt-1 block w-full rounded-sm border border-gray-300 p-1 shadow-sm focus:border-[#FF6500]  focus:ring-opacity-50 <?php echo (!empty($password_err)) ? 'border-red-500' : ''; ?>" value="<?php echo $password; ?>" required>
                            <span class="text-red-500 text-xs"><?php echo $password_err; ?></span>
                            
                            <!-- Password strength meter -->
                            <div class="mt-2">
                                <div class="flex items-center">
                                    <div class="w-full bg-gray-200 rounded-full h-2.5 mr-2">
                                        <div class="bg-gray-500 h-2.5 rounded-full" id="password-strength-meter" style="width: 0%"></div>
                                    </div>
                                    <span class="text-xs text-gray-500" id="password-strength-text">Password strength</span>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">Password must be 6+ characters with upper & lowercase letters, a number, a special character, and no 4+ consecutive digits!</p>
                            </div>
                        </div>
                        <div>
                            <label for="confirm_password" class="block text-sm font-medium text-gray-700">Confirm Password <span class="text-red-500">*</span></label>
                            <input type="password" name="confirm_password" id="confirm_password" class="mt-1 block w-full rounded-sm border border-gray-300 p-1 <?php echo (!empty($confirm_password_err)) ? 'border-red-500' : ''; ?>" value="<?php echo $confirm_password; ?>" required>
                            <span class="text-red-500 text-xs"><?php echo $confirm_password_err; ?></span>
                        </div>
                        <div>
                            <label for="role" class="block text-sm font-medium text-gray-700">Role <span class="text-red-500">*</span></label>
                            <?php
                            // Check if an admin already exists
                            $admin_check_sql = "SELECT COUNT(*) as admin_count FROM employees WHERE role = 'admin'";
                            $admin_result = mysqli_query($conn, $admin_check_sql);
                            $admin_exists = mysqli_fetch_assoc($admin_result)['admin_count'] > 0;
                            ?>
                            <select name="role" id="role" class="mt-1 block w-full rounded-sm border border-gray-300 p-1 shadow-sm<?php echo (!empty($role_err)) ? 'border-red-500' : ''; ?>" required>
                                <option value="">Select Role</option>
                                <?php if ($admin_exists): ?>
                                <option value="admin" disabled title="Only one admin account is allowed">Admin (Limit Reached)</option>
                                <?php else: ?>
                                <option value="admin" <?php echo ($role == "admin") ? "selected" : ""; ?>>Admin</option>
                                <?php endif; ?>
                                <option value="sales_rep" <?php echo ($role == "sales_rep") ? "selected" : ""; ?>>Sales Representative</option>
                                <option value="manager" <?php echo ($role == "manager") ? "selected" : ""; ?>>Manager</option>
                            </select>
                            <span class="text-red-500 text-xs"><?php echo $role_err; ?></span>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-4">
                        <a href="../employees.php" class="px-4 py-2 bg-[#0b192c] text-white rounded-sm hover:bg-[#1e3e62]">Cancel</a>
                        <button type="submit" class="px-4 py-2 bg-[#0b192c] text-white rounded-sm hover:bg-[#1e3e62]">Create Employee</button>
                    </div>
                </form>
            </div>
        </main>
    </div>
</body>
</html>
