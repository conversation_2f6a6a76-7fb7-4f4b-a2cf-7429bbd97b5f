<?php
$conn = require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/phpmailer_config.php';

$check_sql = "SHOW COLUMNS FROM employees LIKE 'reset_token'";
$result = mysqli_query($conn, $check_sql);

if (mysqli_num_rows($result) == 0) {
    $alter_sql = "ALTER TABLE employees
                  ADD COLUMN reset_token VARCHAR(255) DEFAULT NULL,
                  ADD COLUMN reset_token_expiry DATETIME DEFAULT NULL";

    mysqli_query($conn, $alter_sql);
}

$email = "";
$email_err = $success_msg = "";

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Check if email is empty
    if (empty(trim($_POST["email"]))) {
        $email_err = "Please enter your email address.";
    } else {
        $email = trim($_POST["email"]);
        
        // Email validation has been removed as requested
        {
            // Check if email exists in the database
        $sql = "SELECT id, username, first_name, last_name FROM employees WHERE email = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "s", $email);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        
        if (mysqli_num_rows($result) == 1) {
            $user = mysqli_fetch_assoc($result);
            
            $token = bin2hex(random_bytes(32));
            $token_hash = password_hash($token, PASSWORD_DEFAULT);
            
            $expiry = date('Y-m-d H:i:s', strtotime('+24 hours'));
            
            $update_sql = "UPDATE employees SET reset_token = ?, reset_token_expiry = ? WHERE id = ?";
            $update_stmt = mysqli_prepare($conn, $update_sql);
            mysqli_stmt_bind_param($update_stmt, "ssi", $token_hash, $expiry, $user['id']);
            
            if (mysqli_stmt_execute($update_stmt)) {
                $reset_link = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) . "/reset_password.php?token=" . $token . "&email=" . urlencode($email);
                
                // Prepare email content
                $subject = "Password Reset Request - Lead Management System";
                $name = !empty($user['first_name']) ? $user['first_name'] : $user['username'];
                
                $body = '
                <html>
                <head>
                    <title>Set New Password</title>
                </head>
                <body>
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
                        <h2 style="color: #FF6500;">Set New Password</h2>
                        <p>Hello ' . htmlspecialchars($name) . ',</p>
                        <p>We received a request to reset your password for your Lead Management System account. Click the button below to set a new password:</p>
                        <p style="text-align: center; margin: 30px 0;">
                            <a href="' . $reset_link . '" style="background-color: #FF6500; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block; font-weight: bold;">Set New Password</a>
                        </p>
                        <p><strong>Important:</strong> You must use this link to reset your password. The system will not allow you to reset your password directly without using this link.</p>
                        <p>If you did not request a password reset, please ignore this email or contact support if you have concerns.</p>
                        <p>This link is only valid for the next 24 hours.</p>
                        <p>If the button above doesn\'t work, copy and paste the following URL into your browser:</p>
                        <p style="word-break: break-all; background-color: #f5f5f5; padding: 10px; border-radius: 4px; font-size: 14px;">' . $reset_link . '</p>
                        <p style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 12px; color: #777;">
                            This is an automated message, please do not reply.
                        </p>
                    </div>
                </body>
                </html>';
                
                // Send email
                $email_result = send_email($email, $subject, $body);
                
                if ($email_result['success']) {
                    $success_msg = "A password reset link has been sent to your email address. Please check your inbox and follow the instructions to create a new password. <strong>You must use the link in the email to reset your password.</strong>";
                } else {
                    // Email sending failed, but token was created
                    $success_msg = "A password reset link has been generated, but we couldn't send the email. Please contact support.";
                    
                    // Log the error
                    error_log("Failed to send password reset email to {$email}: " . $email_result['message']);
                }
            } else {
                $email_err = "Oops! Something went wrong. Please try again later.";
            }
            
            mysqli_stmt_close($update_stmt);
        } else {
            $email_err = "User with this email address does not exist in our system.";
        }
        
        mysqli_stmt_close($stmt);
    }
  }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forgot Password - Lead Management System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .text-color{ color: #FF6500; }
        .button-hover { transition: background-color 0.3s ease, transform 0.2s ease; }
        .button-hover:hover { background-color: #e55b00; transform: scale(1.05); }
    </style>
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center">
    <div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-md">
        <div class="text-center mb-8">
            <img src="../img/logo.png" alt="Logo" class="w-40 mx-auto rounded-lg">
            <h2 class="text-2xl font-bold mt-4 text-[#0b192C]">Forgot Password</h2>
            <p class="text-gray-600 mt-2">Enter your email address to reset your password</p>
        </div>

        <?php if (!empty($success_msg)) : ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-5 rounded relative mb-4 overflow-hidden" role="alert">
                <div class="block break-words text-center">
                    <div class="mb-3">
                        <i class="fas fa-envelope-open-text text-3xl text-green-600"></i>
                    </div>
                    <h3 class="font-bold text-lg mb-2">Email Sent!</h3>
                    <?php echo $success_msg; ?>
                </div>
            </div>
        <?php endif; ?>

        <?php if (!empty($email_err)) : ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                <strong class="font-bold">Error! </strong>
                <span class="block sm:inline"><?php echo $email_err; ?></span>
            </div>
        <?php endif; ?>

        <?php if (empty($success_msg)) : ?>
            <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                <div class="mb-6">
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address <span class="text-red-500">*</span></label>
                    <input type="email" name="email" id="email" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6500] <?php echo (!empty($email_err)) ? 'border-red-500' : ''; ?>" value="<?php echo $email; ?>" required>
                    <?php if (!empty($email_err) && strpos($email_err, "User with this email") === false) : ?>
                        <span class="text-red-500 text-xs"><?php echo $email_err; ?></span>
                    <?php endif; ?>
                </div>
                <button type="submit" class="w-full bg-[#0b192c] text-white py-2 px-4 rounded-sm hover:bg-[#1e3e62]">
                    Reset Password
                </button>
            </form>
        <?php endif; ?>
        
        <!-- Back to Login link shown in all cases -->
        <div class="mt-6 text-center">
            <a href="login.php" class="text-sm text-[#FF6500] hover:underline">Back to Login</a>
        </div>
    </div>
</body>
</html>
