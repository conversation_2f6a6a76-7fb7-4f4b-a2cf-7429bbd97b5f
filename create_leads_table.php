<?php
// Create separate leads table for inquiries
$conn = require_once 'get_db_connection.php';

$sql = "CREATE TABLE IF NOT EXISTS leads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    first_name VA<PERSON><PERSON><PERSON>(50) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    country_code VARCHAR(10) DEFAULT '+91',
    company VARCHAR(100),
    customer_interest TEXT,
    source VARCHAR(50) DEFAULT 'Web Form',
    status ENUM('new', 'contacted', 'qualified', 'converted', 'lost') DEFAULT 'new',
    notes TEXT,
    
    -- Assignment and tracking
    assigned_to INT,
    created_by INT,
    converted_to_contact_id INT NULL, -- Reference to contacts table if converted
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    FOREIGN KEY (assigned_to) REFERENCES employees(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES employees(id) ON DELETE SET NULL,
    FOREIGN KEY (converted_to_contact_id) REFERENCES contacts(id) ON DELETE SET NULL,
    
    -- Indexes for better performance
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_status (status),
    INDEX idx_assigned_to (assigned_to),
    INDEX idx_created_by (created_by),
    INDEX idx_first_name (first_name),
    INDEX idx_last_name (last_name)
)";

if (mysqli_query($conn, $sql)) {
    echo "Leads table created successfully!<br>";
    
    // Also create a notifications table update for leads
    $update_notifications_sql = "ALTER TABLE notifications 
                                ADD COLUMN IF NOT EXISTS related_to ENUM('contacts', 'leads', 'sales') DEFAULT 'contacts',
                                ADD COLUMN IF NOT EXISTS related_id INT DEFAULT NULL";
    
    if (mysqli_query($conn, $update_notifications_sql)) {
        echo "Notifications table updated successfully!<br>";
    } else {
        echo "Error updating notifications table: " . mysqli_error($conn) . "<br>";
    }
    
} else {
    echo "Error creating leads table: " . mysqli_error($conn) . "<br>";
}

mysqli_close($conn);
?>

<!DOCTYPE html>
<html>
<head>
    <title>Database Setup</title>
</head>
<body>
    <h2>Database Setup Complete</h2>
    <p><a href="lead_form.php">Go to Lead Form</a></p>
    <p><a href="leads.php">Go to Leads Management</a></p>
</body>
</html>