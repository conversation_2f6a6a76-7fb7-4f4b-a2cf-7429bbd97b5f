<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>LMS - About us</title>
  <link rel="icon" type="image/png" href="/images/logo.title.png">
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet" />
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: "#4A77A8",
            secondary: "#F7A041"
          },
          borderRadius: {
            none: "0px",
            sm: "4px",
            DEFAULT: "8px",
            md: "12px",
            lg: "16px",
            xl: "20px",
            "2xl": "24px",
            "3xl": "32px",
            full: "9999px",
            button: "8px",
          },
        },
      },
    };
  </script>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet" />
  <link
    href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Open+Sans:wght@400;500;600&display=swap"
    rel="stylesheet" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" />
  <!-- AOS CSS -->
  <link href="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.css" rel="stylesheet">
  <style>
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-20px);
            }
        }
        
        .animate-fadeInUp {
            animation: fadeInUp 0.6s ease-out forwards;
        }
        
        .animate-float {
            animation: float 6s ease-in-out infinite;
        }
        
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
   
        
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
        
        /* Mega Menu Styles */
        .mega-menu {
            position: fixed;
            left: 0;
            right: 0;
            z-index: 50;
        }
        
        .mega-menu-item:hover .mega-menu {
            opacity: 1;
            visibility: visible;
        }
        
        /* Hamburger to X animation for mobile menu button */
        #mobileMenuBtn.open span:nth-child(1) {
          transform: rotate(45deg) translateY(8px);
        }
        #mobileMenuBtn.open span:nth-child(2) {
          opacity: 0;
        }
        #mobileMenuBtn.open span:nth-child(3) {
          transform: rotate(-45deg) translateY(-8px);
        }
    </style>
</head>
<body class="overflow-x-hidden">

  <header class="fixed w-full top-0 z-50">
    <div class="lg:max-w-full mx-auto px-4 sm:px-4 md:px-6 lg:px-8 py-2 sm:py-3 md:py-4">
      <div
        class=" bg-white backdrop-blur-lg rounded-lg sm:rounded-xl lg:rounded-2xl shadow-lg border border-white/20 mx-1 sm:mx-2 md:mx-3 lg:mx-4">
        <div class=" flex justify-between items-center h-12 sm:h-14 md:h-16 px-2 sm:px-4 md:px-6">

          <div>
            <h1
              class="text-lg sm:text-xl md:text-2xl bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent relative">
              <img src="images/logo.png" alt="LMS Logo" class="lg:w-32  w-20 object-contain">
            </h1>
          </div>

          <!-- Desktop Nav -->
          <nav class="hidden lg:flex justify-between items-center">
            <!-- logo -->

            
            <div class="flex  ">
              <a href="index.html"
                class="flex nav-link px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-all duration-300">
                Home
              </a>
              <a href="About.html"
                class="flex px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-all duration-300">
                About
              </a>
              <a href="products.html"
                class=" flex px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-all duration-300">
                Products
              </a>

              <!-- Features Dropdown -->
              <div class="relative">
                <button id="featuresDropdownBtn"
                  class="px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-all duration-300 flex items-center">
                  Features
                  <i class="ri-arrow-down-s-line ml-1 transition-transform duration-200"></i>
                </button>
                <div id="featuresDropdownMenu" class="absolute top-full left-0 mt-2 w-64 bg-white rounded-xl shadow-2xl border border-gray-100 py-2 z-50 hidden">
                  <a href="leadmanagement.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-brain-line text-blue-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Lead Management</div>
                      <div class="text-xs text-gray-500">Smart lead prioritization</div>
                    </div>
                  </a>
                  <a href="salemanagement.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-settings-3-line text-green-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Sales Management</div>
                      <div class="text-xs text-gray-500">Streamline your process</div>
                    </div>
                  </a>
                  <a href="analytics.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-pie-chart-line text-purple-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Advanced Analytics</div>
                      <div class="text-xs text-gray-500">Detailed insights & reports</div>
                    </div>
                  </a>
                  <a href="taskmanager.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-smartphone-line text-orange-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Task Manager</div>
                      <div class="text-xs text-gray-500">Capture from anywhere</div>
                    </div>
                  </a>
                  <a href="customersupport.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-team-line text-indigo-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Customer Support</div>
                      <div class="text-xs text-gray-500">Work together effectively</div>
                    </div>
                  </a>
                  <a href="integration.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-teal-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-shield-check-line text-teal-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Integration</div>
                      <div class="text-xs text-gray-500">Connect seamlessly with tools</div>
                    </div>
                  </a>
                </div>
              </div>

            </div>
            <!-- login -->
            <div class="flex ">
              <a href="contact.html"
                class=" flex items-center font-bold px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black  transition-all duration-300">
                Contact
              </a>

              <a href="/LEADManagement/auth/login.php"
                class=" flex items-center bg-orange-500 rounded-3xl font-bold px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-white  transition-all duration-300">
                Login
              </a>
            </div>

          </nav>

          <!-- Mobile Menu Button -->
          <div class="lg:hidden z-50">
            <button id="mobileMenuBtn" class="focus:outline-none relative w-6 sm:w-7 md:w-8 h-6 sm:h-7 md:h-8">
              <span class="absolute top-1 left-0 w-6 sm:w-7 md:w-8 h-0.5 bg-black transition-transform duration-300"></span>
              <span class="absolute top-3 left-0 w-6 sm:w-7 md:w-8 h-0.5 bg-black transition-opacity duration-300"></span>
              <span class="absolute top-5 left-0 w-6 sm:w-7 md:w-8 h-0.5 bg-black transition-transform duration-300"></span>
            </button>
          </div>
        </div>

        <!-- Mobile Nav Menu -->
        <div id="mobileMenu" class="lg:hidden px-3 sm:px-4 md:px-6 pb-4 sm:pb-5 md:pb-6 pt-2 hidden">
          <div class="flex flex-col space-y-2 sm:space-y-3 md:space-y-4">
            <a href="index.html"
              class="text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200">
              Home
            </a>
            <a href="About.html"
              class="text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200">
              About
            </a>
            <a href="products.html"
              class="text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200">
              Products
            </a>
            <a href="contact.html"
              class="text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200">
              Contact
            </a>

            <!-- Mobile Features Section -->
            <div>
              <button id="mobileFeaturesBtn"
                class="w-full text-left text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200 flex items-center justify-between">
                <span>Features</span>
                <i class="ri-arrow-down-s-line transition-transform duration-200"></i>
              </button>

              <div id="mobileFeaturesMenu" class="ml-4 mt-2 space-y-2 hidden">
                <a href="leadmanagement.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-brain-line mr-2"></i>Lead Management
                </a>
                <a href="salemanagement.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-settings-3-line mr-2"></i>Email
                  Marketing
                </a>
                <a href="analytics.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-pie-chart-line mr-2"></i>Advanced Analytics
                </a>
                <a href="taskmanager.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-smartphone-line mr-2"></i>Task Manager
                </a>
                <a href="customersupport.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-team-line mr-2"></i>Customer Support
                </a>
                <a href="integration.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-shield-check-line mr-2"></i>Integration
                </a>
              </div>
            </div>

            <a href="/LEADManagement/auth/login.php"
              class="bg-[#FF6500] text-white px-4 sm:px-5 md:px-6 py-1.5 sm:py-2 md:py-2.5 text-sm sm:text-base md:text-lg rounded-full font-bold text-center mt-2 hover:bg-[#e55a00] transition-colors duration-200">
              Login
            </a>
          </div>
        </div>
      </div>
    </div>
  </header>


   <!-- Hero Section: About Us -->
<section class="px-4 sm:px-6 md:px-10 py-16 sm:py-20 mt-16 relative overflow-hidden bg-gradient-to-br from-orange-50 via-blue-50 to-white">
  <div class="text-center w-auto mx-auto space-y-10">
    <div class="animate-fadeInUp">
      <h1 class="text-5xl sm:text-6xl md:text-7xl font-extrabold font-serif bg-gradient-to-r from-orange-500 to-blue-600 bg-clip-text text-transparent mb-4">
        About LMS
      </h1>
      <p class="text-lg sm:text-2xl md:text-3xl text-gray-700 font-medium mt-6 px-2 max-w-3xl mx-auto">
        LMS (Lead Management System) is a next-generation platform designed to help businesses capture, nurture, and convert leads into loyal customers. Our mission is to empower sales teams with smart automation, real-time analytics, and seamless collaboration tools—all in one intuitive dashboard.
      </p>
      <div class="flex flex-wrap justify-center gap-4 mt-8">
        <span class="inline-block bg-white/80 text-blue-700 font-semibold px-4 py-2 rounded-full shadow">Smart Automation</span>
        <span class="inline-block bg-white/80 text-orange-600 font-semibold px-4 py-2 rounded-full shadow">Real-Time Insights</span>
        <span class="inline-block bg-white/80 text-purple-700 font-semibold px-4 py-2 rounded-full shadow">Seamless Collaboration</span>
        <span class="inline-block bg-white/80 text-green-700 font-semibold px-4 py-2 rounded-full shadow">Unified Integrations</span>
      </div>
    </div>
    <div class="absolute inset-0 pointer-events-none">
      <div class="absolute -top-20 left-1/4 w-32 h-32 bg-blue-700 rounded-full opacity-10 animate-float"></div>
      <div class="absolute -top-40 right-1/4 w-24 h-24 bg-orange-500 rounded-full opacity-50 animate-float" style="animation-delay: -2s;"></div>
      <div class="absolute top-10 left-[16%] w-16 h-16 bg-blue-900 rounded-full opacity-25 animate-float" style="animation-delay: -4s;"></div>
    </div>
  </div>
</section>

<!-- Our Mission & Vision -->
<section class="py-16 px-4 sm:px-6 lg:px-12 bg-white">
  <div class="w-auto mx-auto grid grid-cols-1 md:grid-cols-2 gap-12 items-center" data-aos="fade-up">
    <div class="space-y-6" data-aos="fade-up">
      <h2 class="text-3xl sm:text-4xl font-bold text-gray-900 text-transparent bg-gradient-to-r from-orange-500 to-blue-500 bg-clip-text mb-4" data-aos="fade-up">Our Mission</h2>
      <p class="text-lg text-gray-700 font-medium">
        We believe every lead is an opportunity. Our mission is to simplify and supercharge the sales process for businesses of all sizes—helping you close more deals, build stronger relationships, and grow faster with less effort.
      </p>
      <h2 class="text-3xl font-bold text-orange-500 mt-10" data-aos="fade-up">Our Vision</h2>
      <p class="text-lg text-gray-700 font-medium">
        To be the most trusted partner for sales-driven organizations worldwide, delivering innovation, reliability, and measurable results through technology.
      </p>
      <div class="flex flex-wrap gap-6 mt-8" data-aos="fade-up">
        <div class="text-center min-w-[100px]">
          <div class="text-3xl font-bold text-blue-500" id="customers-count">1000</div>
          <div class="text-sm text-gray-500">Happy Customers</div>
        </div>
        <div class="text-center min-w-[100px]" data-aos="fade-up">
          <div class="text-3xl font-bold text-purple-500" id="leads-count">10000+</div>
          <div class="text-sm text-gray-500">Leads Managed</div>
        </div>
        <div class="text-center min-w-[100px]" data-aos="fade-up">
          <div class="text-3xl font-bold text-pink-500" id="conversion-rate">98%</div>
          <div class="text-sm text-gray-500">Avg. Conversion</div>
        </div>
      </div>
    </div>
    <div class="relative w-full flex justify-center">
      <div class="glass-effect rounded-2xl overflow-hidden shadow-lg max-w-lg w-full">
        <img src="images/index.webp" alt="LMS Dashboard" class="w-full h-80 object-cover rounded-2xl" loading="lazy" />
      </div>
    </div>
  </div>
</section>

<!-- Why Choose Us Section -->
<section class="py-16 px-4 sm:px-6 lg:px-12">
  <div class="w-auto mx-auto" data-aos="fade-up">
    <h2 class="text-4xl sm:text-5xl font-extrabold text-center bg-gradient-to-r from-orange-500 to-blue-500 bg-clip-text text-transparent mb-16">Why Choose LMS?</h2>
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8" data-aos="fade-up">
      <div class="bg-white rounded-2xl shadow-lg p-8 flex flex-col items-center text-center hover:-translate-y-2 hover:shadow-2xl transition-all" data-aos="fade-up">
        <span class="text-4xl mb-4 text-blue-500">⚡</span>
        <h3 class="text-xl font-bold mb-2">Lightning Fast Setup</h3>
        <p class="text-gray-600">Get started in minutes, not days. No coding or IT required.</p>
      </div>
      <div class="bg-white rounded-2xl shadow-lg p-8 flex flex-col items-center text-center hover:-translate-y-2 hover:shadow-2xl transition-all" data-aos="fade-up">
        <span class="text-4xl mb-4 text-orange-500">🤖</span>
        <h3 class="text-xl font-bold mb-2">Smart Automation</h3>
        <p class="text-gray-600">Automate follow-ups, reminders, and lead scoring with AI-driven workflows.</p>
      </div>
      <div class="bg-white rounded-2xl shadow-lg p-8 flex flex-col items-center text-center hover:-translate-y-2 hover:shadow-2xl transition-all" data-aos="fade-up">
        <span class="text-4xl mb-4 text-purple-500">📊</span>
        <h3 class="text-xl font-bold mb-2">Real-Time Analytics</h3>
        <p class="text-gray-600">Visualize your pipeline, track KPIs, and make data-driven decisions instantly.</p>
      </div>
      <div class="bg-white rounded-2xl shadow-lg p-8 flex flex-col items-center text-center hover:-translate-y-2 hover:shadow-2xl transition-all" data-aos="fade-up">
        <span class="text-4xl mb-4 text-green-500">🔗</span>
        <h3 class="text-xl font-bold mb-2">Seamless Integrations</h3>
        <p class="text-gray-600">Connect with your favorite tools—email, calendar, CRM, and more.</p>
      </div>
    </div>
  </div>
</section>

<!-- Meet the Team Section -->
<section class="py-20 px-4 sm:px-6 lg:px-10 bg-white">
  <div class="w-auto mx-auto" data-aos="fade-up">
    <div class="text-center mb-16" data-aos="fade-up">
      <h2 class="text-4xl md:text-5xl bg-gradient-to-r from-blue-500 to-orange-500 bg-clip-text text-transparent font-bold mb-6">
        Meet Our Team
      </h2>
      <p class="text-xl text-gray-600 font-medium max-w-3xl mx-auto">
        A passionate, diverse team dedicated to transforming how businesses manage leads and grow relationships.
      </p>
    </div>
    <div class="grid gap-10 sm:grid-cols-2 lg:grid-cols-3" data-aos="fade-up">
      <!-- Team Member 1 -->
      <div class="bg-white rounded-2xl shadow-xl p-8 flex flex-col items-center group hover:-translate-y-2 hover:shadow-2xl transition-all duration-300" data-aos="fade-up">
        <div class="relative mb-4">
          <img src="images/team.jpg" alt="Ava Patel" class="w-28 h-28 rounded-full object-cover border-4 border-blue-200 shadow-lg group-hover:scale-105 transition-transform" />
          <span class="absolute bottom-2 right-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full"></span>
        </div>
        <h3 class="text-xl font-bold mb-1 group-hover:text-blue-500 transition-colors">Ava Patel</h3>
        <p class="text-blue-400 mb-2 font-semibold">CEO & Co-Founder</p>
        <p class="text-gray-600 text-center text-sm mb-4">Visionary leader with 12+ years in SaaS and sales technology. Passionate about empowering teams to achieve more.</p>
        <div class="flex gap-3">
          <a href="#" class="text-gray-400 hover:text-blue-500"><i class="ri-linkedin-box-fill text-xl"></i></a>
          <a href="#" class="text-gray-400 hover:text-blue-500"><i class="ri-twitter-x-line text-xl"></i></a>
        </div>
      </div>
      <!-- Team Member 2 -->
      <div class="bg-white rounded-2xl shadow-xl p-8 flex flex-col items-center group hover:-translate-y-2 hover:shadow-2xl transition-all duration-300" data-aos="fade-up">
        <div class="relative mb-4">
          <img src="images/customer.jpg" alt="Liam Chen" class="w-28 h-28 rounded-full object-cover border-4 border-orange-200 shadow-lg group-hover:scale-105 transition-transform" />
          <span class="absolute bottom-2 right-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full"></span>
        </div>
        <h3 class="text-xl font-bold mb-1 group-hover:text-orange-500 transition-colors">Liam Chen</h3>
        <p class="text-orange-400 mb-2 font-semibold">CTO & Co-Founder</p>
        <p class="text-gray-600 text-center text-sm mb-4">Tech innovator and architect behind our robust, scalable platform. Loves building tools that make work easier.</p>
        <div class="flex gap-3">
          <a href="#" class="text-gray-400 hover:text-orange-500"><i class="ri-linkedin-box-fill text-xl"></i></a>
          <a href="#" class="text-gray-400 hover:text-orange-500"><i class="ri-github-fill text-xl"></i></a>
        </div>
      </div>
      <!-- Team Member 3 -->
      <div class="bg-white rounded-2xl shadow-xl p-8 flex flex-col items-center group hover:-translate-y-2 hover:shadow-2xl transition-all duration-300" data-aos="fade-up">
        <div class="relative mb-4">
          <img src="images/lead.jpg" alt="Priya Singh" class="w-28 h-28 rounded-full object-cover border-4 border-green-200 shadow-lg group-hover:scale-105 transition-transform" />
          <span class="absolute bottom-2 right-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full"></span>
        </div>
        <h3 class="text-xl font-bold mb-1 group-hover:text-green-500 transition-colors">Priya Singh</h3>
        <p class="text-green-400 mb-2 font-semibold">Head of Customer Success</p>
        <p class="text-gray-600 text-center text-sm mb-4">Dedicated to helping clients succeed and maximize value from our LMS. Expert in onboarding and support.</p>
        <div class="flex gap-3">
          <a href="#" class="text-gray-400 hover:text-green-500"><i class="ri-linkedin-box-fill text-xl"></i></a>
          <a href="#" class="text-gray-400 hover:text-green-500"><i class="ri-mail-line text-xl"></i></a>
        </div>
      </div>
      <!-- Team Member 4 -->
      <div class="bg-white rounded-2xl shadow-xl p-8 flex flex-col items-center group hover:-translate-y-2 hover:shadow-2xl transition-all duration-300" data-aos="fade-up">
        <div class="relative mb-4">
          <img src="images/products.jpg" alt="Carlos Rivera" class="w-28 h-28 rounded-full object-cover border-4 border-purple-200 shadow-lg group-hover:scale-105 transition-transform" />
          <span class="absolute bottom-2 right-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full"></span>
        </div>
        <h3 class="text-xl font-bold mb-1 group-hover:text-purple-500 transition-colors">Carlos Rivera</h3>
        <p class="text-purple-400 mb-2 font-semibold">Product Manager</p>
        <p class="text-gray-600 text-center text-sm mb-4">Drives product vision and ensures our features solve real customer problems. Loves user feedback!</p>
        <div class="flex gap-3">
          <a href="#" class="text-gray-400 hover:text-purple-500"><i class="ri-linkedin-box-fill text-xl"></i></a>
          <a href="#" class="text-gray-400 hover:text-purple-500"><i class="ri-github-fill text-xl"></i></a>
        </div>
      </div>
      <!-- Team Member 5 -->
      <div class="bg-white rounded-2xl shadow-xl p-8 flex flex-col items-center group hover:-translate-y-2 hover:shadow-2xl transition-all duration-300" data-aos="fade-up">
        <div class="relative mb-4">
          <img src="images/officemeeting.jpg" alt="Emily Zhang" class="w-28 h-28 rounded-full object-cover border-4 border-yellow-200 shadow-lg group-hover:scale-105 transition-transform" />
          <span class="absolute bottom-2 right-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full"></span>
        </div>
        <h3 class="text-xl font-bold mb-1 group-hover:text-yellow-500 transition-colors">Emily Zhang</h3>
        <p class="text-yellow-400 mb-2 font-semibold">Lead Designer</p>
        <p class="text-gray-600 text-center text-sm mb-4">Creates beautiful, intuitive interfaces that delight users and drive adoption.</p>
        <div class="flex gap-3">
          <a href="#" class="text-gray-400 hover:text-yellow-500"><i class="ri-linkedin-box-fill text-xl"></i></a>
          <a href="#" class="text-gray-400 hover:text-yellow-500"><i class="ri-instagram-line text-xl"></i></a>
        </div>
      </div>
      <!-- Team Member 6 -->
      <div class="bg-white rounded-2xl shadow-xl p-8 flex flex-col items-center group hover:-translate-y-2 hover:shadow-2xl transition-all duration-300" data-aos="fade-up">
        <div class="relative mb-4">
          <img src="images/sales.jpg" alt="David Kim" class="w-28 h-28 rounded-full object-cover border-4 border-pink-200 shadow-lg group-hover:scale-105 transition-transform" />
          <span class="absolute bottom-2 right-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full"></span>
        </div>
        <h3 class="text-xl font-bold mb-1 group-hover:text-pink-500 transition-colors">David Kim</h3>
        <p class="text-pink-400 mb-2 font-semibold">Head of Sales</p>
        <p class="text-gray-600 text-center text-sm mb-4">Sales strategist with a track record of building high-performing teams and exceeding targets.</p>
        <div class="flex gap-3">
          <a href="#" class="text-gray-400 hover:text-pink-500"><i class="ri-linkedin-box-fill text-xl"></i></a>
          <a href="#" class="text-gray-400 hover:text-pink-500"><i class="ri-twitter-x-line text-xl"></i></a>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Our Journey Section (Modern Infographic Style) -->
<section class="py-20 px-0 w-full">
  <div class="max-w-7xl mx-auto" data-aos="fade-up">
    <h2 class="text-4xl md:text-5xl text-center bg-gradient-to-r from-blue-500 to-orange-500 bg-clip-text text-transparent font-bold mb-6">
      Our Journey
    </h2>
    <!-- Horizontal Timeline for Desktop -->
    <div class="relative w-full min-h-[380px] hidden lg:block" data-aos="fade-up">
      <!-- SVG Timeline -->
      <svg viewBox="0 0 900 300" xmlns="http://www.w3.org/2000/svg" class="w-full h-[340px]" aria-hidden="true" preserveAspectRatio="none">
        <!-- Wavy Path (adjusted to dip lower at 03) -->
        <path d="M50 200 Q 200 50, 350 200 Q 500 400, 650 100 Q 800 -50, 850 200" stroke="#3B82F6" stroke-width="12" fill="none" />
        <!-- Milestones -->
        <g>
          <circle cx="70" cy="180" r="18" fill="#fff" stroke="#F59E42" stroke-width="6" />
          <text x="70" y="188" text-anchor="middle" font-size="16" font-family="Inter, sans-serif" font-weight="bold" fill="#F59E42">01</text>
        </g>
        <g>
          <circle cx="290" cy="150" r="18" fill="#fff" stroke="#F59E42" stroke-width="6" />
          <text x="290" y="155" text-anchor="middle" font-size="16" font-family="Inter, sans-serif" font-weight="bold" fill="#F59E42">02</text>
        </g>
        <g>
          <circle cx="450" cy="275" r="18" fill="#fff" stroke="#F59E42" stroke-width="6" />
          <text x="450" y="280" text-anchor="middle" font-size="16" font-family="Inter, sans-serif" font-weight="bold" fill="#F59E42">03</text>
        </g>
        <g>
          <circle cx="640" cy="120" r="18" fill="#fff" stroke="#F59E42" stroke-width="6" />
          <text x="640" y="128" text-anchor="middle" font-size="16" font-family="Inter, sans-serif" font-weight="bold" fill="#F59E42">04</text>
        </g>
        <g>
          <circle cx="840" cy="170" r="18" fill="#fff" stroke="#F59E42" stroke-width="6" />
          <text x="840" y="175" text-anchor="middle" font-size="16" font-family="Inter, sans-serif" font-weight="bold" fill="#F59E42">05</text>
        </g>
        <!-- Rocket Icon (moved further up and right) -->
        <g transform="translate(835,120)">
          <polygon points="0,0 20,30 -20,30" fill="#F43F5E" />
          <rect x="-10" y="-30" width="20" height="40" rx="10" fill="#F59E42" />
          <circle cx="0" cy="-20" r="7" fill="#fff" stroke="#3B82F6" stroke-width="2" />
        </g>
      </svg>
      <!-- Milestone Labels -->
      <div class="absolute inset-0 pointer-events-none text-sm sm:text-base font-medium text-center leading-snug w-full" data-aos="fade-up">
        <!-- 01 -->
        <div class="absolute left-[7.7%] top-[68%] w-[15%] min-w-[120px] -translate-x-1/2">
          <p class="text-gray-800 font-bold">Founded to empower<br>businesses with smart<br>lead management.</p>
        </div>
        <!-- 02 (adjusted higher to avoid overlap) -->
        <div class="absolute left-[33.5%] top-[14.5%] w-[18%] min-w-[120px] -translate-x-1/2">
          <p class="text-gray-800 font-bold">Reached 1,000+<br>customers and launched<br>automation tools.</p>
        </div>
        <!-- 03 (lowered to follow deeper path dip) -->
        <div class="absolute left-[50%] top-[89%] w-[18%] min-w-[120px] -translate-x-1/2">
          <p class="text-gray-800 font-bold">Expanded globally and<br>introduced analytics<br>dashboard.</p>
        </div>
        <!-- 04 -->
        <div class="absolute left-[66.5%] top-[8%] w-[18%] min-w-[120px] -translate-x-1/2">
          <p class="text-gray-800 font-bold">Became industry leader<br>with AI-powered insights.</p>
        </div>
        <!-- 05 -->
        <div class="absolute left-[96.5%] top-[64%] w-[15%] min-w-[120px] -translate-x-1/2">
          <p class="text-gray-800 font-bold">Launched next-gen<br>platform to drive record<br>client growth.</p>
        </div>
      </div>
    </div>
    <!-- Vertical Timeline for Mobile/Tablet -->
    <div class="block lg:hidden" data-aos="fade-up">
      <div class="flex flex-col items-center relative px-2 sm:px-6">
        <!-- Vertical line -->
        <div class="absolute left-1/2 top-0 bottom-0 w-1 bg-blue-200 rounded-full -translate-x-1/2 z-0" style="height: 100%;"></div>
        <!-- Milestone 1 -->
        <div class="flex flex-col items-center w-full mb-8 relative z-10">
          <div class="flex flex-col items-center">
            <div class="flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-white border-4 border-orange-400 font-bold text-orange-500 text-base sm:text-lg shadow">01</div>
            <div class="h-4 sm:h-6 w-1 bg-blue-200"></div>
          </div>
          <div class="w-full mt-2 text-center px-2 sm:px-4">
            <p class="text-gray-800 font-bold text-sm sm:text-base">Founded to empower<br>businesses with smart<br>lead management.</p>
          </div>
        </div>
        <!-- Milestone 2 -->
        <div class="flex flex-col items-center w-full mb-8 relative z-10">
          <div class="flex flex-col items-center">
            <div class="flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-white border-4 border-orange-400 font-bold text-orange-500 text-base sm:text-lg shadow">02</div>
            <div class="h-4 sm:h-6 w-1 bg-blue-200"></div>
          </div>
          <div class="w-full mt-2 text-center px-2 sm:px-4">
            <p class="text-gray-800 font-bold text-sm sm:text-base">Reached 1,000+<br>customers and launched<br>automation tools.</p>
          </div>
        </div>
        <!-- Milestone 3 -->
        <div class="flex flex-col items-center w-full mb-8 relative z-10">
          <div class="flex flex-col items-center">
            <div class="flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-white border-4 border-orange-400 font-bold text-orange-500 text-base sm:text-lg shadow">03</div>
            <div class="h-4 sm:h-6 w-1 bg-blue-200"></div>
          </div>
          <div class="w-full mt-2 text-center px-2 sm:px-4">
            <p class="text-gray-800 font-bold text-sm sm:text-base">Expanded globally and<br>introduced analytics<br>dashboard.</p>
          </div>
        </div>
        <!-- Milestone 4 -->
        <div class="flex flex-col items-center w-full mb-8 relative z-10">
          <div class="flex flex-col items-center">
            <div class="flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-white border-4 border-orange-400 font-bold text-orange-500 text-base sm:text-lg shadow">04</div>
            <div class="h-4 sm:h-6 w-1 bg-blue-200"></div>
          </div>
          <div class="w-full mt-2 text-center px-2 sm:px-4">
            <p class="text-gray-800 font-bold text-sm sm:text-base">Became industry leader<br>with AI-powered insights.</p>
          </div>
        </div>
        <!-- Milestone 5 -->
        <div class="flex flex-col items-center w-full relative z-10">
          <div class="flex flex-col items-center">
            <div class="flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-white border-4 border-orange-400 font-bold text-orange-500 text-base sm:text-lg shadow">05</div>
            <!-- Rocket icon -->
            <svg class="w-7 h-7 sm:w-8 sm:h-8 mt-2" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
              <polygon points="20,32 32,40 8,40" fill="#F43F5E" />
              <rect x="10" y="0" width="20" height="30" rx="10" fill="#F59E42" />
              <circle cx="20" cy="10" r="7" fill="#fff" stroke="#3B82F6" stroke-width="2" />
            </svg>
          </div>
          <div class="w-full mt-2 text-center px-2 sm:px-4">
            <p class="text-gray-800 font-bold text-sm sm:text-base">Launched next-gen<br>platform to drive record<br>client growth.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>



<!-- Values Section -->
<section class="py-10 sm:py-14 lg:py-20 px-4 sm:px-6 lg:px-8">

  <div class="w-auto mx-auto" data-aos="fade-up">
    <!-- Heading -->
    <div class="text-center mb-12 sm:mb-16" data-aos="fade-up">
      <h2
        class="text-4xl md:text-5xl text-center bg-gradient-to-r from-blue-500 to-orange-500 bg-clip-text text-transparent font-bold mb-6">
        Our Values
      </h2>
      <p class="text-base sm:text-lg md:text-xl text-gray-600 font-medium max-w-3xl mx-auto">
        The principles that guide everything we do.
      </p>
    </div>

    <!-- Values Grid -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8" data-aos="fade-up">
      
      <!-- Value 1 -->
      <div class="text-center group px-4 sm:px-2" data-aos="fade-up">
        <div
          class="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform" data-aos="fade-up">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <h3 class="text-lg sm:text-xl font-bold mb-2 sm:mb-3 group-hover:text-blue-400 transition-colors">
          Reliability
        </h3>
        <p class="text-sm sm:text-base text-gray-600 font-medium">
          We build systems you can depend on, with 99.9% uptime and enterprise-grade security.
        </p>
      </div>

      <!-- Value 2 -->
      <div class="text-center group px-4 sm:px-2" data-aos="fade-up">
        <div
          class="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform" data-aos="fade-up">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M13 10V3L4 14h7v7l9-11h-7z">
            </path>
          </svg>
        </div>
        <h3 class="text-lg sm:text-xl font-bold mb-2 sm:mb-3 group-hover:text-purple-400 transition-colors">
          Innovation
        </h3>
        <p class="text-sm sm:text-base text-gray-600 font-medium">
          We constantly push boundaries with AI and automation to give you a competitive edge.
        </p>
      </div>

      <!-- Value 3 -->
      <div class="text-center group px-4 sm:px-2" data-aos="fade-up">
        <div
          class="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform" data-aos="fade-up">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z">
            </path>
          </svg>
        </div>
        <h3 class="text-lg sm:text-xl font-bold mb-2 sm:mb-3 group-hover:text-green-400 transition-colors">
          Collaboration
        </h3>
        <p class="text-sm sm:text-base text-gray-600 font-medium">
          Your success is our success. We work as partners to achieve your business goals.
        </p>
      </div>

      <!-- Value 4 -->
      <div class="text-center group px-4 sm:px-2" data-aos="fade-up">
        <div
          class="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform" data-aos="fade-up">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z">
            </path>
          </svg>
        </div>
        <h3 class="text-lg sm:text-xl font-bold mb-2 sm:mb-3 group-hover:text-orange-400 transition-colors">
          Transparency
        </h3>
        <p class="text-sm sm:text-base text-gray-600 font-medium">
          Clear pricing, honest communication, and open feedback loops in everything we do.
        </p>
      </div>

    </div>
  </div>
</section>

<!-- CTA Section -->
<section class="pt-4 sm:pt-6 lg:pt-8 pb-16 sm:pb-20 lg:pb-24">
  <div class="w-auto mx-auto text-center" data-aos="fade-up">
    <div class="glass-effect shadow-2xl rounded-3xl p-8 sm:p-12" data-aos="fade-up">
      <h2 class="text-4xl md:text-5xl bg-gradient-to-r from-blue-500 to-orange-500 bg-clip-text text-transparent font-bold mb-6">
        Ready to Transform Your Leads?
      </h2>
      <p class="text-lg sm:text-xl text-gray-600 mb-8">
        Start your 14-day free trial today. No credit card required.
      </p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <button
          class="bg-gradient-to-r from-gray-300 to-orange-500 hover:from-blue-500 hover:to-purple-700 hover:text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all transform hover:scale-105" data-aos="fade-up">
          Start Free Trial
        </button>
        <a href="contact.html"
          class="border border-gray-800 bg-gradient-to-l from-gray-100 to-gray-300 hover:from-blue-500 hover:to-purple-700 hover:text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all transform hover:scale-105" data-aos="fade-up">
          Schedule Demo
        </a>
      </div>
    </div>
  </div>
</section>

<!-- Footer -->
<footer class="bg-black text-white px-6 sm:px-8 lg:px-12 w-full text-sm sm:text-base">
  <div class="w-full">
    <div class="py-16 grid grid-cols-1 md:grid-cols-4 gap-10 text-center md:text-left">
      <!-- Logo & Description -->
      <div class="flex flex-col items-center md:items-start space-y-6">
        <img src="images/logo.png" alt="Logo" class="w-40 sm:w-48 h-auto bg-white" />
        <p class="text-gray-400 text-sm sm:text-base max-w-sm text-center md:text-left">
          Effortless payroll, seamless success – simplify your pay process today.
        </p>
        <div class="flex gap-5 justify-center md:justify-start">
         <!-- Social Icons -->
         <a href="#" class="text-gray-400 hover:text-white transition">
          <!-- Facebook -->
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"/>
          </svg>
        </a>
        <!-- Twitter -->
        <a href="#" class="text-gray-400 hover:text-white transition">
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"/>
          </svg>
        </a>
        <!-- Instagram -->
        <a href="#" class="text-gray-400 hover:text-white transition">
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M7.75 2h8.5A5.75 5.75 0 0122 7.75v8.5A5.75 5.75 0 0116.25 22h-8.5A5.75 5.75 0 012 16.25v-8.5A5.75 5.75 0 017.75 2zm0 1.5A4.25 4.25 0 003.5 7.75v8.5A4.25 4.25 0 007.75 20.5h8.5a4.25 4.25 0 004.25-4.25v-8.5A4.25 4.25 0 0016.25 3.5h-8.5zm8.75 2a1 1 0 110 2 1 1 0 010-2zM12 7a5 5 0 110 10 5 5 0 010-10zm0 1.5a3.5 3.5 0 100 7 3.5 3.5 0 000-7z"/>
          </svg>
        </a>
        <!-- LinkedIn -->
        <a href="#" class="text-gray-400 hover:text-white transition">
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M4.98 3.5C4.98 4.61 4.1 5.5 3 5.5S1.02 4.61 1.02 3.5 1.9 1.5 3 1.5 4.98 2.39 4.98 3.5zM.5 6h5V20h-5V6zm7.5 0h4.7v1.785h.066c.655-1.24 2.255-2.535 4.634-2.535 4.953 0 5.867 3.26 5.867 7.5V20h-5v-6.417c0-1.531-.027-3.5-2.134-3.5-2.138 0-2.466 1.668-2.466 3.39V20h-5V6z"/>
          </svg>
        </a>
        </div>
      </div>

      <!-- Quick Links -->
      <div class="space-y-4">
        <h3 class="text-base font-extrabold">Quick Links</h3>
        <ul class="space-y-2 text-gray-400 text-sm sm:text-base">
          <li><a href="index.html" class="hover:text-white">Home</a></li>
          <li><a href="about.html" class="hover:text-white">About Us</a></li>
          <li><a href="products.html" class="hover:text-white">Products</a></li>
          <li><a href="contact.html" class="hover:text-white">Contact Us</a></li>
          <li><a href="login.html" class="hover:text-white">Login</a></li>
        </ul>
      </div>

      <!-- Products -->
      <div class="space-y-4">
        <h3 class="text-base font-extrabold">Products</h3>
        <ul class="space-y-2 text-gray-400 text-sm sm:text-base">
          <li><a href="leadmanagement.html" class="hover:text-white">Lead Management</a></li>
          <li><a href="salemanagement.html" class="hover:text-white">Sales Management</a></li>
          <li><a href="analytics.html" class="hover:text-white">Advance Analytics</a></li>
          <li><a href="customersupport.html" class="hover:text-white">Customer Management</a></li>
          <li><a href="integration.html" class="hover:text-white">Integration</a></li>
        </ul>
      </div>

      <!-- Contact -->
      <div class="space-y-4">
        <h3 class="text-lg font-extrabold">Contact</h3>
        <ul class="space-y-3 text-gray-400 text-sm">
          <li class="flex items-center justify-center md:justify-start gap-2">
            <svg class="w-4 h-4" stroke="currentColor" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            <a href="mailto:<EMAIL>" class="hover:text-white"><EMAIL></a>
          </li>
          <li class="flex items-center justify-center md:justify-start gap-2">
            <svg class="w-4 h-4" stroke="currentColor" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
            </svg>
            +1 (555) 482-9316
          </li>
          <li class="flex items-start justify-center md:justify-start gap-2">
            <svg class="w-4 h-4" stroke="currentColor" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            <span>
             Gurgaon, Haryana
            </span>
          </li>
        </ul>
      </div>
    </div>

    <!-- Bottom -->
    <div class="border-t border-gray-800 py-4 flex flex-col sm:flex-row justify-between items-center text-gray-400 text-sm sm:text-base gap-4 text-center">
      <p>© 2025 LMS. All rights reserved.</p>
      <div class="flex gap-4">
        <a href="#" class="hover:text-white">Terms & Conditions</a>
        <a href="#" class="hover:text-white">Privacy Policy</a>
      </div>
    </div>
  </div>
</footer>


<script src="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js"></script>
<script>
  AOS.init();
</script>
<script>
  // Simple dropdown toggle for Features
  document.addEventListener('DOMContentLoaded', function () {
    const btn = document.getElementById('featuresDropdownBtn');
    const menu = document.getElementById('featuresDropdownMenu');
    btn.addEventListener('click', function (e) {
      e.stopPropagation();
      menu.classList.toggle('hidden');
    });
    // Close dropdown when clicking outside
    document.addEventListener('click', function (e) {
      if (!btn.contains(e.target) && !menu.contains(e.target)) {
        menu.classList.add('hidden');
      }
    });
  });

  // Mobile menu toggle
  document.addEventListener('DOMContentLoaded', function () {
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    const mobileMenu = document.getElementById('mobileMenu');
    mobileMenuBtn.addEventListener('click', function (e) {
      e.stopPropagation();
      mobileMenu.classList.toggle('hidden');
      mobileMenuBtn.classList.toggle('open'); // Toggle open class for X animation
    });
    document.addEventListener('click', function (e) {
      if (!mobileMenu.contains(e.target) && !mobileMenuBtn.contains(e.target)) {
        mobileMenu.classList.add('hidden');
        mobileMenuBtn.classList.remove('open'); // Remove open class if clicking outside
      }
    });

    // Mobile features dropdown
    const mobileFeaturesBtn = document.getElementById('mobileFeaturesBtn');
    const mobileFeaturesMenu = document.getElementById('mobileFeaturesMenu');
    mobileFeaturesBtn.addEventListener('click', function (e) {
      e.stopPropagation();
      mobileFeaturesMenu.classList.toggle('hidden');
    });
    document.addEventListener('click', function (e) {
      if (!mobileFeaturesMenu.contains(e.target) && !mobileFeaturesBtn.contains(e.target)) {
        mobileFeaturesMenu.classList.add('hidden');
      }
    });
  });

  // Counter Animation Function
  function animateCounter(element, target, duration = 2000) {
    const start = 0;
    const increment = target / (duration / 16); // 60fps
    let current = start;
    
    const timer = setInterval(() => {
      current += increment;
      if (current >= target) {
        current = target;
        clearInterval(timer);
      }
      
      // Format the number based on target value
      if (target >= 1000) {
        element.textContent = Math.floor(current).toLocaleString();
      } else if (target < 100) {
        element.textContent = Math.floor(current) + '%';
      } else {
        element.textContent = Math.floor(current);
      }
    }, 16);
  }

  // Intersection Observer for counter animation
  const observerOptions = {
    threshold: 0.5,
    rootMargin: '0px 0px -100px 0px'
  };

  const counterObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const customersCount = document.getElementById('customers-count');
        const leadsCount = document.getElementById('leads-count');
        const conversionRate = document.getElementById('conversion-rate');
        
        // Animate counters
        animateCounter(customersCount, 1000, 2000);
        animateCounter(leadsCount, 10000, 2500);
        animateCounter(conversionRate, 98, 1500);
        
        // Stop observing after animation starts
        counterObserver.unobserve(entry.target);
      }
    });
  }, observerOptions);

  // Start observing the statistics section
  document.addEventListener('DOMContentLoaded', function() {
    const statsSection = document.querySelector('[data-aos="fade-up"]');
    if (statsSection) {
      counterObserver.observe(statsSection);
    }
  });
</script>


</body>

</html>