<?php
// Get database connection using reliable method
$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';

// Check if database is set up first
require_once 'check_database_setup.php';
redirect_to_setup_if_needed();

// Check if the user is logged in
ensure_session_started();
require_login();

$error_message = '';
$success_message = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['lead_id'])) {
    $lead_id = intval($_POST['lead_id']);

    // Debug information
    error_log("Convert Lead Debug - Lead ID: " . $lead_id);
    error_log("Convert Lead Debug - User ID: " . (isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 'NOT SET'));
    error_log("Convert Lead Debug - User Role: " . (isset($_SESSION['role']) ? $_SESSION['role'] : 'NOT SET'));
    error_log("Convert Lead Debug - POST data: " . print_r($_POST, true));

    if ($lead_id <= 0) {
        $error_message = "Invalid lead ID.";
    } else {
        // Check if user has permission to convert this lead
        $is_sales_rep = isset($_SESSION['role']) && $_SESSION['role'] === 'sales_rep';
        $sales_rep_id = $is_sales_rep ? $_SESSION['user_id'] : 0;
        
        // Get lead details
        $lead_sql = "SELECT * FROM leads WHERE id = ?";
        if ($is_sales_rep) {
            $lead_sql .= " AND assigned_to = ?";
        }
        
        $lead_stmt = mysqli_prepare($conn, $lead_sql);
        if ($is_sales_rep) {
            mysqli_stmt_bind_param($lead_stmt, "ii", $lead_id, $sales_rep_id);
        } else {
            mysqli_stmt_bind_param($lead_stmt, "i", $lead_id);
        }
        mysqli_stmt_execute($lead_stmt);
        $lead_result = mysqli_stmt_get_result($lead_stmt);
        $lead = mysqli_fetch_assoc($lead_result);
        
        if (!$lead) {
            $error_message = "Lead not found or you don't have permission to convert it.";
        } else {
            // Start transaction
            mysqli_begin_transaction($conn);
            
            try {
                // Insert into contacts table
                $contact_sql = "INSERT INTO contacts (first_name, last_name, email, phone, country_code, 
                               address, source, customer_interest, assigned_to, created_by, created_at) 
                               VALUES (?, ?, ?, ?, ?, '', ?, ?, ?, ?, NOW())";
                $contact_stmt = mysqli_prepare($conn, $contact_sql);
                mysqli_stmt_bind_param($contact_stmt, "sssssssii", 
                    $lead['first_name'], $lead['last_name'], $lead['email'], 
                    $lead['phone'], $lead['country_code'], $lead['source'], 
                    $lead['customer_interest'], $lead['assigned_to'], $lead['created_by']);
                
                if (mysqli_stmt_execute($contact_stmt)) {
                    $new_contact_id = mysqli_insert_id($conn);
                    
                    // Update lead status to converted and set converted_to_contact_id
                    $update_lead_sql = "UPDATE leads SET status = 'converted', converted_to_contact_id = ? WHERE id = ?";
                    $update_stmt = mysqli_prepare($conn, $update_lead_sql);
                    mysqli_stmt_bind_param($update_stmt, "ii", $new_contact_id, $lead_id);
                    
                    if (mysqli_stmt_execute($update_stmt)) {
                        // Delete from leads table (since it's now converted)
                        $delete_lead_sql = "DELETE FROM leads WHERE id = ?";
                        $delete_stmt = mysqli_prepare($conn, $delete_lead_sql);
                        mysqli_stmt_bind_param($delete_stmt, "i", $lead_id);
                        
                        if (mysqli_stmt_execute($delete_stmt)) {
                            // Create notification for assigned sales rep
                            if ($lead['assigned_to']) {
                                $notification_title = "Lead Converted to Customer";
                                $notification_message = "Lead " . $lead['first_name'] . " " . $lead['last_name'] . " has been converted to a customer.";
                                $notification_link = "view_contact.php?id=" . $new_contact_id;
                                
                                $notify_sql = "INSERT INTO notifications (user_id, title, message, link, type, created_at) 
                                              VALUES (?, ?, ?, ?, 'success', NOW())";
                                $notify_stmt = mysqli_prepare($conn, $notify_sql);
                                mysqli_stmt_bind_param($notify_stmt, "isss", $lead['assigned_to'], $notification_title, $notification_message, $notification_link);
                                mysqli_stmt_execute($notify_stmt);
                            }
                            
                            // Create notification for the user who performed the conversion (if different)
                            if ($lead['assigned_to'] != $_SESSION['user_id']) {
                                $notification_title = "Lead Conversion Completed";
                                $notification_message = "You successfully converted lead " . $lead['first_name'] . " " . $lead['last_name'] . " to a customer.";
                                $notification_link = "view_contact.php?id=" . $new_contact_id;
                                
                                $notify_sql = "INSERT INTO notifications (user_id, title, message, link, type, created_at) 
                                              VALUES (?, ?, ?, ?, 'success', NOW())";
                                $notify_stmt = mysqli_prepare($conn, $notify_sql);
                                mysqli_stmt_bind_param($notify_stmt, "isss", $_SESSION['user_id'], $notification_title, $notification_message, $notification_link);
                                mysqli_stmt_execute($notify_stmt);
                            }
                            
                            mysqli_commit($conn);
                            $success_message = "Lead successfully converted to customer!";
                            
                            // Redirect to the new contact page after a short delay
                            header("refresh:2;url=view_contact.php?id=" . $new_contact_id);
                        } else {
                            mysqli_rollback($conn);
                            $error_message = "Error removing lead from leads table: " . mysqli_error($conn);
                        }
                    } else {
                        mysqli_rollback($conn);
                        $error_message = "Error updating lead status: " . mysqli_error($conn);
                    }
                } else {
                    mysqli_rollback($conn);
                    $error_message = "Error creating customer record: " . mysqli_error($conn);
                }
            } catch (Exception $e) {
                mysqli_rollback($conn);
                $error_message = "Error during conversion: " . $e->getMessage();
            }
        }
    }
} else {
    $error_message = "Invalid request.";
}

$page_title = "Convert Lead";
include __DIR__ . '/includes/header.php';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="img/minilogo.jpg" type="image/x-icon">
    <title>Convert Lead - CRM System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">

<?php include 'includes/navigation.php'; ?>

<!-- Main Content -->
<div class="ml-0 min-[870px]:ml-60 min-h-screen flex flex-col">
    <header class="bg-[#1E3E62] p-4 flex justify-between items-center z-10">
        <h1 class="text-2xl font-bold text-white mx-10 my-2">Convert Lead</h1>
        <div class="mx-10">
            <?php include 'includes/top_notification.php'; ?>
        </div>
    </header>

    <main class="flex-1 p-10">
        <div class="max-w-2xl mx-auto">
            <!-- Back Button -->
            <div class="mb-6">
                <a href="leads.php" class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Leads
                </a>
            </div>

            <!-- Result Card -->
            <div class="bg-white rounded-lg shadow-md p-8">
                <?php if (!empty($success_message)): ?>
                    <div class="text-center">
                        <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
                            <i class="fas fa-check text-green-600 text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Conversion Successful!</h3>
                        <p class="text-sm text-gray-500 mb-6"><?php echo $success_message; ?></p>
                        <div class="flex justify-center space-x-4">
                            <a href="leads.php" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
                                Back to Leads
                            </a>
                            <a href="contacts.php" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                                View Customers
                            </a>
                        </div>
                        <p class="text-xs text-gray-400 mt-4">
                            <i class="fas fa-info-circle mr-1"></i>
                            You will be redirected to the customer profile in a moment...
                        </p>
                    </div>
                <?php else: ?>
                    <div class="text-center">
                        <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
                            <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Conversion Failed</h3>
                        <p class="text-sm text-red-600 mb-6"><?php echo $error_message; ?></p>
                        <div class="flex justify-center space-x-4">
                            <a href="leads.php" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
                                Back to Leads
                            </a>
                            <button onclick="history.back()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                                Try Again
                            </button>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </main>
</div>

</body>
</html>
