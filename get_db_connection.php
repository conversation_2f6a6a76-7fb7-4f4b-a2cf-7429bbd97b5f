<?php
/**
 * Simple Database Connection Helper
 * 
 * This file provides a reliable way to get a database connection
 * without the issues caused by require_once return values.
 */

function get_db_connection() {
    // Database configuration
    $host = 'localhost';
    $username = 'root';
    $password = '';
    $database = 'lead_management';
    
    try {
        // Create connection
        $conn = new mysqli($host, $username, $password);
        
        // Check connection
        if ($conn->connect_error) {
            throw new Exception("Connection failed: " . $conn->connect_error);
        }
        
        // Set charset
        $conn->set_charset('utf8mb4');
        
        // Create database if it doesn't exist
        $sql = "CREATE DATABASE IF NOT EXISTS $database";
        if ($conn->query($sql)) {
            // Select the database
            $conn->select_db($database);
        } else {
            throw new Exception("Could not create/select database: " . $conn->error);
        }
        
        return $conn;
        
    } catch (Exception $e) {
        // Return false on error so we can handle it gracefully
        error_log("Database connection error: " . $e->getMessage());
        return false;
    }
}

// If this file is included directly, return the connection
if (!function_exists('get_database_connection')) {
    $conn = get_db_connection();
    
    if (!$conn) {
        die("
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px;'>
            <h2 style='color: #d32f2f;'>Database Connection Error</h2>
            <p>Could not connect to the database. Please check:</p>
            <ul>
                <li>XAMPP MySQL service is running</li>
                <li>Database credentials are correct</li>
                <li>MySQL is accessible on localhost:3306</li>
            </ul>
            <p><strong>Next steps:</strong></p>
            <ol>
                <li><a href='test_db_connection.php' style='color: #1976d2;'>Run database connection test</a></li>
                <li><a href='initialize_database.php' style='color: #1976d2;'>Initialize database</a></li>
                <li>Check XAMPP Control Panel</li>
            </ol>
        </div>
        ");
    }
    
    return $conn;
}
?>
