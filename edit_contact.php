<?php
// Get database connection using reliable method
$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';
require_once 'includes/validation.php';

// Check if database is set up first
require_once 'check_database_setup.php';
redirect_to_setup_if_needed();

// Check if the user is logged in
ensure_session_started();
require_login();

// Block access for sales reps
if (isset($_SESSION['role']) && $_SESSION['role'] === 'sales_rep') {
    header("Location: contacts.php?error=Unauthorized");
    exit;
}

// Get contact ID from URL
$contact_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($contact_id <= 0) {
    header("Location: contacts.php");
    exit;
}

$error_message = '';
$success_message = '';

// Get contact data
$sql = "SELECT * FROM contacts WHERE id = ?";
$stmt = mysqli_prepare($conn, $sql);
mysqli_stmt_bind_param($stmt, "i", $contact_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) == 0) {
    header("Location: contacts.php?error=Contact not found");
    exit;
}

$contact = mysqli_fetch_assoc($result);

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $first_name = sanitize_input($_POST['first_name']);
    $last_name = sanitize_input($_POST['last_name']);
    $email = sanitize_input($_POST['email']);
    $phone = sanitize_input($_POST['phone']);
    $assigned_to = !empty($_POST['assigned_to']) ? intval($_POST['assigned_to']) : null;
    $address = sanitize_input($_POST['address']);

    $category = sanitize_input($_POST['category']);
    $is_referral = isset($_POST['is_referral']) ? 1 : 0;
    $referred_by_contact_id = !empty($_POST['referred_by_contact_id']) ? intval($_POST['referred_by_contact_id']) : null;
    $referrer_name = sanitize_input($_POST['referrer_name'] ?? '');
    $referrer_phone = sanitize_input($_POST['referrer_phone'] ?? '');
    $referrer_relationship = sanitize_input($_POST['referrer_relationship'] ?? '');
    $product_interests = isset($_POST['product_interests']) ? $_POST['product_interests'] : [];

    // Convert selected product interests to comma-separated string for customer_interest field
    if (!empty($product_interests)) {
        // Get product names for the selected IDs
        $product_names = [];
        $placeholders = str_repeat('?,', count($product_interests) - 1) . '?';
        $product_query = "SELECT name FROM products WHERE id IN ($placeholders)";
        $product_stmt = mysqli_prepare($conn, $product_query);

        // Create types string (all integers)
        $types = str_repeat('i', count($product_interests));
        mysqli_stmt_bind_param($product_stmt, $types, ...$product_interests);
        mysqli_stmt_execute($product_stmt);
        $product_result = mysqli_stmt_get_result($product_stmt);

        while ($row = mysqli_fetch_assoc($product_result)) {
            $product_names[] = $row['name'];
        }

        $customer_interest = implode(', ', $product_names);
    } else {
        $customer_interest = '';
    }
    
    // Validation
    $errors = [];
    
    if (empty($first_name)) {
        $errors[] = "First name is required.";
    }
    
    if (empty($last_name)) {
        $errors[] = "Last name is required.";
    }
    
    if (empty($email)) {
        $errors[] = "Email is required.";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Please enter a valid email address.";
    }
    
    // Enhanced phone validation
    if (!empty($phone)) {
        $phone_validation = get_phone_validation_result($phone);
        if (!$phone_validation['valid']) {
            $errors[] = "Contact Phone: " . $phone_validation['message'];
        } else {
            // Check if phone number already exists (excluding current contact)
            $check_phone_sql = "SELECT id, CONCAT(first_name, ' ', last_name) as name FROM contacts WHERE phone = ? AND id != ?";
            $check_phone_stmt = mysqli_prepare($conn, $check_phone_sql);
            mysqli_stmt_bind_param($check_phone_stmt, "si", $phone, $contact_id);
            mysqli_stmt_execute($check_phone_stmt);
            $check_phone_result = mysqli_stmt_get_result($check_phone_stmt);
            if (mysqli_num_rows($check_phone_result) > 0) {
                $existing_contact = mysqli_fetch_assoc($check_phone_result);
                $errors[] = "Phone number already exists for contact: " . $existing_contact['name'];
            }
        }
    }

    // Enhanced referrer phone validation (if provided)
    if (!empty($referrer_phone)) {
        $referrer_phone_validation = get_phone_validation_result($referrer_phone);
        if (!$referrer_phone_validation['valid']) {
            $errors[] = "Referrer Phone: " . $referrer_phone_validation['message'];
        } else {
            // Check if referrer phone is same as contact phone
            if ($referrer_phone === $phone) {
                $errors[] = "Referrer phone number cannot be the same as contact phone number";
            }
        }
    }

    // Check if email already exists (excluding current contact)
    if (!empty($email)) {
        $check_email_sql = "SELECT id FROM contacts WHERE email = ? AND id != ?";
        $check_email_stmt = mysqli_prepare($conn, $check_email_sql);
        mysqli_stmt_bind_param($check_email_stmt, "si", $email, $contact_id);
        mysqli_stmt_execute($check_email_stmt);
        $check_email_result = mysqli_stmt_get_result($check_email_stmt);

        if (mysqli_num_rows($check_email_result) > 0) {
            $errors[] = "A contact with this email already exists.";
        }
    }
    
    if (empty($errors)) {
        $sql = "UPDATE contacts SET
                first_name = ?, last_name = ?, email = ?, phone = ?,
                assigned_to = ?, address = ?,
                customer_interest = ?, category = ?,
                is_referral = ?, referred_by_contact_id = ?, referrer_name = ?, referrer_phone = ?, referrer_relationship = ?,
                updated_at = CURRENT_TIMESTAMP
                WHERE id = ?";

        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "ssssisssiissssi",
            $first_name, $last_name, $email, $phone,
            $assigned_to, $address,
            $customer_interest, $category,
            $is_referral, $referred_by_contact_id, $referrer_name, $referrer_phone, $referrer_relationship, $contact_id);
        
        if (mysqli_stmt_execute($stmt)) {
            // Product interests are now saved in the customer_interest field
            header("Location: view_contact.php?id=" . $contact_id . "&success=updated");
            exit;
        } else {
            $error_message = "Error updating contact: " . mysqli_error($conn);
        }
    } else {
        $error_message = implode("<br>", $errors);
    }
} else {
    // Pre-populate form with existing data
    $_POST = $contact;
}

// Get only sales representatives for assignment dropdown
$users_query = "SELECT id, first_name, last_name FROM employees WHERE role = 'sales_rep' ORDER BY first_name, last_name";
$users_result = mysqli_query($conn, $users_query);

// Get all products for interest selection
$products_query = "SELECT id, name, category FROM products ORDER BY category, name";
$products_result = mysqli_query($conn, $products_query);

// Check if contact_product_interests table exists
$table_check = "SHOW TABLES LIKE 'contact_product_interests'";
$table_result = mysqli_query($conn, $table_check);
$interests_table_exists = mysqli_num_rows($table_result) > 0;

// Get all products for interest selection
$products_query = "SELECT id, name, category FROM products ORDER BY category, name";
$products_result = mysqli_query($conn, $products_query);

// Get current product interests from customer_interest field
$current_product_interests = [];
if (!empty($contact['customer_interest'])) {
    // Parse the customer_interest field to find matching product IDs
    $interest_names = array_map('trim', explode(',', $contact['customer_interest']));

    // Get product IDs for the interest names
    if (!empty($interest_names)) {
        $placeholders = str_repeat('?,', count($interest_names) - 1) . '?';
        $match_query = "SELECT id FROM products WHERE name IN ($placeholders)";
        $match_stmt = mysqli_prepare($conn, $match_query);

        $types = str_repeat('s', count($interest_names));
        mysqli_stmt_bind_param($match_stmt, $types, ...$interest_names);
        mysqli_stmt_execute($match_stmt);
        $match_result = mysqli_stmt_get_result($match_stmt);

        while ($row = mysqli_fetch_assoc($match_result)) {
            $current_product_interests[] = $row['id'];
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="img/minilogo.jpg" type="image/x-icon">
    <title>Edit Contact - CRM System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="js/validation.js"></script>
    <style>
        .text-color { color: #FF6500; }
        .bg-color { background-color: #FF6500; }
    </style>
</head>
<body class="bg-gray-100">

<?php include 'includes/navigation.php'; ?>

<!-- Main Content -->
<div class="ml-0 min-[870px]:ml-60 min-h-screen flex flex-col">
    <header class="bg-[#1E3E62] p-4 flex justify-between z-10 flex-col">
        <h1 class="text-2xl font-bold text-white mx-10 my-2">Edit Contact</h1>
            </header>
    
    <main class="flex-1 p-10">
        <?php if (!empty($error_message)): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <?php echo $error_message; ?>
        </div>
        <?php endif; ?>

        <div class="bg-white rounded-lg shadow p-6">
            <form method="POST" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- First Name -->
                    <div>
                        <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2">
                            First Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="first_name" name="first_name" required pattern="[A-Za-z\s]+" 
                               title="Only alphabetic characters are allowed"
                               value="<?php echo htmlspecialchars($_POST['first_name'] ?? ''); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>

                    <!-- Last Name -->
                    <div>
                        <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2">
                            Last Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="last_name" name="last_name" required pattern="[A-Za-z\s]+"
                               title="Only alphabetic characters are allowed"
                               value="<?php echo htmlspecialchars($_POST['last_name'] ?? ''); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            
                    </div>

                    <!-- Email -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                            Email <span class="text-red-500">*</span>
                        </label>
                        <input type="email" id="email" name="email" required
                               value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>

                    <!-- Phone -->
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                            Phone <span class="text-red-500">*</span>
                        </label>
                        <input type="text"
                               id="phone"
                               name="phone"
                               inputmode="numeric"
                               pattern="[0-9]*"
                               maxlength="10"
                               value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="Enter 10-digit mobile number"
                               required>
                        <div class="phone-validation-message text-sm mt-1"></div>
                    </div>



                    


                    <!-- Assigned To -->
                    <div>
                        <label for="assigned_to" class="block text-sm font-medium text-gray-700 mb-2">Assign To</label>
                        <select id="assigned_to" name="assigned_to"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">Unassigned</option>
                            <?php while ($user = mysqli_fetch_assoc($users_result)): ?>
                            <option value="<?php echo $user['id']; ?>" <?php echo ($_POST['assigned_to'] ?? '') == $user['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>
                            </option>
                            <?php endwhile; ?>
                        </select>
                    </div>

                    <!-- Category -->
                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                        <input type="text" id="category" name="category"
                               value="<?php echo htmlspecialchars($_POST['category'] ?? ''); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>


                </div>

                <!-- Address -->
                <div>
                    <label for="address" class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                    <textarea id="address" name="address" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              placeholder="Full address..."><?php echo htmlspecialchars($_POST['address'] ?? ''); ?></textarea>
                </div>

                <!-- Product Interests -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Product Interests</label>
                    <div class="border border-gray-300 rounded-md p-3 max-h-48 overflow-y-auto">
                        <?php
                        mysqli_data_seek($products_result, 0); // Reset result pointer
                        $current_category = '';
                        while ($product = mysqli_fetch_assoc($products_result)):
                            if ($current_category !== $product['category']):
                                if ($current_category !== '') echo '</div>';
                                $current_category = $product['category'];
                        ?>
                            <div class="mb-3">
                                <h4 class="text-sm font-medium text-gray-600 mb-2"><?php echo htmlspecialchars($current_category ?: 'General'); ?></h4>
                        <?php endif; ?>
                                <label class="flex items-center mb-1">
                                    <input type="checkbox" name="product_interests[]" value="<?php echo $product['id']; ?>"
                                           <?php echo in_array($product['id'], $current_product_interests) ? 'checked' : ''; ?>
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                                    <span class="text-sm text-gray-700"><?php echo htmlspecialchars($product['name']); ?></span>
                                </label>
                        <?php endwhile; ?>
                        <?php if ($current_category !== '') echo '</div>'; ?>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">Select products the contact is interested in</p>
                </div>

                <!-- Customer Interest (Legacy) -->
                <div>
                    <label for="customer_interest" class="block text-sm font-medium text-gray-700 mb-2">Additional Interest Notes</label>
                    <textarea id="customer_interest" name="customer_interest" rows="2"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              placeholder="Any additional notes about customer interests..."><?php echo htmlspecialchars($_POST['customer_interest'] ?? ''); ?></textarea>
                </div>

                <!-- Referral Information -->
                <div class="border-t pt-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Referral Information</h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Is Referral Checkbox -->
                        <div class="md:col-span-2">
                            <label class="flex items-center">
                                <input type="checkbox" id="is_referral" name="is_referral" value="1"
                                       <?php echo (isset($_POST['is_referral']) && $_POST['is_referral']) ? 'checked' : ''; ?>
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2">
                                <span class="text-sm font-medium text-gray-700">This contact was referred by someone</span>
                            </label>
                        </div>

                        <!-- Referral Fields (shown when checkbox is checked) -->
                        <div id="referral-fields" class="md:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-4" style="display: none;">
                            <div>
                                <label for="referred_by_contact_id" class="block text-sm font-medium text-gray-700 mb-2">Referred by Contact ID</label>
                                <input type="number" id="referred_by_contact_id" name="referred_by_contact_id"
                                       value="<?php echo htmlspecialchars($_POST['referred_by_contact_id'] ?? ''); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       placeholder="Enter contact ID if referrer is existing contact">
                            </div>
                            <div>
                                <label for="referrer_name" class="block text-sm font-medium text-gray-700 mb-2">Referrer Name</label>
                                <input type="text" id="referrer_name" name="referrer_name"
                                       value="<?php echo htmlspecialchars($_POST['referrer_name'] ?? ''); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       placeholder="Enter referrer's full name">
                            </div>
                            <div>
                                <label for="referrer_phone" class="block text-sm font-medium text-gray-700 mb-2">
                                    Referrer Phone
                                </label>
                                <input type="text"
                                       id="referrer_phone"
                                       name="referrer_phone"
                                       inputmode="numeric"
                                       pattern="[0-9]*"
                                       maxlength="10"
                                       value="<?php echo htmlspecialchars($_POST['referrer_phone'] ?? ''); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                       placeholder="Enter referrer's mobile number">
                                <div class="referrer-phone-validation-message text-sm mt-1"></div>
                            </div>
                            <div>
                                <label for="referrer_relationship" class="block text-sm font-medium text-gray-700 mb-2">Referrer Relationship</label>
                                <select id="referrer_relationship" name="referrer_relationship"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="">Select Relationship</option>
                                    <option value="Friend" <?php echo (isset($_POST['referrer_relationship']) && $_POST['referrer_relationship'] == 'Friend') ? 'selected' : ''; ?>>Friend</option>
                                    <option value="Family" <?php echo (isset($_POST['referrer_relationship']) && $_POST['referrer_relationship'] == 'Family') ? 'selected' : ''; ?>>Family</option>
                                    <option value="Colleague" <?php echo (isset($_POST['referrer_relationship']) && $_POST['referrer_relationship'] == 'Colleague') ? 'selected' : ''; ?>>Colleague</option>
                                    <option value="Business Partner" <?php echo (isset($_POST['referrer_relationship']) && $_POST['referrer_relationship'] == 'Business Partner') ? 'selected' : ''; ?>>Business Partner</option>
                                    <option value="Neighbor" <?php echo (isset($_POST['referrer_relationship']) && $_POST['referrer_relationship'] == 'Neighbor') ? 'selected' : ''; ?>>Neighbor</option>
                                    <option value="Other" <?php echo (isset($_POST['referrer_relationship']) && $_POST['referrer_relationship'] == 'Other') ? 'selected' : ''; ?>>Other</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="flex justify-end space-x-4">
                    <a href="view_contact.php?id=<?php echo $contact_id; ?>" class="px-6 py-2 bg-[#0b192c] text-white rounded-sm hover:bg-[#1e3e62]">
                        Cancel
                    </a>
                    <button type="submit" class="px-6 py-2 bg-[#0b192c] text-white rounded-sm hover:bg-[#1e3e62]" title="Update Contact">
                        <i class="fas fa-save"></i>
                    </button>
                </div>
            </form>
        </div>
    </main>
</div>

<script>
// Enhanced phone validation for edit contact form
document.addEventListener('DOMContentLoaded', function() {
    const phoneInput = document.getElementById('phone');
    const referrerPhoneInput = document.getElementById('referrer_phone');
    const isReferralCheckbox = document.getElementById('is_referral');
    const referralFields = document.getElementById('referral-fields');

    // Function to handle phone validation display
    function handlePhoneValidation(input) {
        const messageElement = input.parentElement.querySelector('.phone-validation-message');

        input.addEventListener('input', function() {
            const phone = this.value.trim();

            if (phone.length > 0) {
                const validation = validateIndianMobile(phone);

                if (!validation.valid) {
                    this.setCustomValidity(validation.message);
                    this.classList.add('border-red-500');
                    this.classList.remove('border-green-500');

                    if (messageElement) {
                        messageElement.innerHTML = `<i class="fas fa-times-circle text-red-500"></i> ${validation.message}`;
                        messageElement.className = 'phone-validation-message text-red-600 text-sm mt-1';
                    }
                } else {
                    this.setCustomValidity('');
                    this.classList.remove('border-red-500');
                    this.classList.add('border-green-500');

                    if (messageElement) {
                        messageElement.innerHTML = `<i class="fas fa-check-circle text-green-500"></i> ${validation.message}`;
                        messageElement.className = 'phone-validation-message text-green-600 text-sm mt-1';
                    }
                }
            } else {
                this.setCustomValidity('');
                this.classList.remove('border-red-500', 'border-green-500');

                if (messageElement) {
                    messageElement.innerHTML = '';
                }
            }
        });

        // Also validate on blur for better UX
        input.addEventListener('blur', function() {
            if (this.value.trim().length > 0) {
                this.dispatchEvent(new Event('input'));
            }
        });
    }

    // Apply validation to phone input
    if (phoneInput) {
        phoneInput.setAttribute('data-custom-validation', 'true');
        handlePhoneValidation(phoneInput);
    }

    // Handle referral checkbox toggle
    if (isReferralCheckbox && referralFields) {
        // Show/hide referral fields based on checkbox state
        function toggleReferralFields() {
            if (isReferralCheckbox.checked) {
                referralFields.style.display = 'grid';
            } else {
                referralFields.style.display = 'none';
                // Clear referral fields when hidden
                document.getElementById('referred_by_contact_id').value = '';
                document.getElementById('referrer_name').value = '';
                document.getElementById('referrer_phone').value = '';
                document.getElementById('referrer_relationship').value = '';
            }
        }

        // Initial state
        toggleReferralFields();

        // Toggle on checkbox change
        isReferralCheckbox.addEventListener('change', toggleReferralFields);
    }

    // Apply comprehensive validation to referrer phone input
    if (referrerPhoneInput) {
        const referrerMessageElement = referrerPhoneInput.parentElement.querySelector('.referrer-phone-validation-message');

        // Debounce function to prevent multiple rapid validations
        let referrerValidationTimeout;

        function validateReferrerPhone() {
            clearTimeout(referrerValidationTimeout);
            referrerValidationTimeout = setTimeout(() => {
                const phone = referrerPhoneInput.value.trim();
                const mainPhone = phoneInput ? phoneInput.value.trim() : '';

                // Clear any existing messages first
                if (referrerMessageElement) {
                    referrerMessageElement.innerHTML = '';
                }

                if (phone.length > 0) {
                    const validation = validateIndianMobile(phone);

                    // Check if referrer phone is same as main contact phone
                    if (phone === mainPhone && mainPhone.length > 0) {
                        referrerPhoneInput.setCustomValidity('Referrer phone cannot be the same as contact phone');
                        referrerPhoneInput.classList.add('border-red-500');
                        referrerPhoneInput.classList.remove('border-green-500');

                        if (referrerMessageElement) {
                            referrerMessageElement.innerHTML = '<i class="fas fa-times-circle text-red-500"></i> Referrer phone cannot be the same as contact phone';
                            referrerMessageElement.className = 'referrer-phone-validation-message text-red-600 text-sm mt-1';
                        }
                    } else if (!validation.valid) {
                        referrerPhoneInput.setCustomValidity(validation.message);
                        referrerPhoneInput.classList.add('border-red-500');
                        referrerPhoneInput.classList.remove('border-green-500');

                        if (referrerMessageElement) {
                            referrerMessageElement.innerHTML = `<i class="fas fa-times-circle text-red-500"></i> ${validation.message}`;
                            referrerMessageElement.className = 'referrer-phone-validation-message text-red-600 text-sm mt-1';
                        }
                    } else {
                        referrerPhoneInput.setCustomValidity('');
                        referrerPhoneInput.classList.remove('border-red-500');
                        referrerPhoneInput.classList.add('border-green-500');

                        if (referrerMessageElement) {
                            referrerMessageElement.innerHTML = `<i class="fas fa-check-circle text-green-500"></i> ${validation.message}`;
                            referrerMessageElement.className = 'referrer-phone-validation-message text-green-600 text-sm mt-1';
                        }
                    }
                } else {
                    referrerPhoneInput.setCustomValidity('');
                    referrerPhoneInput.classList.remove('border-red-500', 'border-green-500');
                }
            }, 100); // 100ms debounce delay
        }

        referrerPhoneInput.addEventListener('input', validateReferrerPhone);
        referrerPhoneInput.addEventListener('blur', validateReferrerPhone);

        // Re-validate referrer phone when main phone changes
        if (phoneInput) {
            phoneInput.addEventListener('input', function() {
                if (referrerPhoneInput.value.trim().length > 0) {
                    validateReferrerPhone();
                }
            });
        }
    }
});
</script>

</body>
</html>
