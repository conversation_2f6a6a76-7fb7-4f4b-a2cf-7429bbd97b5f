<?php
// Get database connection using reliable method
$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';

// Check if database is set up first
require_once 'check_database_setup.php';
redirect_to_setup_if_needed();

// Check if the user is logged in
ensure_session_started();
require_login();

// Only sales reps can access this page
if (!has_role('sales_rep')) {
    header("Location: index.php");
    exit;
}

$user_id = $_SESSION['user_id'];

// Handle bulk actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['bulk_action'])) {
    $action = $_POST['bulk_action'];
    $selected_contacts = isset($_POST['selected_contacts']) ? $_POST['selected_contacts'] : [];

    if (!empty($selected_contacts) && !empty($action)) {
        $contact_ids = array_map('intval', $selected_contacts);
        $placeholders = str_repeat('?,', count($contact_ids) - 1) . '?';

        // Only allow actions on contacts assigned to or created by this user
        $ownership_check = "SELECT id FROM contacts WHERE id IN ($placeholders) AND (assigned_to = ? OR created_by = ?)";
        $check_params = array_merge($contact_ids, [$user_id, $user_id]);
        $check_stmt = mysqli_prepare($conn, $ownership_check);
        mysqli_stmt_bind_param($check_stmt, str_repeat('i', count($check_params)), ...$check_params);
        mysqli_stmt_execute($check_stmt);
        $check_result = mysqli_stmt_get_result($check_stmt);
        $allowed_contacts = [];
        while ($row = mysqli_fetch_assoc($check_result)) {
            $allowed_contacts[] = $row['id'];
        }

        if (!empty($allowed_contacts)) {
            $allowed_placeholders = str_repeat('?,', count($allowed_contacts) - 1) . '?';

            if ($action === 'delete') {
                $delete_sql = "DELETE FROM contacts WHERE id IN ($allowed_placeholders)";
                $stmt = mysqli_prepare($conn, $delete_sql);
                mysqli_stmt_bind_param($stmt, str_repeat('i', count($allowed_contacts)), ...$allowed_contacts);

                if (mysqli_stmt_execute($stmt)) {
                    $success_message = count($allowed_contacts) . " contact(s) deleted successfully.";
                } else {
                    $error_message = "Error deleting contacts: " . mysqli_error($conn);
                }
            } elseif ($action === 'unassign') {
                $unassign_sql = "UPDATE contacts SET assigned_to = NULL WHERE id IN ($allowed_placeholders)";
                $stmt = mysqli_prepare($conn, $unassign_sql);
                mysqli_stmt_bind_param($stmt, str_repeat('i', count($allowed_contacts)), ...$allowed_contacts);

                if (mysqli_stmt_execute($stmt)) {
                    $success_message = count($allowed_contacts) . " contact(s) unassigned successfully.";
                } else {
                    $error_message = "Error unassigning contacts: " . mysqli_error($conn);
                }
            }
        } else {
            $error_message = "You don't have permission to perform this action on the selected contacts.";
        }
    } else {
        $error_message = "Please select contacts and choose an action.";
    }
}

// Get filter parameters
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$search = isset($_GET['search']) ? $_GET['search'] : '';

// Pagination parameters
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$records_per_page = 10;
$offset = ($page - 1) * $records_per_page;

// Build filtered query
$where_conditions = ["c.assigned_to = ?"];
$params = [$user_id];
$param_types = 'i';

if (!empty($status_filter)) {
    // Handle different filter types
    switch ($status_filter) {
        case 'referral':
            $where_conditions[] = "c.is_referral = 1";
            break;
        case 'direct':
            $where_conditions[] = "c.is_referral IS NULL OR c.is_referral = 0";
            break;
        default:
            // No default status filter since status column is removed
            break;
    }
}

if (!empty($search)) {
    $where_conditions[] = "(c.first_name LIKE ? OR c.last_name LIKE ? OR c.email LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $param_types .= 'sss';
}

$where_clause = " WHERE " . implode(" AND ", $where_conditions);

// Get total count for pagination
$count_sql = "SELECT COUNT(*) as total FROM contacts c" . $where_clause;
$count_stmt = mysqli_prepare($conn, $count_sql);
mysqli_stmt_bind_param($count_stmt, $param_types, ...$params);
mysqli_stmt_execute($count_stmt);
$count_result = mysqli_stmt_get_result($count_stmt);
$total_records = mysqli_fetch_assoc($count_result)['total'];
$total_pages = ceil($total_records / $records_per_page);

// Build main query with pagination
$sql = "SELECT c.*,
        CONCAT(c.first_name, ' ', c.last_name) as full_name,
        c.customer_interest,
        CONCAT(u.first_name, ' ', u.last_name) as assigned_to_name,
        CONCAT(u2.first_name, ' ', u2.last_name) as created_by_name,
        CONCAT(ref.first_name, ' ', ref.last_name) as referred_by_name
        FROM contacts c
        LEFT JOIN employees u ON c.assigned_to = u.id
        LEFT JOIN employees u2 ON c.created_by = u2.id
        LEFT JOIN contacts ref ON c.referred_by_contact_id = ref.id" .
        $where_clause .
        " ORDER BY c.created_at DESC
        LIMIT ? OFFSET ?";

// Add pagination parameters
$params[] = $records_per_page;
$params[] = $offset;
$param_types .= 'ii';

$stmt = mysqli_prepare($conn, $sql);
mysqli_stmt_bind_param($stmt, $param_types, ...$params);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

// Get statistics for this sales rep
$stats_sql = "SELECT
    COUNT(*) as total_contacts,
    COUNT(DISTINCT s.contact_id) as my_customers,
    (COUNT(*) - COUNT(DISTINCT s.contact_id)) as my_leads
    FROM contacts c
    LEFT JOIN sales s ON c.id = s.contact_id
    WHERE c.assigned_to = ?";
$stats_stmt = mysqli_prepare($conn, $stats_sql);
mysqli_stmt_bind_param($stats_stmt, "i", $user_id);
mysqli_stmt_execute($stats_stmt);
$stats_result = mysqli_stmt_get_result($stats_stmt);
$stats = mysqli_fetch_assoc($stats_result);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="img/minilogo.jpg" type="image/x-icon">
    <title>My Contacts - CRM System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .text-color { color: #FF6500; }
        .bg-color { background-color: #FF6500; }
    </style>
</head>
<body class="bg-gray-100">

<?php include 'includes/navigation.php'; ?>

<!-- Main Content -->
<div class="ml-0 min-[870px]:ml-60 min-h-screen flex flex-col">
    <header class="bg-[#1E3E62] p-4 flex justify-between items-center z-10">
        <h1 class="text-2xl font-bold text-white mx-10 my-2">My Contacts</h1>
        <div class="mx-10">
            <?php include 'includes/top_notification.php'; ?>
        </div>
    </header>
    
    <main class="flex-1 p-10">
        <!-- Success/Error Messages -->
        <?php if (isset($success_message)): ?>
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
            <i class="fas fa-check-circle mr-2"></i><?php echo $success_message; ?>
        </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <i class="fas fa-exclamation-circle mr-2"></i><?php echo $error_message; ?>
        </div>
        <?php endif; ?>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">My Assigned Contacts</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $stats['total_contacts']; ?></p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">My Leads</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $stats['my_leads']; ?></p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">My Customers</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $stats['my_customers']; ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Actions -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                <div class="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
                    <!-- Search -->
                    <form method="GET" class="flex space-x-2">
                        <input type="text" name="search" value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="Search contacts..." 
                               class="px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        
                                                
                        <button type="submit" class="px-4 py-2 bg-[#0b192c] text-white rounded-sm hover:bg-[#1e3e62]">
                            <i class="fas fa-search"></i> Filter
                        </button>

                        <?php if (!empty($search) || !empty($status_filter)): ?>
                        <a href="my_contacts.php" class="px-4 py-2 bg-[#0b192c] text-white rounded-sm hover:bg-[#1e3e62]">
                            <i class="fas fa-times"></i> Clear
                        </a>
                        <?php endif; ?>
                    </form>
                </div>
                
                <div class="flex space-x-2">
                    <!-- "Add Contact" button removed for sales reps -->
                </div>
            </div>
        </div>

        <!-- Bulk Actions -->
        <div id="bulk-actions" class="bg-white rounded-lg shadow p-4 mb-6" style="display: none;">
            <form method="POST" id="bulk-form">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                    <div class="flex items-center space-x-4">
                        <span class="text-sm font-medium text-gray-700">
                            <span id="selected-count">0</span> contact(s) selected
                        </span>

                        <select name="bulk_action" id="bulk_action" class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent" disabled>
                            <option value="">Actions Disabled</option>
                        </select>
                    </div>

                    <div class="flex space-x-2">
                        <button type="submit" class="px-4 py-2 bg-[#0b192c] text-white rounded-sm hover:bg-[#1e3e62]">
                            <i class="fas fa-check"></i> Apply Action
                        </button>
                        <button type="button" onclick="clearSelection()" class="px-4 py-2 bg-[#0b192c] text-white rounded-sm hover:bg-[#1e3e62]">
                            <i class="fas fa-times"></i> Clear Selection
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Contacts Table -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" id="select-all" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">S.No</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Interest</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php
                        $serial_number = $offset + 1;
                        while ($row = mysqli_fetch_assoc($result)):
                        ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" name="selected_contacts[]" value="<?php echo $row['id']; ?>"
                                       class="contact-checkbox h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><?php echo $serial_number++; ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($row['full_name'] ?? ''); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900"><?php echo htmlspecialchars($row['email']); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900"><?php echo htmlspecialchars($row['phone'] ?? 'N/A'); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900"><?php echo htmlspecialchars($row['customer_interest'] ?? 'N/A'); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php
                                // Determine contact type
                                $type = 'Direct';
                                $type_color = 'bg-blue-100 text-blue-800';

                                if (!empty($row['is_referral']) && $row['is_referral']) {
                                    $type = 'Referral';
                                    $type_color = 'bg-purple-100 text-purple-800';
                                } elseif (!empty($row['assigned_to'])) {
                                    $type = 'Assigned';
                                    $type_color = 'bg-green-100 text-green-800';
                                } elseif (empty($row['assigned_to'])) {
                                    $type = 'Unassigned';
                                    $type_color = 'bg-gray-100 text-gray-800';
                                }
                                ?>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $type_color; ?>">
                                    <?php echo $type; ?>
                                </span>
                                <?php if (!empty($row['is_referral']) && $row['is_referral'] && !empty($row['referrer_name'])): ?>
                                <div class="text-xs text-gray-500 mt-1">
                                    by <?php echo htmlspecialchars($row['referrer_name']); ?>
                                </div>
                                <?php endif; ?>
                            </td>
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo date('M j, Y', strtotime($row['created_at'])); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <a href="view_contact.php?id=<?php echo $row['id']; ?>" class="text-blue-600 hover:text-blue-900 mr-3" title="View">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div class="flex-1 flex justify-between sm:hidden">
                    <!-- Mobile pagination -->
                    <?php if ($page > 1): ?>
                    <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>"
                       class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Previous
                    </a>
                    <?php endif; ?>

                    <?php if ($page < $total_pages): ?>
                    <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>"
                       class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Next
                    </a>
                    <?php endif; ?>
                </div>

                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            Showing <span class="font-medium"><?php echo $offset + 1; ?></span> to
                            <span class="font-medium"><?php echo min($offset + $records_per_page, $total_records); ?></span> of
                            <span class="font-medium"><?php echo $total_records; ?></span> results
                        </p>
                    </div>

                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <!-- Previous Page Link -->
                            <?php if ($page > 1): ?>
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>"
                               class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                            <?php else: ?>
                            <span class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-300">
                                <i class="fas fa-chevron-left"></i>
                            </span>
                            <?php endif; ?>

                            <!-- Page Numbers -->
                            <?php
                            $start_page = max(1, $page - 2);
                            $end_page = min($total_pages, $page + 2);

                            // Show first page if not in range
                            if ($start_page > 1): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => 1])); ?>"
                                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">1</a>
                                <?php if ($start_page > 2): ?>
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>
                                <?php endif; ?>
                            <?php endif; ?>

                            <!-- Current page range -->
                            <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                                <?php if ($i == $page): ?>
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600"><?php echo $i; ?></span>
                                <?php else: ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"
                                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"><?php echo $i; ?></a>
                                <?php endif; ?>
                            <?php endfor; ?>

                            <!-- Show last page if not in range -->
                            <?php if ($end_page < $total_pages): ?>
                                <?php if ($end_page < $total_pages - 1): ?>
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>
                                <?php endif; ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $total_pages])); ?>"
                                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"><?php echo $total_pages; ?></a>
                            <?php endif; ?>

                            <!-- Next Page Link -->
                            <?php if ($page < $total_pages): ?>
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>"
                               class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                            <?php else: ?>
                            <span class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-300">
                                <i class="fas fa-chevron-right"></i>
                            </span>
                            <?php endif; ?>
                        </nav>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </main>
</div>

<script>
// Bulk actions functionality
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('select-all');
    const contactCheckboxes = document.querySelectorAll('.contact-checkbox');
    const bulkActionsDiv = document.getElementById('bulk-actions');
    const selectedCountSpan = document.getElementById('selected-count');
    const bulkActionSelect = document.getElementById('bulk_action');
    const bulkForm = document.getElementById('bulk-form');

    // Select all functionality
    selectAllCheckbox.addEventListener('change', function() {
        contactCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActions();
    });

    // Individual checkbox functionality
    contactCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectAllState();
            updateBulkActions();
        });
    });

    // Form submission with confirmation
    bulkForm.addEventListener('submit', function(e) {
        const selectedContacts = document.querySelectorAll('.contact-checkbox:checked');
        const action = bulkActionSelect.value;

        if (selectedContacts.length === 0) {
            e.preventDefault();
            alert('Please select at least one contact.');
            return;
        }

        if (!action) {
            e.preventDefault();
            alert('Please choose an action.');
            return;
        }

        if (action === 'delete') {
            if (!confirm(`Are you sure you want to delete ${selectedContacts.length} contact(s)? This action cannot be undone.`)) {
                e.preventDefault();
                return;
            }
        } else if (action === 'unassign') {
            if (!confirm(`Are you sure you want to unassign ${selectedContacts.length} contact(s) from yourself?`)) {
                e.preventDefault();
                return;
            }
        }
    });

    function updateSelectAllState() {
        const checkedBoxes = document.querySelectorAll('.contact-checkbox:checked');
        const totalBoxes = contactCheckboxes.length;

        if (checkedBoxes.length === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (checkedBoxes.length === totalBoxes) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else {
            selectAllCheckbox.indeterminate = true;
            selectAllCheckbox.checked = false;
        }
    }

    function updateBulkActions() {
        const checkedBoxes = document.querySelectorAll('.contact-checkbox:checked');
        const count = checkedBoxes.length;

        selectedCountSpan.textContent = count;

        if (count > 0) {
            bulkActionsDiv.style.display = 'block';
        } else {
            bulkActionsDiv.style.display = 'none';
            bulkActionSelect.value = '';
        }
    }
});

function clearSelection() {
    document.querySelectorAll('.contact-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    document.getElementById('select-all').checked = false;
    document.getElementById('select-all').indeterminate = false;
    document.getElementById('bulk-actions').style.display = 'none';
    document.getElementById('bulk_action').value = '';
}
</script>

</body>
</html>
