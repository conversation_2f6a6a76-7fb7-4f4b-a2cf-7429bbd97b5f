<?php
// Include database configuration
$conn = require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if the user is logged in
ensure_session_started();
require_login();

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    // Redirect back to products page with error
    header("Location: products.php?error=1&message=" . urlencode("No product ID provided"));
    exit;
}

$product_id = intval($_GET['id']);

// Process form submission for updating a product
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['action']) && $_POST['action'] == 'update_product') {
    $name = sanitize_input($_POST['name']);
    $price = sanitize_input($_POST['price']);
    $category = sanitize_input($_POST['category']);
    
    $sql = "UPDATE products SET 
            name = ?, 
            price = ?, 
            category = ? 
            WHERE id = ?";
    
    // Convert price to integer
    $price = intval($price);
    
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "sdsi", $name, $price, $category, $product_id);
    
    if (mysqli_stmt_execute($stmt)) {
        // Redirect to avoid form resubmission
        header("Location: products.php?success=1&message=" . urlencode("Product updated successfully"));
        exit;
    } else {
        $error_message = "Error: " . mysqli_error($conn);
    }
}

// Get the product data
$sql = "SELECT * FROM products WHERE id = ?";
$stmt = mysqli_prepare($conn, $sql);
mysqli_stmt_bind_param($stmt, "i", $product_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) == 0) {
    // Product doesn't exist
    header("Location: products.php?error=1&message=" . urlencode("Product not found"));
    exit;
}

$product = mysqli_fetch_assoc($result);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="img/minilogo.jpg" type="image/x-icon">
    <title>Edit Product - CRM System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .text-color{ color: #FF6500; }
        .input-focus {
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        .input-focus:focus {
            border-color: #FF6500;
            box-shadow: 0 0 0 3px rgba(255, 101, 0, 0.1);
        }

        .button-hover {
            transition: background-color 0.3s ease, transform 0.2s ease;
        }

        .button-hover:hover {
            background-color: #e55b00;
            transform: scale(1.05);
        }
    </style>
</head>
<body class="">
<?php include 'includes/navigation.php'; ?>

    <!-- Main Content -->
    <div class="ml-0 min-[870px]:ml-60 min-h-screen flex flex-col">
        <!-- Navbar -->
        <header class="bg-[#1E3E62] p-4 flex justify-between z-10 flex-col">
            <h1 class="text-2xl font-bold text-white mx-10 my-2">Edit Product</h1>
                    </header>
        <main class="flex-1 overflow-y-auto space-y-10 p-10">
            <!-- Error message -->
            <?php if (isset($error_message)) : ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                    <strong class="font-bold">Error!</strong>
                    <span class="block sm:inline"><?php echo $error_message; ?></span>
                    <button class="absolute top-0 bottom-0 right-0 px-4 py-3" onclick="this.parentElement.style.display='none'">
                        <svg class="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
                    </button>
                </div>
            <?php endif; ?>

            <!-- Product Edit Form -->
            <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                <div class="px-6 py-5 border-b border-gray-200 sm:px-8" style="background: linear-gradient(to right, #1E3E62, #0B192C);">
                    <h3 class="text-lg leading-6 font-medium text-white">
                        Edit Product Information
                    </h3>
                    <p class="mt-1 max-w-2xl text-sm text-white">
                        Update the product details
                    </p>
                </div>
                <div class="px-4 py-5 sm:p-6">
                    <form id="product-form" class="space-y-4" method="POST" action="edit_product.php?id=<?php echo $product_id; ?>">
                        <input type="hidden" name="action" value="update_product">
                        <div class="grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-2">
                            <div class="sm:col-span-2">
                                <label for="name" class="block text-sm font-medium text-gray-700">Product Name <span class="text-red-500">*</span></label>
                                <input type="text" name="name" id="name" value="<?php echo htmlspecialchars($product['name']); ?>" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md p-2 border input-focus" required>
                            </div>
                            <div>
                                <label for="price" class="block text-sm font-medium text-gray-700">Price <span class="text-red-500">*</span></label>
                                <input type="text" name="price" id="price" pattern="[1-9][0-9]{0,7}" title="Enter a valid price (whole numbers only, no decimals, maximum 8 digits)" value="<?php echo htmlspecialchars(intval($product['price'])); ?>" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md p-2 border input-focus" required>
                            </div>
                            <div>
                                <label for="category" class="block text-sm font-medium text-gray-700">Category <span class="text-red-500">*</span></label>
                                <input type="text" name="category" id="category" value="<?php echo htmlspecialchars($product['category']); ?>" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md p-2 border input-focus" required>
                            </div>
                        </div>
                        <div class="pt-2 flex justify-between">
                            <a href="products.php" class="inline-flex justify-center py-2 px-6 border border-transparent shadow-sm text-sm font-medium rounded-sm text-white bg-[#0b192c] hover:bg-[#1e3e62] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0b192c]">
                                Cancel
                            </a>
                            <button type="submit" class="inline-flex justify-center py-2 px-6 border border-transparent shadow-sm text-sm font-medium rounded-sm text-white bg-[#0b192c] hover:bg-[#1e3e62] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0b192c]" title="Update Product">
                                <i class="fas fa-save"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </main>
    </div>
    <script>
    // Validation for price field
    document.addEventListener('DOMContentLoaded', function() {
        const priceInput = document.getElementById('price');
        const productForm = document.getElementById('product-form');
        
        productForm.addEventListener('submit', function(event) {
            const priceValue = priceInput.value;
            
            // Check if price is a valid whole number (no decimals)
            if (!/^[1-9][0-9]*$/.test(priceValue)) {
                alert('Please enter a valid price (whole numbers only, no decimals).');
                event.preventDefault();
                return;
            }
            
            // Check if price is not larger than 8 digits
            if (priceValue.length > 8) {
                alert('Price cannot be larger than 8 digits!');
                event.preventDefault();
                return;
            }
            
            // Sequential digits check removed as per requirement
        });
    });
    </script>
</body>
</html>
