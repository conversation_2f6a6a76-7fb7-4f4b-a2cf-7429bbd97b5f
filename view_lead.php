<?php
// Get database connection using reliable method
$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';

// Check if database is set up first
require_once 'check_database_setup.php';
redirect_to_setup_if_needed();

// Check if the user is logged in
ensure_session_started();
require_login();

// Get lead ID from URL
$lead_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($lead_id <= 0) {
    header("Location: leads.php");
    exit;
}

// Check if user has permission to view this lead
$is_sales_rep = isset($_SESSION['role']) && $_SESSION['role'] === 'sales_rep';
$sales_rep_id = $is_sales_rep ? $_SESSION['user_id'] : 0;

// Get lead data with related information
$sql = "SELECT c.*,
        CONCAT(COALESCE(c.first_name, ''), ' ', COALESCE(c.last_name, '')) as full_name,
        CONCAT(COALESCE(u.first_name, ''), ' ', COALESCE(u.last_name, '')) as assigned_to_name,
        CONCAT(COALESCE(u2.first_name, ''), ' ', COALESCE(u2.last_name, '')) as created_by_name,
        u.email as assigned_to_email
        FROM contacts c
        LEFT JOIN employees u ON c.assigned_to = u.id
        LEFT JOIN employees u2 ON c.created_by = u2.id
        WHERE c.id = ?";

// Add permission check for sales reps
if ($is_sales_rep) {
    $sql .= " AND c.assigned_to = ?";
}

$stmt = mysqli_prepare($conn, $sql);
if ($is_sales_rep) {
    mysqli_stmt_bind_param($stmt, "ii", $lead_id, $sales_rep_id);
} else {
    mysqli_stmt_bind_param($stmt, "i", $lead_id);
}
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) == 0) {
    header("Location: leads.php?error=Lead not found or access denied");
    exit;
}

$lead = mysqli_fetch_assoc($result);

// Get product interests from customer_interest field
$product_interests_text = $lead['customer_interest'];

// Get sales history for this lead
$sales_sql = "SELECT s.*, p.name as product_name 
              FROM sales s
              LEFT JOIN products p ON s.product_id = p.id
              WHERE s.contact_id = ?
              ORDER BY s.sale_date DESC";
$sales_stmt = mysqli_prepare($conn, $sales_sql);
mysqli_stmt_bind_param($sales_stmt, "i", $lead_id);
mysqli_stmt_execute($sales_stmt);
$sales_result = mysqli_stmt_get_result($sales_stmt);

// Get total sales amount
$total_sales_sql = "SELECT COUNT(*) as count, COALESCE(SUM(total_amount), 0) as total 
                    FROM sales WHERE contact_id = ?";
$total_stmt = mysqli_prepare($conn, $total_sales_sql);
mysqli_stmt_bind_param($total_stmt, "i", $lead_id);
mysqli_stmt_execute($total_stmt);
$total_result = mysqli_stmt_get_result($total_stmt);
$total_data = mysqli_fetch_assoc($total_result);

// Get lead activity/notes history (if you have an activity log table)
// For now, we'll just show the notes from the lead record

$page_title = "View Lead - " . $lead['full_name'];
include __DIR__ . '/includes/header.php';
?>

<script src="https://cdn.tailwindcss.com"></script>
<script>
    tailwind.config = {
        theme: {
            extend: {
                colors: {
                    primary: "#1E3E62",
                    secondary: "#FF6500",
                }
            }
        }
    }
</script>

<?php include __DIR__ . '/includes/navigation.php'; ?>

<!-- Main Content -->
<div class="flex-grow p-4 md:p-8 ml-0 md:ml-60 mt-16">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <div class="flex items-center mb-4 md:mb-0">
                <a href="leads.php" class="text-gray-500 hover:text-gray-700 mr-4">
                    <i class="fas fa-arrow-left text-xl"></i>
                </a>
                <div>
                    <h1 class="text-2xl md:text-3xl font-bold text-gray-800">
                        <?php echo htmlspecialchars($lead['full_name'] ?: 'Unnamed Lead'); ?>
                    </h1>
                    <p class="text-gray-600">Lead Details</p>
                </div>
            </div>
            
            <div class="flex flex-col sm:flex-row gap-3">
                <?php if (!$is_sales_rep || ($is_sales_rep && $lead['assigned_to'] == $sales_rep_id)): ?>
                <a href="edit_lead.php?id=<?php echo $lead['id']; ?>" 
                   class="inline-flex items-center justify-center bg-primary text-white py-2 px-4 rounded-md hover:bg-blue-800 transition-colors">
                    <i class="fas fa-edit mr-2"></i> Edit Lead
                </a>
                <?php endif; ?>
                
                <?php if (!$is_sales_rep): ?>
                <button onclick="showAssignModal()" 
                        class="inline-flex items-center justify-center bg-secondary text-white py-2 px-4 rounded-md hover:bg-orange-600 transition-colors">
                    <i class="fas fa-user-plus mr-2"></i> Reassign
                </button>
                <?php endif; ?>
                
                <a href="create_sale.php?contact_id=<?php echo $lead['id']; ?>" 
                   class="inline-flex items-center justify-center bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors">
                    <i class="fas fa-plus mr-2"></i> Create Sale
                </a>
            </div>
        </div>

        <!-- Lead Status Banner -->
        <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <i class="fas fa-info-circle text-blue-400 mr-3"></i>
                    <div>
                        <p class="text-blue-800 font-medium">Lead Status</p>
                        <p class="text-blue-600 text-sm">
                            <?php if (!empty($lead['assigned_to_name'])): ?>
                                Assigned to <?php echo htmlspecialchars($lead['assigned_to_name']); ?>
                            <?php else: ?>
                                Unassigned Lead
                            <?php endif; ?>
                        </p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="text-blue-800 font-medium">Created</p>
                    <p class="text-blue-600 text-sm"><?php echo date('M j, Y g:i A', strtotime($lead['created_at'])); ?></p>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Lead Information -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-6">Lead Information</h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Basic Information -->
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">Full Name</label>
                            <p class="text-lg text-gray-900"><?php echo htmlspecialchars($lead['full_name'] ?: 'No Name Provided'); ?></p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">Email</label>
                            <p class="text-lg text-gray-900">
                                <a href="mailto:<?php echo htmlspecialchars($lead['email']); ?>" 
                                   class="text-blue-600 hover:text-blue-800 flex items-center">
                                    <i class="fas fa-envelope mr-2"></i>
                                    <?php echo htmlspecialchars($lead['email']); ?>
                                </a>
                            </p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">Phone</label>
                            <p class="text-lg text-gray-900">
                                <?php if (!empty($lead['phone'])): ?>
                                <a href="tel:<?php echo htmlspecialchars($lead['country_code'] . $lead['phone']); ?>" 
                                   class="text-blue-600 hover:text-blue-800 flex items-center">
                                    <i class="fas fa-phone mr-2"></i>
                                    <?php echo htmlspecialchars($lead['country_code'] . ' ' . $lead['phone']); ?>
                                </a>
                                <?php else: ?>
                                <span class="text-gray-500 flex items-center">
                                    <i class="fas fa-phone mr-2"></i>
                                    Not provided
                                </span>
                                <?php endif; ?>
                            </p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">Country</label>
                            <p class="text-lg text-gray-900">
                                <?php echo !empty($lead['country']) ? htmlspecialchars($lead['country']) : 'Not specified'; ?>
                            </p>
                        </div>

                        <!-- Address Information -->
                        <?php if (!empty($lead['address'])): ?>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-600 mb-1">Address</label>
                            <p class="text-lg text-gray-900 bg-gray-50 p-3 rounded-md">
                                <?php echo nl2br(htmlspecialchars($lead['address'])); ?>
                            </p>
                        </div>
                        <?php endif; ?>

                        <!-- Identity Documents -->
                        <?php if (!empty($lead['aadhar_card'])): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">Aadhar Card</label>
                            <p class="text-lg text-gray-900 font-mono"><?php echo htmlspecialchars($lead['aadhar_card']); ?></p>
                        </div>
                        <?php endif; ?>

                        <?php if (!empty($lead['pan_card'])): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">PAN Card</label>
                            <p class="text-lg text-gray-900 font-mono"><?php echo htmlspecialchars($lead['pan_card']); ?></p>
                        </div>
                        <?php endif; ?>

                        <?php if (!empty($lead['category'])): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-600 mb-1">Category</label>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                <?php echo htmlspecialchars($lead['category']); ?>
                            </span>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Product Interests -->
                    <?php if (!empty($product_interests_text)): ?>
                    <div class="mt-6">
                        <label class="block text-sm font-medium text-gray-600 mb-3">Product Interests</label>
                        <div class="bg-gray-50 p-4 rounded-md">
                            <?php
                            $interests = array_map('trim', explode(',', $product_interests_text));
                            foreach ($interests as $index => $interest): ?>
                                <div class="flex items-center py-2 border-b border-gray-200 last:border-b-0">
                                    <span class="bg-primary text-white text-xs font-medium px-2 py-1 rounded-full mr-3 min-w-[24px] text-center">
                                        <?php echo $index + 1; ?>
                                    </span>
                                    <span class="text-gray-800 font-medium">
                                        <?php echo htmlspecialchars($interest); ?>
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Notes Section -->
                    <?php if (!empty($lead['notes'])): ?>
                    <div class="mt-6">
                        <label class="block text-sm font-medium text-gray-600 mb-2">Notes</label>
                        <div class="bg-gray-50 p-4 rounded-md border-l-4 border-yellow-400">
                            <p class="text-gray-800"><?php echo nl2br(htmlspecialchars($lead['notes'])); ?></p>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Sales History -->
                <?php if (mysqli_num_rows($sales_result) > 0): ?>
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-6">Sales History</h2>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php while ($sale = mysqli_fetch_assoc($sales_result)): ?>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo date('M j, Y', strtotime($sale['sale_date'])); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo htmlspecialchars($sale['product_name'] ?: 'N/A'); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                                        $<?php echo number_format($sale['total_amount'], 2); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Completed
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <a href="view_sale.php?id=<?php echo $sale['id']; ?>" class="text-blue-600 hover:text-blue-900">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Quick Stats -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Total Sales</span>
                            <span class="font-semibold text-green-600">$<?php echo number_format($total_data['total'], 2); ?></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Number of Sales</span>
                            <span class="font-semibold text-blue-600"><?php echo $total_data['count']; ?></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Lead Age</span>
                            <span class="font-semibold text-gray-800">
                                <?php 
                                $created = new DateTime($lead['created_at']);
                                $now = new DateTime();
                                $diff = $now->diff($created);
                                echo $diff->days . ' days';
                                ?>
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Assignment Info -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Assignment Details</h3>
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Assigned To</label>
                            <p class="text-gray-900">
                                <?php if (!empty($lead['assigned_to_name'])): ?>
                                    <?php echo htmlspecialchars($lead['assigned_to_name']); ?>
                                    <?php if (!empty($lead['assigned_to_email'])): ?>
                                    <br><a href="mailto:<?php echo htmlspecialchars($lead['assigned_to_email']); ?>" 
                                           class="text-blue-600 hover:text-blue-800 text-sm">
                                        <?php echo htmlspecialchars($lead['assigned_to_email']); ?>
                                    </a>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span class="text-gray-500">Unassigned</span>
                                <?php endif; ?>
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Created By</label>
                            <p class="text-gray-900"><?php echo htmlspecialchars($lead['created_by_name'] ?: 'Unknown'); ?></p>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                    <div class="space-y-3">
                        <a href="mailto:<?php echo htmlspecialchars($lead['email']); ?>" 
                           class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-envelope mr-2"></i>
                            Send Email
                        </a>
                        <?php if (!empty($lead['phone'])): ?>
                        <a href="tel:<?php echo htmlspecialchars($lead['country_code'] . $lead['phone']); ?>" 
                           class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-phone mr-2"></i>
                            Call Lead
                        </a>
                        <?php endif; ?>
                        <button onclick="showFollowUpModal()" 
                                class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-calendar-plus mr-2"></i>
                            Schedule Follow-up
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Assignment Modal -->
<?php if (!$is_sales_rep): ?>
<div id="assignModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Reassign Lead</h3>
            <form action="assign_lead.php" method="POST">
                <input type="hidden" name="lead_id" value="<?php echo $lead['id']; ?>">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Assign to Sales Rep</label>
                    <select name="assigned_to" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Unassigned</option>
                        <?php
                        $employees_sql = "SELECT id, CONCAT(first_name, ' ', last_name) as name FROM employees WHERE role IN ('sales_rep', 'manager')";
                        $employees_result = mysqli_query($conn, $employees_sql);
                        while ($employee = mysqli_fetch_assoc($employees_result)):
                        ?>
                        <option value="<?php echo $employee['id']; ?>" <?php echo $employee['id'] == $lead['assigned_to'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($employee['name']); ?>
                        </option>
                        <?php endwhile; ?>
                    </select>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="hideAssignModal()" 
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-800">
                        Assign
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
function showAssignModal() {
    document.getElementById('assignModal').classList.remove('hidden');
}

function hideAssignModal() {
    document.getElementById('assignModal').classList.add('hidden');
}

function showFollowUpModal() {
    alert('Follow-up scheduling feature coming soon!');
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('assignModal');
    if (event.target == modal) {
        hideAssignModal();
    }
}
</script>

<?php include __DIR__ . '/includes/footer.php'; ?>