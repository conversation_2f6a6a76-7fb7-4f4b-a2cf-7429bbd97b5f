<?php
header('Content-Type: application/json');
require_once '../get_db_connection.php';
function sanitize($value) {
    return htmlspecialchars(strip_tags(trim($value)));
}
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        if (isset($_GET['id'])) {
            $id = intval($_GET['id']);
            $stmt = mysqli_prepare($conn, "SELECT * FROM sales WHERE id = ?");
            mysqli_stmt_bind_param($stmt, 'i', $id);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $sale = mysqli_fetch_assoc($result);
            echo json_encode($sale ?: []);
        } else {
            $result = mysqli_query($conn, "SELECT * FROM sales");
            $sales = [];
            while ($row = mysqli_fetch_assoc($result)) { $sales[] = $row; }
            echo json_encode($sales);
        }
        break;
    case 'POST':
        $data = json_decode(file_get_contents('php://input'), true);
        if (!$data) { http_response_code(400); echo json_encode(['error' => 'Invalid JSON']); exit; }
        $contact_id = intval($data['contact_id'] ?? 0);
        $product_id = intval($data['product_id'] ?? 0);
        $total_amount = floatval($data['total_amount'] ?? 0);
        $sale_date = sanitize($data['sale_date'] ?? '');
        $payment_method = sanitize($data['payment_method'] ?? '');
        $sales_rep_id = intval($data['sales_rep_id'] ?? 0);
        if (!$contact_id || !$product_id || !$sale_date || !$payment_method) {
            http_response_code(400); echo json_encode(['error' => 'Missing required fields']); exit;
        }
        $stmt = mysqli_prepare($conn, "INSERT INTO sales (contact_id, product_id, total_amount, sale_date, payment_method, sales_rep_id) VALUES (?, ?, ?, ?, ?, ?)");
        mysqli_stmt_bind_param($stmt, 'iidssi', $contact_id, $product_id, $total_amount, $sale_date, $payment_method, $sales_rep_id);
        $success = mysqli_stmt_execute($stmt);
        echo json_encode(['success' => $success, 'id' => mysqli_insert_id($conn)]);
        break;
    case 'PUT':
        if (!isset($_GET['id'])) { http_response_code(400); echo json_encode(['error' => 'Missing id']); exit; }
        $id = intval($_GET['id']);
        $data = json_decode(file_get_contents('php://input'), true);
        if (!$data) { http_response_code(400); echo json_encode(['error' => 'Invalid JSON']); exit; }
        $contact_id = intval($data['contact_id'] ?? 0);
        $product_id = intval($data['product_id'] ?? 0);
        $total_amount = floatval($data['total_amount'] ?? 0);
        $sale_date = sanitize($data['sale_date'] ?? '');
        $payment_method = sanitize($data['payment_method'] ?? '');
        $sales_rep_id = intval($data['sales_rep_id'] ?? 0);
        if (!$contact_id || !$product_id || !$sale_date || !$payment_method) {
            http_response_code(400); echo json_encode(['error' => 'Missing required fields']); exit;
        }
        $stmt = mysqli_prepare($conn, "UPDATE sales SET contact_id=?, product_id=?, total_amount=?, sale_date=?, payment_method=?, sales_rep_id=? WHERE id=?");
        mysqli_stmt_bind_param($stmt, 'iidssii', $contact_id, $product_id, $total_amount, $sale_date, $payment_method, $sales_rep_id, $id);
        $success = mysqli_stmt_execute($stmt);
        echo json_encode(['success' => $success]);
        break;
    case 'DELETE':
        if (!isset($_GET['id'])) { http_response_code(400); echo json_encode(['error' => 'Missing id']); exit; }
        $id = intval($_GET['id']);
        $stmt = mysqli_prepare($conn, "DELETE FROM sales WHERE id = ?");
        mysqli_stmt_bind_param($stmt, 'i', $id);
        $success = mysqli_stmt_execute($stmt);
        echo json_encode(['success' => $success]);
        break;
    default:
        http_response_code(405);
        echo json_encode(['error' => 'Method Not Allowed']);
}
