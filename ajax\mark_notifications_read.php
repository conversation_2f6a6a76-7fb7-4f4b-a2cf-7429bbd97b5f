<?php
session_start();

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Not logged in']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['action'])) {
    echo json_encode(['success' => false, 'message' => 'Invalid request']);
    exit;
}

$action = $input['action'];

// Initialize notifications if not set
if (!isset($_SESSION['notifications'])) {
    $_SESSION['notifications'] = [];
}

if ($action === 'mark_all_read') {
    // Mark all notifications as read
    foreach ($_SESSION['notifications'] as &$notification) {
        $notification['is_read'] = true;
    }
    echo json_encode(['success' => true, 'message' => 'All notifications marked as read']);
    
} elseif ($action === 'mark_read_on_view') {
    // Mark notifications as read when dropdown is viewed
    foreach ($_SESSION['notifications'] as &$notification) {
        $notification['is_read'] = true;
    }
    echo json_encode(['success' => true, 'message' => 'Notifications marked as read']);
    
} else {
    echo json_encode(['success' => false, 'message' => 'Unknown action']);
}
?>
