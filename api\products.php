<?php
header('Content-Type: application/json');
require_once '../get_db_connection.php';
function sanitize($value) {
    return htmlspecialchars(strip_tags(trim($value)));
}
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        if (isset($_GET['id'])) {
            $id = intval($_GET['id']);
            $stmt = mysqli_prepare($conn, "SELECT * FROM products WHERE id = ?");
            mysqli_stmt_bind_param($stmt, 'i', $id);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $product = mysqli_fetch_assoc($result);
            echo json_encode($product ?: []);
        } else {
            $result = mysqli_query($conn, "SELECT * FROM products");
            $products = [];
            while ($row = mysqli_fetch_assoc($result)) { $products[] = $row; }
            echo json_encode($products);
        }
        break;
    case 'POST':
        $data = json_decode(file_get_contents('php://input'), true);
        if (!$data) { http_response_code(400); echo json_encode(['error' => 'Invalid JSON']); exit; }
        $name = sanitize($data['name'] ?? '');
        $category = sanitize($data['category'] ?? '');
        $price = floatval($data['price'] ?? 0);
        if (!$name || !$category) {
            http_response_code(400); echo json_encode(['error' => 'Missing required fields']); exit;
        }
        $stmt = mysqli_prepare($conn, "INSERT INTO products (name, category, price) VALUES (?, ?, ?)");
        mysqli_stmt_bind_param($stmt, 'ssd', $name, $category, $price);
        $success = mysqli_stmt_execute($stmt);
        echo json_encode(['success' => $success, 'id' => mysqli_insert_id($conn)]);
        break;
    case 'PUT':
        if (!isset($_GET['id'])) { http_response_code(400); echo json_encode(['error' => 'Missing id']); exit; }
        $id = intval($_GET['id']);
        $data = json_decode(file_get_contents('php://input'), true);
        if (!$data) { http_response_code(400); echo json_encode(['error' => 'Invalid JSON']); exit; }
        $name = sanitize($data['name'] ?? '');
        $category = sanitize($data['category'] ?? '');
        $price = floatval($data['price'] ?? 0);
        if (!$name || !$category) {
            http_response_code(400); echo json_encode(['error' => 'Missing required fields']); exit;
        }
        $stmt = mysqli_prepare($conn, "UPDATE products SET name=?, category=?, price=? WHERE id=?");
        mysqli_stmt_bind_param($stmt, 'ssdi', $name, $category, $price, $id);
        $success = mysqli_stmt_execute($stmt);
        echo json_encode(['success' => $success]);
        break;
    case 'DELETE':
        if (!isset($_GET['id'])) { http_response_code(400); echo json_encode(['error' => 'Missing id']); exit; }
        $id = intval($_GET['id']);
        $stmt = mysqli_prepare($conn, "DELETE FROM products WHERE id = ?");
        mysqli_stmt_bind_param($stmt, 'i', $id);
        $success = mysqli_stmt_execute($stmt);
        echo json_encode(['success' => $success]);
        break;
    default:
        http_response_code(405);
        echo json_encode(['error' => 'Method Not Allowed']);
}
