/**
 * Notifications System
 * 
 * This script handles real-time notifications for the Lead Management System
 */

// Initialize notification system
document.addEventListener('DOMContentLoaded', function() {
    // Get notification elements
    const notificationBell = document.getElementById('notification-bell');
    const notificationCount = document.getElementById('notification-count');
    const notificationDropdown = document.getElementById('notification-dropdown');
    const notificationList = document.getElementById('notification-list');
    
    // Check if notification elements exist
    if (!notificationBell || !notificationDropdown || !notificationList) {
        console.error('Notification elements not found');
        return;
    }
    
    // Toggle notification dropdown
    notificationBell.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        // Toggle dropdown visibility
        notificationDropdown.classList.toggle('hidden');
        
        // If dropdown is visible, mark notifications as read
        if (!notificationDropdown.classList.contains('hidden')) {
            markNotificationsAsRead();
        }
    });
    
    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (notificationDropdown && !notificationDropdown.contains(e.target) && !notificationBell.contains(e.target)) {
            notificationDropdown.classList.add('hidden');
        }
    });
    
    // Function to fetch notifications
    function fetchNotifications() {
        fetch('api/notifications.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateNotifications(data.notifications, data.unread_count);
                } else {
                    console.error('Error fetching notifications:', data.message);
                }
            })
            .catch(error => {
                console.error('Error fetching notifications:', error);
            });
    }
    
    // Function to update notification UI
    function updateNotifications(notifications, unreadCount) {
        // Update notification count
        if (notificationCount) {
            if (unreadCount > 0) {
                notificationCount.textContent = unreadCount;
                notificationCount.classList.remove('hidden');
            } else {
                notificationCount.classList.add('hidden');
            }
        }
        
        // Update notification list
        if (notificationList) {
            // Clear existing notifications
            notificationList.innerHTML = '';
            
            if (notifications.length === 0) {
                // Show no notifications message
                const noNotifications = document.createElement('li');
                noNotifications.className = 'py-2 px-4 text-gray-500 text-center';
                noNotifications.textContent = 'No notifications';
                notificationList.appendChild(noNotifications);
            } else {
                // Add notifications to list
                notifications.forEach(notification => {
                    const li = document.createElement('li');
                    li.className = 'border-b border-gray-100 last:border-0';
                    
                    // Create notification item
                    const notificationItem = document.createElement('a');
                    notificationItem.href = getNotificationLink(notification);
                    notificationItem.className = `block py-2 px-4 hover:bg-gray-50 transition-colors ${notification.is_read ? 'bg-white' : 'bg-blue-50'}`;
                    
                    // Create notification content
                    const content = document.createElement('div');
                    
                    // Add notification title
                    const title = document.createElement('div');
                    title.className = 'font-medium text-gray-900';
                    title.textContent = notification.title;
                    content.appendChild(title);
                    
                    // Add notification message
                    const message = document.createElement('div');
                    message.className = 'text-sm text-gray-500';
                    message.textContent = notification.message;
                    content.appendChild(message);
                    
                    // Add notification time
                    const time = document.createElement('div');
                    time.className = 'text-xs text-gray-400 mt-1';
                    time.textContent = formatNotificationTime(notification.created_at);
                    content.appendChild(time);
                    
                    // Add content to notification item
                    notificationItem.appendChild(content);
                    
                    // Add notification item to list
                    li.appendChild(notificationItem);
                    notificationList.appendChild(li);
                });
            }
        }
    }
    
    // Function to mark notifications as read
    function markNotificationsAsRead() {
        fetch('api/notifications.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ action: 'mark_read' }),
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update UI to reflect read status
                    if (notificationCount) {
                        notificationCount.classList.add('hidden');
                    }
                    
                    // Update notification items in the list
                    const notificationItems = notificationList.querySelectorAll('a');
                    notificationItems.forEach(item => {
                        item.classList.remove('bg-blue-50');
                        item.classList.add('bg-white');
                    });
                } else {
                    console.error('Error marking notifications as read:', data.message);
                }
            })
            .catch(error => {
                console.error('Error marking notifications as read:', error);
            });
    }
    
    // Function to get notification link based on type
    function getNotificationLink(notification) {
        switch (notification.type) {
            case 'lead_created':
                return `view_contact.php?id=${notification.related_id}`;
            case 'lead_assigned':
                return `view_contact.php?id=${notification.related_id}`;
            case 'lead_converted':
                return `view_contact.php?id=${notification.related_id}`;
            case 'lead_followup':
                return `view_contact.php?id=${notification.related_id}`;
            default:
                return '#';
        }
    }
    
    // Function to format notification time
    function formatNotificationTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diffMs = now - date;
        const diffSec = Math.round(diffMs / 1000);
        const diffMin = Math.round(diffSec / 60);
        const diffHour = Math.round(diffMin / 60);
        const diffDay = Math.round(diffHour / 24);
        
        if (diffSec < 60) {
            return 'Just now';
        } else if (diffMin < 60) {
            return `${diffMin} minute${diffMin > 1 ? 's' : ''} ago`;
        } else if (diffHour < 24) {
            return `${diffHour} hour${diffHour > 1 ? 's' : ''} ago`;
        } else if (diffDay < 7) {
            return `${diffDay} day${diffDay > 1 ? 's' : ''} ago`;
        } else {
            return date.toLocaleDateString();
        }
    }
    
    // Fetch notifications on page load
    fetchNotifications();
    
    // Set up polling for new notifications (every 30 seconds)
    setInterval(fetchNotifications, 30000);
});