<?php
// Get database connection using reliable method
$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';

// Check if database is set up first
require_once 'check_database_setup.php';
redirect_to_setup_if_needed();

// Check if the user is logged in
ensure_session_started();
require_login();

$is_sales_rep = isset($_SESSION['role']) && $_SESSION['role'] === 'sales_rep';
$sales_rep_id = $is_sales_rep ? $_SESSION['user_id'] : 0;

// Handle bulk actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['bulk_action'])) {
    $action = $_POST['bulk_action'];
    $selected_leads = isset($_POST['selected_leads']) ? $_POST['selected_leads'] : [];

    if (!empty($selected_leads) && !empty($action)) {
        $lead_ids = array_map('intval', $selected_leads);
        $placeholders = str_repeat('?,', count($lead_ids) - 1) . '?';

        if ($action === 'delete') {
            $delete_sql = "DELETE FROM contacts WHERE id IN ($placeholders) AND status LIKE 'lead_%'";
            $stmt = mysqli_prepare($conn, $delete_sql);
            mysqli_stmt_bind_param($stmt, str_repeat('i', count($lead_ids)), ...$lead_ids);

            if (mysqli_stmt_execute($stmt)) {
                $success_message = count($lead_ids) . " lead(s) deleted successfully.";
            } else {
                $error_message = "Error deleting leads: " . mysqli_error($conn);
            }
        } elseif ($action === 'assign' && isset($_POST['assign_to_user']) && !empty($_POST['assign_to_user'])) {
            $assign_to = intval($_POST['assign_to_user']);
            if ($assign_to > 0) {
                $assign_sql = "UPDATE contacts SET assigned_to = ? WHERE id IN ($placeholders) AND status LIKE 'lead_%'";
                $stmt = mysqli_prepare($conn, $assign_sql);
                $params = array_merge([$assign_to], $lead_ids);
                mysqli_stmt_bind_param($stmt, 'i' . str_repeat('i', count($lead_ids)), ...$params);

                if (mysqli_stmt_execute($stmt)) {
                    $success_message = count($lead_ids) . " lead(s) assigned successfully.";
                    
                    // Get the sales rep details for email notification
                    $sales_rep_sql = "SELECT first_name, last_name, email FROM employees WHERE id = ?";
                    $sales_rep_stmt = mysqli_prepare($conn, $sales_rep_sql);
                    mysqli_stmt_bind_param($sales_rep_stmt, "i", $assign_to);
                    mysqli_stmt_execute($sales_rep_stmt);
                    $sales_rep_result = mysqli_stmt_get_result($sales_rep_stmt);
                    $sales_rep = mysqli_fetch_assoc($sales_rep_result);
                    
                    // Create notification for the sales rep
                    $notification_title = "Leads Assigned";
                    $notification_message = count($lead_ids) . " lead(s) have been assigned to you.";
                    
                    $notify_sql = "INSERT INTO notifications (user_id, title, message, type, related_to, related_id, created_at) 
                                  VALUES (?, ?, ?, 'lead_assigned', 'contacts', NULL, NOW())";
                    $notify_stmt = mysqli_prepare($conn, $notify_sql);
                    mysqli_stmt_bind_param($notify_stmt, "iss", $assign_to, $notification_title, $notification_message);
                    mysqli_stmt_execute($notify_stmt);
                    
                    // Send email notifications to each lead
                    foreach ($lead_ids as $lead_id) {
                        // Get lead details
                        $lead_sql = "SELECT first_name, last_name, email FROM contacts WHERE id = ?";
                        $lead_stmt = mysqli_prepare($conn, $lead_sql);
                        mysqli_stmt_bind_param($lead_stmt, "i", $lead_id);
                        mysqli_stmt_execute($lead_stmt);
                        $lead_result = mysqli_stmt_get_result($lead_stmt);
                        $lead = mysqli_fetch_assoc($lead_result);
                        
                        // Get the lead_assigned email template
                        $template_sql = "SELECT subject, body FROM email_templates WHERE name = 'lead_assigned' LIMIT 1";
                        $template_result = mysqli_query($conn, $template_sql);
                        
                        if ($template = mysqli_fetch_assoc($template_result)) {
                            $subject = $template['subject'];
                            $body = $template['body'];
                            
                            // Replace placeholders with actual data
                            $body = str_replace('{{first_name}}', $lead['first_name'], $body);
                            $body = str_replace('{{sales_rep_name}}', $sales_rep['first_name'] . ' ' . $sales_rep['last_name'], $body);
                            $body = str_replace('{{sales_rep_email}}', $sales_rep['email'], $body);
                            
                            // Send the email
                            $email_result = send_email($lead['email'], $subject, $body);
                            
                            // Log the email
                            $log_status = $email_result['success'] ? 'sent' : 'failed';
                            $log_error = $email_result['success'] ? NULL : $email_result['message'];
                            
                            $log_sql = "INSERT INTO email_logs (recipient, subject, template_id, status, error_message, sent_at) 
                                       VALUES (?, ?, (SELECT id FROM email_templates WHERE name = 'lead_assigned' LIMIT 1), ?, ?, NOW())";
                            $log_stmt = mysqli_prepare($conn, $log_sql);
                            mysqli_stmt_bind_param($log_stmt, "ssss", $lead['email'], $subject, $log_status, $log_error);
                            mysqli_stmt_execute($log_stmt);
                        }
                    }
                } else {
                    $error_message = "Error assigning leads: " . mysqli_error($conn);
                }
            } else {
                $error_message = "Please select a valid user to assign leads to.";
            }
        } elseif ($action === 'convert' && isset($_POST['convert_status'])) {
            $new_status = $_POST['convert_status'];
            
            if ($new_status === 'customer_active') {
                // Convert leads to customers
                $convert_sql = "UPDATE contacts SET status = 'customer_active' WHERE id IN ($placeholders) AND status LIKE 'lead_%'";
                $stmt = mysqli_prepare($conn, $convert_sql);
                mysqli_stmt_bind_param($stmt, str_repeat('i', count($lead_ids)), ...$lead_ids);
                
                if (mysqli_stmt_execute($stmt)) {
                    $success_message = count($lead_ids) . " lead(s) converted to customers successfully.";
                    
                    // Send email notifications to each converted lead
                    foreach ($lead_ids as $lead_id) {
                        // Get lead details
                        $lead_sql = "SELECT c.first_name, c.last_name, c.email, c.assigned_to, 
                                    CONCAT(e.first_name, ' ', e.last_name) as sales_rep_name 
                                    FROM contacts c
                                    LEFT JOIN employees e ON c.assigned_to = e.id
                                    WHERE c.id = ?";
                        $lead_stmt = mysqli_prepare($conn, $lead_sql);
                        mysqli_stmt_bind_param($lead_stmt, "i", $lead_id);
                        mysqli_stmt_execute($lead_stmt);
                        $lead_result = mysqli_stmt_get_result($lead_stmt);
                        $lead = mysqli_fetch_assoc($lead_result);
                        
                        // Create notification for the assigned sales rep
                        if ($lead['assigned_to']) {
                            $notification_title = "Lead Converted";
                            $notification_message = "Lead {$lead['first_name']} {$lead['last_name']} has been converted to a customer.";
                            
                            $notify_sql = "INSERT INTO notifications (user_id, title, message, type, related_to, related_id, created_at) 
                                          VALUES (?, ?, ?, 'lead_converted', 'contacts', ?, NOW())";
                            $notify_stmt = mysqli_prepare($conn, $notify_sql);
                            mysqli_stmt_bind_param($notify_stmt, "issi", $lead['assigned_to'], $notification_title, $notification_message, $lead_id);
                            mysqli_stmt_execute($notify_stmt);
                        }
                        
                        // Get the lead_converted email template
                        $template_sql = "SELECT subject, body FROM email_templates WHERE name = 'lead_converted' LIMIT 1";
                        $template_result = mysqli_query($conn, $template_sql);
                        
                        if ($template = mysqli_fetch_assoc($template_result)) {
                            $subject = $template['subject'];
                            $body = $template['body'];
                            
                            // Replace placeholders with actual data
                            $body = str_replace('{{first_name}}', $lead['first_name'], $body);
                            $body = str_replace('{{sales_rep_name}}', $lead['sales_rep_name'] ?? 'our team', $body);
                            
                            // Send the email
                            $email_result = send_email($lead['email'], $subject, $body);
                            
                            // Log the email
                            $log_status = $email_result['success'] ? 'sent' : 'failed';
                            $log_error = $email_result['success'] ? NULL : $email_result['message'];
                            
                            $log_sql = "INSERT INTO email_logs (recipient, subject, template_id, status, error_message, sent_at) 
                                       VALUES (?, ?, (SELECT id FROM email_templates WHERE name = 'lead_converted' LIMIT 1), ?, ?, NOW())";
                            $log_stmt = mysqli_prepare($conn, $log_sql);
                            mysqli_stmt_bind_param($log_stmt, "ssss", $lead['email'], $subject, $log_status, $log_error);
                            mysqli_stmt_execute($log_stmt);
                        }
                    }
                } else {
                    $error_message = "Error converting leads: " . mysqli_error($conn);
                }
            } else {
                // Update lead status
                $update_sql = "UPDATE contacts SET status = ? WHERE id IN ($placeholders) AND status LIKE 'lead_%'";
                $stmt = mysqli_prepare($conn, $update_sql);
                $params = array_merge([$new_status], $lead_ids);
                mysqli_stmt_bind_param($stmt, 's' . str_repeat('i', count($lead_ids)), ...$params);
                
                if (mysqli_stmt_execute($stmt)) {
                    $status_text = str_replace('lead_', '', $new_status);
                    $status_text = ucfirst($status_text);
                    $success_message = count($lead_ids) . " lead(s) marked as $status_text successfully.";
                } else {
                    $error_message = "Error updating lead status: " . mysqli_error($conn);
                }
            }
        } elseif ($action === 'followup') {
            // Send follow-up emails to selected leads
            $success_count = 0;
            $error_count = 0;
            
            foreach ($lead_ids as $lead_id) {
                // Get lead details
                $lead_sql = "SELECT c.first_name, c.last_name, c.email, c.assigned_to, 
                            CONCAT(e.first_name, ' ', e.last_name) as sales_rep_name 
                            FROM contacts c
                            LEFT JOIN employees e ON c.assigned_to = e.id
                            WHERE c.id = ?";
                $lead_stmt = mysqli_prepare($conn, $lead_sql);
                mysqli_stmt_bind_param($lead_stmt, "i", $lead_id);
                mysqli_stmt_execute($lead_stmt);
                $lead_result = mysqli_stmt_get_result($lead_stmt);
                $lead = mysqli_fetch_assoc($lead_result);
                
                // Get the lead_followup email template
                $template_sql = "SELECT subject, body FROM email_templates WHERE name = 'lead_followup' LIMIT 1";
                $template_result = mysqli_query($conn, $template_sql);
                
                if ($template = mysqli_fetch_assoc($template_result)) {
                    $subject = $template['subject'];
                    $body = $template['body'];
                    
                    // Replace placeholders with actual data
                    $body = str_replace('{{first_name}}', $lead['first_name'], $body);
                    $body = str_replace('{{sales_rep_name}}', $lead['sales_rep_name'] ?? 'our team', $body);
                    
                    // Send the email
                    $email_result = send_email($lead['email'], $subject, $body);
                    
                    // Log the email
                    $log_status = $email_result['success'] ? 'sent' : 'failed';
                    $log_error = $email_result['success'] ? NULL : $email_result['message'];
                    
                    $log_sql = "INSERT INTO email_logs (recipient, subject, template_id, status, error_message, sent_at) 
                               VALUES (?, ?, (SELECT id FROM email_templates WHERE name = 'lead_followup' LIMIT 1), ?, ?, NOW())";
                    $log_stmt = mysqli_prepare($conn, $log_sql);
                    mysqli_stmt_bind_param($log_stmt, "ssss", $lead['email'], $subject, $log_status, $log_error);
                    mysqli_stmt_execute($log_stmt);
                    
                    if ($email_result['success']) {
                        $success_count++;
                    } else {
                        $error_count++;
                    }
                }
                
                // Create notification for the assigned sales rep
                if ($lead['assigned_to']) {
                    $notification_title = "Follow-up Email Sent";
                    $notification_message = "A follow-up email has been sent to {$lead['first_name']} {$lead['last_name']}.";
                    
                    $notify_sql = "INSERT INTO notifications (user_id, title, message, type, related_to, related_id, created_at) 
                                  VALUES (?, ?, ?, 'lead_followup', 'contacts', ?, NOW())";
                    $notify_stmt = mysqli_prepare($conn, $notify_sql);
                    mysqli_stmt_bind_param($notify_stmt, "issi", $lead['assigned_to'], $notification_title, $notification_message, $lead_id);
                    mysqli_stmt_execute($notify_stmt);
                }
            }
            
            if ($success_count > 0) {
                $success_message = "Follow-up emails sent to $success_count lead(s) successfully.";
                if ($error_count > 0) {
                    $success_message .= " $error_count email(s) failed to send.";
                }
            } else {
                $error_message = "Error sending follow-up emails.";
            }
        }
    } else {
        if (empty($selected_leads)) {
            $error_message = "Please select at least one lead.";
        } elseif (empty($action)) {
            $error_message = "Please choose an action.";
        } else {
            $error_message = "Please select leads and choose an action.";
        }
    }
}

// Get filter parameters
$filter = isset($_GET['filter']) ? $_GET['filter'] : '';
$search = isset($_GET['search']) ? $_GET['search'] : '';

// Pagination parameters
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$records_per_page = 10;
$offset = ($page - 1) * $records_per_page;

// Build filtered query conditions
$where_conditions = ["c.status LIKE 'lead_%'"]; // Only show leads, not customers
$params = [];
$param_types = '';

if ($is_sales_rep) {
    $where_conditions[] = "c.assigned_to = ?";
    $params[] = $sales_rep_id;
    $param_types .= 'i';
}

if (!empty($filter)) {
    // Handle different filter types
    switch ($filter) {
        case 'new':
            $where_conditions[] = "c.status = 'lead_new'";
            break;
        case 'contacted':
            $where_conditions[] = "c.status = 'lead_contacted'";
            break;
        case 'qualified':
            $where_conditions[] = "c.status = 'lead_qualified'";
            break;
        case 'lost':
            $where_conditions[] = "c.status = 'lead_lost'";
            break;
        case 'assigned':
            $where_conditions[] = "c.assigned_to IS NOT NULL";
            break;
        case 'unassigned':
            $where_conditions[] = "c.assigned_to IS NULL";
            break;
    }
}

if (!empty($search)) {
    $where_conditions[] = "(c.first_name LIKE ? OR c.last_name LIKE ? OR c.email LIKE ? OR c.phone LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $param_types .= 'ssss';
}

$where_clause = !empty($where_conditions) ? " WHERE " . implode(" AND ", $where_conditions) : "";

// Get total count for pagination
$count_sql = "SELECT COUNT(*) as total FROM contacts c " . $where_clause;
if (!empty($params)) {
    $count_stmt = mysqli_prepare($conn, $count_sql);
    mysqli_stmt_bind_param($count_stmt, $param_types, ...$params);
    mysqli_stmt_execute($count_stmt);
    $count_result = mysqli_stmt_get_result($count_stmt);
} else {
    $count_result = mysqli_query($conn, $count_sql);
}
$total_records = mysqli_fetch_assoc($count_result)['total'];
$total_pages = ceil($total_records / $records_per_page);

// Build main query with pagination
$sql = "SELECT c.*,
        CONCAT(c.first_name, ' ', c.last_name) as full_name,
        CONCAT(u.first_name, ' ', u.last_name) as assigned_to_name,
        CONCAT(u2.first_name, ' ', u2.last_name) as created_by_name
        FROM contacts c
        LEFT JOIN employees u ON c.assigned_to = u.id
        LEFT JOIN employees u2 ON c.created_by = u2.id" .
        $where_clause .
        " ORDER BY c.created_at DESC
        LIMIT ? OFFSET ?";

// Add pagination parameters
$params[] = $records_per_page;
$params[] = $offset;
$param_types .= 'ii';

if (!empty($params)) {
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, $param_types, ...$params);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
} else {
    $result = mysqli_query($conn, $sql);
}

// Get all sales reps for the dropdown
$sales_reps_query = "SELECT id, CONCAT(first_name, ' ', last_name) as name FROM employees WHERE role = 'sales_rep' ORDER BY first_name, last_name";
$sales_reps_result = mysqli_query($conn, $sales_reps_query);
$sales_reps = [];
while ($row = mysqli_fetch_assoc($sales_reps_result)) {
    $sales_reps[] = $row;
}

// Get statistics
if ($is_sales_rep) {
    // Stats for Sales Rep
    $stats_sql = "SELECT 
                 COUNT(*) as total_leads,
                 SUM(CASE WHEN status = 'lead_new' THEN 1 ELSE 0 END) as new_leads,
                 SUM(CASE WHEN status = 'lead_contacted' THEN 1 ELSE 0 END) as contacted_leads,
                 SUM(CASE WHEN status = 'lead_qualified' THEN 1 ELSE 0 END) as qualified_leads
                 FROM contacts WHERE assigned_to = ? AND status LIKE 'lead_%'";
    $stmt = mysqli_prepare($conn, $stats_sql);
    mysqli_stmt_bind_param($stmt, 'i', $sales_rep_id);
    mysqli_stmt_execute($stmt);
    $stats = mysqli_fetch_assoc(mysqli_stmt_get_result($stmt));
} else {
    // Stats for Admin/Manager
    $stats_sql = "SELECT 
                 COUNT(*) as total_leads,
                 SUM(CASE WHEN status = 'lead_new' THEN 1 ELSE 0 END) as new_leads,
                 SUM(CASE WHEN status = 'lead_contacted' THEN 1 ELSE 0 END) as contacted_leads,
                 SUM(CASE WHEN status = 'lead_qualified' THEN 1 ELSE 0 END) as qualified_leads,
                 SUM(CASE WHEN assigned_to IS NOT NULL THEN 1 ELSE 0 END) as assigned_leads,
                 SUM(CASE WHEN assigned_to IS NULL THEN 1 ELSE 0 END) as unassigned_leads
                 FROM contacts WHERE status LIKE 'lead_%'";
    $stats_result = mysqli_query($conn, $stats_sql);
    $stats = mysqli_fetch_assoc($stats_result);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="img/minilogo.jpg" type="image/x-icon">
    <title>Leads Management - CRM System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .text-color { color: #FF6500; }
        .bg-color { background-color: #FF6500; }
        
        /* Status badge styling */
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        .status-new {
            background-color: #e0f2fe;
            color: #0369a1;
        }
        .status-contacted {
            background-color: #fef3c7;
            color: #92400e;
        }
        .status-qualified {
            background-color: #d1fae5;
            color: #065f46;
        }
        .status-lost {
            background-color: #fee2e2;
            color: #b91c1c;
        }
    </style>
</head>
<body class="">

<?php include 'includes/navigation.php'; ?>

<!-- Main Content -->
<div class="ml-0 min-[870px]:ml-60 min-h-screen flex flex-col">
    <header class="bg-[#1E3E62] p-4 flex justify-between z-10 flex-col">
        <h1 class="text-2xl font-bold text-white mx-10 my-2">Leads Management</h1>
    </header>

    <main class="flex-1 overflow-y-auto space-y-10">
        <!-- Statistics Cards -->
        <section class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-10 px-10" style="background: linear-gradient(to bottom, #1E3E62 60%, white 50%);">
            <div class="bg-white p-4 rounded-2xl shadow-xl h-[150px]">
                <i class="fas fa-users text-3xl text-black mb-2"></i>
                <p class="text-sm text-gray-500">Total Leads</p>
                <h2 class="text-xl font-bold mt-2 text-color"><?php echo $stats['total_leads']; ?></h2>
            </div>
            <div class="bg-white p-4 rounded-2xl shadow-xl h-[150px]">
                <i class="fas fa-star text-3xl text-black mb-2"></i>
                <p class="text-sm text-gray-500">New Leads</p>
                <h2 class="text-xl font-bold mt-2 text-color"><?php echo $stats['new_leads']; ?></h2>
            </div>
            <div class="bg-white p-4 rounded-2xl shadow-xl h-[150px]">
                <i class="fas fa-phone text-3xl text-black mb-2"></i>
                <p class="text-sm text-gray-500">Contacted Leads</p>
                <h2 class="text-xl font-bold mt-2 text-color"><?php echo $stats['contacted_leads']; ?></h2>
            </div>
            <div class="bg-white p-4 rounded-2xl shadow-xl h-[150px]">
                <i class="fas fa-check-circle text-3xl text-black mb-2"></i>
                <p class="text-sm text-gray-500">Qualified Leads</p>
                <h2 class="text-xl font-bold mt-2 text-color"><?php echo $stats['qualified_leads']; ?></h2>
            </div>
        </section>
        
        <!-- Leads Management Section -->
        <section class="px-10 mt-10">
            <!-- Success/Error Messages -->
            <?php if (isset($success_message)): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
                    <strong class="font-bold">Success!</strong>
                    <span class="block sm:inline"><?php echo $success_message; ?></span>
                    <button class="absolute top-0 bottom-0 right-0 px-4 py-3" onclick="this.parentElement.style.display='none'">
                        <svg class="fill-current h-6 w-6 text-green-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
                    </button>
                </div>
            <?php endif; ?>
            
            <?php if (isset($error_message)): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
                    <strong class="font-bold">Error!</strong>
                    <span class="block sm:inline"><?php echo $error_message; ?></span>
                    <button class="absolute top-0 bottom-0 right-0 px-4 py-3" onclick="this.parentElement.style.display='none'">
                        <svg class="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
                    </button>
                </div>
            <?php endif; ?>
            
            <!-- Filters and Search -->
            <div class="bg-white p-6 rounded-2xl shadow-xl mb-6">
                <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
                    <h3 class="text-lg font-semibold text-color mb-4 md:mb-0">Leads Management</h3>
                    <div class="flex flex-col sm:flex-row gap-4">
                        <!-- Filter Dropdown -->
                        <div class="relative">
                            <select id="lead-filter" onchange="window.location.href='?filter='+this.value" class="block w-full bg-white border border-gray-300 hover:border-gray-400 px-4 py-2 pr-8 rounded shadow leading-tight focus:outline-none focus:shadow-outline">
                                <option value="" <?php echo $filter === '' ? 'selected' : ''; ?>>All Leads</option>
                                <option value="new" <?php echo $filter === 'new' ? 'selected' : ''; ?>>New Leads</option>
                                <option value="contacted" <?php echo $filter === 'contacted' ? 'selected' : ''; ?>>Contacted</option>
                                <option value="qualified" <?php echo $filter === 'qualified' ? 'selected' : ''; ?>>Qualified</option>
                                <option value="lost" <?php echo $filter === 'lost' ? 'selected' : ''; ?>>Lost</option>
                                <option value="assigned" <?php echo $filter === 'assigned' ? 'selected' : ''; ?>>Assigned</option>
                                <option value="unassigned" <?php echo $filter === 'unassigned' ? 'selected' : ''; ?>>Unassigned</option>
                            </select>
                        </div>
                        
                        <!-- Search Box -->
                        <form action="" method="get" class="flex">
                            <input type="hidden" name="filter" value="<?php echo htmlspecialchars($filter); ?>">
                            <input type="text" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="Search leads..." class="w-full sm:w-64 px-4 py-2 border border-gray-300 rounded-l focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <button type="submit" class="bg-[#1E3E62] text-white px-4 py-2 rounded-r hover:bg-[#0B192C] focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>
                    </div>
                </div>
                
                <!-- Bulk Actions Form -->
                <form action="" method="post" id="bulk-actions-form">
                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-white">
                            <thead>
                                <tr class="bg-gray-100 text-gray-600 uppercase text-sm leading-normal">
                                    <th class="py-3 px-6 text-left">
                                        <input type="checkbox" id="select-all" class="form-checkbox h-5 w-5 text-blue-600">
                                    </th>
                                    <th class="py-3 px-6 text-left">Name</th>
                                    <th class="py-3 px-6 text-left">Contact</th>
                                    <th class="py-3 px-6 text-left">Interest</th>
                                    <th class="py-3 px-6 text-left">Status</th>
                                    <th class="py-3 px-6 text-left">Assigned To</th>
                                    <th class="py-3 px-6 text-left">Created</th>
                                    <th class="py-3 px-6 text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="text-gray-600 text-sm">
                                <?php if (mysqli_num_rows($result) > 0): ?>
                                    <?php while ($row = mysqli_fetch_assoc($result)): ?>
                                        <tr class="border-b border-gray-200 hover:bg-gray-50">
                                            <td class="py-3 px-6 text-left">
                                                <input type="checkbox" name="selected_leads[]" value="<?php echo $row['id']; ?>" class="lead-checkbox form-checkbox h-5 w-5 text-blue-600">
                                            </td>
                                            <td class="py-3 px-6 text-left">
                                                <div class="flex items-center">
                                                    <div class="mr-2">
                                                        <i class="fas fa-user text-gray-400"></i>
                                                    </div>
                                                    <span><?php echo htmlspecialchars($row['full_name']); ?></span>
                                                </div>
                                            </td>
                                            <td class="py-3 px-6 text-left">
                                                <div>
                                                    <div class="text-sm"><?php echo htmlspecialchars($row['email']); ?></div>
                                                    <div class="text-xs text-gray-500"><?php echo htmlspecialchars($row['phone']); ?></div>
                                                </div>
                                            </td>
                                            <td class="py-3 px-6 text-left">
                                                <?php echo htmlspecialchars($row['customer_interest'] ?? 'N/A'); ?>
                                            </td>
                                            <td class="py-3 px-6 text-left">
                                                <?php 
                                                $status_class = '';
                                                $status_text = '';
                                                
                                                switch ($row['status']) {
                                                    case 'lead_new':
                                                        $status_class = 'status-new';
                                                        $status_text = 'New';
                                                        break;
                                                    case 'lead_contacted':
                                                        $status_class = 'status-contacted';
                                                        $status_text = 'Contacted';
                                                        break;
                                                    case 'lead_qualified':
                                                        $status_class = 'status-qualified';
                                                        $status_text = 'Qualified';
                                                        break;
                                                    case 'lead_lost':
                                                        $status_class = 'status-lost';
                                                        $status_text = 'Lost';
                                                        break;
                                                    default:
                                                        $status_class = '';
                                                        $status_text = 'Unknown';
                                                }
                                                ?>
                                                <span class="status-badge <?php echo $status_class; ?>">
                                                    <?php echo $status_text; ?>
                                                </span>
                                            </td>
                                            <td class="py-3 px-6 text-left">
                                                <?php echo $row['assigned_to_name'] ? htmlspecialchars($row['assigned_to_name']) : '<span class="text-gray-400">Unassigned</span>'; ?>
                                            </td>
                                            <td class="py-3 px-6 text-left">
                                                <?php echo date('M d, Y', strtotime($row['created_at'])); ?>
                                            </td>
                                            <td class="py-3 px-6 text-center">
                                                <div class="flex item-center justify-center">
                                                    <a href="view_contact.php?id=<?php echo $row['id']; ?>" class="w-4 mr-2 transform hover:text-blue-500 hover:scale-110 transition-all" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="edit_contact.php?id=<?php echo $row['id']; ?>" class="w-4 mr-2 transform hover:text-yellow-500 hover:scale-110 transition-all" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="delete_contact.php?id=<?php echo $row['id']; ?>" class="w-4 mr-2 transform hover:text-red-500 hover:scale-110 transition-all" title="Delete" onclick="return confirm('Are you sure you want to delete this lead?');">
                                                        <i class="fas fa-trash-alt"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="8" class="py-6 text-center text-gray-500">No leads found</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Bulk Actions Controls -->
                    <div class="mt-4 flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
                        <div class="flex-1">
                            <label for="bulk-action" class="block text-sm font-medium text-gray-700 mb-1">Bulk Action</label>
                            <select id="bulk-action" name="bulk_action" class="block w-full bg-white border border-gray-300 hover:border-gray-400 px-4 py-2 rounded shadow leading-tight focus:outline-none focus:shadow-outline">
                                <option value="">Select Action</option>
                                <option value="assign">Assign to Sales Rep</option>
                                <option value="convert">Convert Status</option>
                                <option value="followup">Send Follow-up Email</option>
                                <option value="delete">Delete</option>
                            </select>
                        </div>
                        
                        <!-- Assign To (conditionally shown) -->
                        <div id="assign-to-container" class="hidden flex-1">
                            <label for="assign-to-user" class="block text-sm font-medium text-gray-700 mb-1">Assign To</label>
                            <select id="assign-to-user" name="assign_to_user" class="block w-full bg-white border border-gray-300 hover:border-gray-400 px-4 py-2 rounded shadow leading-tight focus:outline-none focus:shadow-outline">
                                <option value="">Select Sales Rep</option>
                                <?php foreach ($sales_reps as $rep): ?>
                                    <option value="<?php echo $rep['id']; ?>"><?php echo htmlspecialchars($rep['name']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <!-- Convert Status (conditionally shown) -->
                        <div id="convert-status-container" class="hidden flex-1">
                            <label for="convert-status" class="block text-sm font-medium text-gray-700 mb-1">Convert To</label>
                            <select id="convert-status" name="convert_status" class="block w-full bg-white border border-gray-300 hover:border-gray-400 px-4 py-2 rounded shadow leading-tight focus:outline-none focus:shadow-outline">
                                <option value="lead_contacted">Contacted</option>
                                <option value="lead_qualified">Qualified</option>
                                <option value="lead_lost">Lost</option>
                                <option value="customer_active">Customer</option>
                            </select>
                        </div>
                        
                        <div class="flex-none self-end">
                            <button type="submit" class="bg-[#1E3E62] text-white px-4 py-2 rounded hover:bg-[#0B192C] focus:outline-none focus:ring-2 focus:ring-blue-500">
                                Apply
                            </button>
                        </div>
                    </div>
                </form>
                
                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <div class="mt-6 flex justify-center">
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <?php if ($page > 1): ?>
                                <a href="?page=<?php echo $page - 1; ?>&filter=<?php echo urlencode($filter); ?>&search=<?php echo urlencode($search); ?>" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">Previous</span>
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            <?php endif; ?>
                            
                            <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                <a href="?page=<?php echo $i; ?>&filter=<?php echo urlencode($filter); ?>&search=<?php echo urlencode($search); ?>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium <?php echo $i === $page ? 'text-blue-600 bg-blue-50' : 'text-gray-700 hover:bg-gray-50'; ?>">
                                    <?php echo $i; ?>
                                </a>
                            <?php endfor; ?>
                            
                            <?php if ($page < $total_pages): ?>
                                <a href="?page=<?php echo $page + 1; ?>&filter=<?php echo urlencode($filter); ?>&search=<?php echo urlencode($search); ?>" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">Next</span>
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            <?php endif; ?>
                        </nav>
                    </div>
                <?php endif; ?>
            </div>
        </section>
    </main>
</div>

<script>
    // Select all checkbox functionality
    document.getElementById('select-all').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.lead-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });
    
    // Show/hide conditional fields based on bulk action selection
    document.getElementById('bulk-action').addEventListener('change', function() {
        const assignContainer = document.getElementById('assign-to-container');
        const convertContainer = document.getElementById('convert-status-container');
        
        // Hide all conditional containers first
        assignContainer.classList.add('hidden');
        convertContainer.classList.add('hidden');
        
        // Show the appropriate container based on selection
        if (this.value === 'assign') {
            assignContainer.classList.remove('hidden');
        } else if (this.value === 'convert') {
            convertContainer.classList.remove('hidden');
        }
    });
</script>
</body>
</html>