<?php
// Get database connection using reliable method
$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';
require_once 'includes/validation.php';

// Check if database is set up first
require_once 'check_database_setup.php';
redirect_to_setup_if_needed();

// Check if the user is logged in
ensure_session_started();
require_login();

$error_message = '';
$success_message = '';

// Initialize form variables
$first_name = $last_name = $email = $phone = $country_code = $company = $customer_interest = $source = $notes = '';
$assigned_to = null;

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $first_name = sanitize_input($_POST['first_name']);
    $last_name = sanitize_input($_POST['last_name']);
    $email = sanitize_input($_POST['email']);
    $phone = sanitize_input($_POST['phone']);
    $country_code = sanitize_input($_POST['country_code']);
    $company = sanitize_input($_POST['company']);
    $customer_interest = sanitize_input($_POST['customer_interest']);
    $source = sanitize_input($_POST['source']);
    $notes = sanitize_input($_POST['notes']);
    $assigned_to = !empty($_POST['assigned_to']) ? intval($_POST['assigned_to']) : null;
    
    // Validation
    $errors = [];
    
    if (empty($first_name)) {
        $errors[] = "First name is required.";
    }
    
    if (empty($last_name)) {
        $errors[] = "Last name is required.";
    }
    
    if (empty($email)) {
        $errors[] = "Email is required.";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Invalid email format.";
    } else {
        // Check if email exists in leads table
        $email_check_sql = "SELECT id FROM leads WHERE email = ?";
        $email_stmt = mysqli_prepare($conn, $email_check_sql);
        mysqli_stmt_bind_param($email_stmt, "s", $email);
        mysqli_stmt_execute($email_stmt);
        $email_result = mysqli_stmt_get_result($email_stmt);
        
        if (mysqli_num_rows($email_result) > 0) {
            $errors[] = "Email already exists in leads.";
        }
        
        // Also check contacts table
        $contact_check_sql = "SELECT id FROM contacts WHERE email = ?";
        $contact_stmt = mysqli_prepare($conn, $contact_check_sql);
        mysqli_stmt_bind_param($contact_stmt, "s", $email);
        mysqli_stmt_execute($contact_stmt);
        $contact_result = mysqli_stmt_get_result($contact_stmt);
        
        if (mysqli_num_rows($contact_result) > 0) {
            $errors[] = "Email already exists as a customer.";
        }
    }
    
    if (!empty($phone) && !preg_match("/^[0-9]{10}$/", $phone)) {
        $errors[] = "Phone number must be 10 digits.";
    }
    
    if (empty($errors)) {
        // Insert new lead
        $insert_sql = "INSERT INTO leads (first_name, last_name, email, phone, country_code, 
                      company, customer_interest, source, notes, assigned_to, created_by, created_at) 
                      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
        
        $insert_stmt = mysqli_prepare($conn, $insert_sql);
        mysqli_stmt_bind_param($insert_stmt, "sssssssssii", 
            $first_name, $last_name, $email, $phone, $country_code, 
            $company, $customer_interest, $source, $notes, $assigned_to, $_SESSION['user_id']);
        
        if (mysqli_stmt_execute($insert_stmt)) {
            $new_lead_id = mysqli_insert_id($conn);
            
            // Create notification for assigned user if assigned
            if ($assigned_to) {
                $notification_title = "New Lead Assigned";
                $notification_message = "You have been assigned a new lead: " . $first_name . " " . $last_name;
                $notification_link = "view_lead.php?id=" . $new_lead_id;
                
                $notify_sql = "INSERT INTO notifications (user_id, title, message, link, type, created_at) 
                              VALUES (?, ?, ?, ?, 'info', NOW())";
                $notify_stmt = mysqli_prepare($conn, $notify_sql);
                mysqli_stmt_bind_param($notify_stmt, "isss", $assigned_to, $notification_title, $notification_message, $notification_link);
                mysqli_stmt_execute($notify_stmt);
            }
            
            $success_message = "Lead added successfully!";
            
            // Reset form
            $first_name = $last_name = $email = $phone = $company = $customer_interest = $source = $notes = '';
            $assigned_to = null;
            $country_code = '+91';
        } else {
            $error_message = "Error adding lead: " . mysqli_error($conn);
        }
    } else {
        $error_message = implode("<br>", $errors);
    }
}

// Get all employees for assignment dropdown
$employees_sql = "SELECT id, CONCAT(first_name, ' ', last_name) as name FROM employees WHERE role IN ('sales_rep', 'manager') ORDER BY first_name, last_name";
$employees_result = mysqli_query($conn, $employees_sql);

// Get all products for interest dropdown
$products_sql = "SELECT id, name, price, category FROM products ORDER BY category, name";
$products_result = mysqli_query($conn, $products_sql);

$page_title = "Add New Lead";
include __DIR__ . '/includes/header.php';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="img/minilogo.jpg" type="image/x-icon">
    <title>Add New Lead - CRM System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">

<?php include 'includes/navigation.php'; ?>

<!-- Main Content -->
<div class="ml-0 min-[870px]:ml-60 min-h-screen flex flex-col">
    <header class="bg-[#1E3E62] p-4 flex justify-between items-center z-10">
        <h1 class="text-2xl font-bold text-white mx-10 my-2">Add New Lead</h1>
        <div class="mx-10">
            <?php include 'includes/top_notification.php'; ?>
        </div>
    </header>

    <main class="flex-1 p-10">
        <!-- Back Button -->
        <div class="mb-6">
            <a href="leads.php" class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Leads
            </a>
        </div>

        <!-- Success/Error Messages -->
        <?php if (!empty($success_message)): ?>
            <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                <?php echo $success_message; ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($error_message)): ?>
            <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                <?php echo $error_message; ?>
            </div>
        <?php endif; ?>

        <!-- Add Lead Form -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <form method="POST" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- First Name -->
                    <div>
                        <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2">First Name *</label>
                        <input type="text" id="first_name" name="first_name" value="<?php echo htmlspecialchars($first_name); ?>" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                    </div>

                    <!-- Last Name -->
                    <div>
                        <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2">Last Name *</label>
                        <input type="text" id="last_name" name="last_name" value="<?php echo htmlspecialchars($last_name); ?>" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                    </div>

                    <!-- Email -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email *</label>
                        <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($email); ?>" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                    </div>

                    <!-- Phone -->
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                        <div class="flex">
                            <select name="country_code" class="px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="+91" <?php echo ($country_code == '+91') ? 'selected' : ''; ?>>+91</option>
                                <option value="+1" <?php echo ($country_code == '+1') ? 'selected' : ''; ?>>+1</option>
                                <option value="+44" <?php echo ($country_code == '+44') ? 'selected' : ''; ?>>+44</option>
                            </select>
                            <input type="text" id="phone" name="phone" value="<?php echo htmlspecialchars($phone); ?>" 
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-r-md focus:outline-none focus:ring-2 focus:ring-blue-500" 
                                   pattern="[0-9]{10}" title="Please enter a 10-digit phone number">
                        </div>
                    </div>
                </div>

                <!-- Company -->
                <div>
                    <label for="company" class="block text-sm font-medium text-gray-700 mb-2">Company</label>
                    <input type="text" id="company" name="company" value="<?php echo htmlspecialchars($company); ?>" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Interest -->
                    <div>
                        <label for="customer_interest" class="block text-sm font-medium text-gray-700 mb-2">Interest</label>
                        <select id="customer_interest" name="customer_interest" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">Select interest</option>
                            <?php
                            if ($products_result && mysqli_num_rows($products_result) > 0) {
                                $current_category = '';
                                while ($product = mysqli_fetch_assoc($products_result)) {
                                    if ($current_category != $product['category']) {
                                        if ($current_category != '') echo '</optgroup>';
                                        echo '<optgroup label="' . htmlspecialchars($product['category']) . '">';
                                        $current_category = $product['category'];
                                    }
                                    $selected = ($customer_interest == $product['name']) ? 'selected' : '';
                                    echo '<option value="' . htmlspecialchars($product['name']) . '" ' . $selected . '>' . 
                                         htmlspecialchars($product['name']) . ' - ₹' . number_format($product['price'], 2) . '</option>';
                                }
                                if ($current_category != '') echo '</optgroup>';
                            }
                            ?>
                            <option value="General Inquiry" <?php echo ($customer_interest == 'General Inquiry') ? 'selected' : ''; ?>>General Inquiry</option>
                        </select>
                    </div>

                    <!-- Source -->
                    <div>
                        <label for="source" class="block text-sm font-medium text-gray-700 mb-2">Source</label>
                        <select id="source" name="source" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="Web Form" <?php echo ($source == 'Web Form') ? 'selected' : ''; ?>>Web Form</option>
                            <option value="Phone Call" <?php echo ($source == 'Phone Call') ? 'selected' : ''; ?>>Phone Call</option>
                            <option value="Email" <?php echo ($source == 'Email') ? 'selected' : ''; ?>>Email</option>
                            <option value="Referral" <?php echo ($source == 'Referral') ? 'selected' : ''; ?>>Referral</option>
                            <option value="Social Media" <?php echo ($source == 'Social Media') ? 'selected' : ''; ?>>Social Media</option>
                            <option value="Advertisement" <?php echo ($source == 'Advertisement') ? 'selected' : ''; ?>>Advertisement</option>
                            <option value="Walk-in" <?php echo ($source == 'Walk-in') ? 'selected' : ''; ?>>Walk-in</option>
                            <option value="Other" <?php echo ($source == 'Other') ? 'selected' : ''; ?>>Other</option>
                        </select>
                    </div>
                </div>

                <!-- Assigned To -->
                <div>
                    <label for="assigned_to" class="block text-sm font-medium text-gray-700 mb-2">Assign To</label>
                    <select id="assigned_to" name="assigned_to" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Unassigned</option>
                        <?php while ($employee = mysqli_fetch_assoc($employees_result)): ?>
                            <option value="<?php echo $employee['id']; ?>" <?php echo ($assigned_to == $employee['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($employee['name']); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>

                <!-- Notes -->
                <div>
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                    <textarea id="notes" name="notes" rows="4" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo htmlspecialchars($notes); ?></textarea>
                </div>

                <!-- Submit Buttons -->
                <div class="flex justify-end space-x-4">
                    <a href="leads.php" class="px-6 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors">
                        Cancel
                    </a>
                    <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                        Add Lead
                    </button>
                </div>
            </form>
        </div>
    </main>
</div>

</body>
</html>
