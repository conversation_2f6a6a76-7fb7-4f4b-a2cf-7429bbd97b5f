[02-Jul-2025 12:06:12 Asia/Kolkata] PHP Fatal error:  Uncaught ArgumentCountError: The number of elements in the type definition string must match the number of bind variables in C:\xampp\htdocs\LEADManagement\edit_contact.php:158
Stack trace:
#0 C:\xampp\htdocs\LEADManagement\edit_contact.php(158): mysqli_stmt_bind_param(Object(mysqli_stmt), 'ssssisssiissssi', 'Test', 'User', 'pratham59.gurug...', '9971274749', NULL, '', '', '', 0, NULL, '', '', '', 7)
#1 {main}
  thrown in C:\xampp\htdocs\LEADManagement\edit_contact.php on line 158
[02-Jul-2025 12:10:04 Asia/Kolkata] Email Template Usage: {"timestamp":"2025-07-02 12:10:04","template":"new_product","recipient_type":"customer","success":true}
[02-Jul-2025 12:10:08 Asia/Kolkata] Email Template Usage: {"timestamp":"2025-07-02 12:10:08","template":"new_product","recipient_type":"customer","success":true}
[02-Jul-2025 12:10:13 Asia/Kolkata] Email Template Usage: {"timestamp":"2025-07-02 12:10:13","template":"new_product","recipient_type":"customer","success":true}
[02-Jul-2025 12:10:17 Asia/Kolkata] Email Template Usage: {"timestamp":"2025-07-02 12:10:17","template":"new_product","recipient_type":"customer","success":true}
[02-Jul-2025 12:10:21 Asia/Kolkata] Email Template Usage: {"timestamp":"2025-07-02 12:10:21","template":"new_product","recipient_type":"customer","success":true}
[02-Jul-2025 12:10:25 Asia/Kolkata] Email Template Usage: {"timestamp":"2025-07-02 12:10:25","template":"new_product","recipient_type":"customer","success":true}
[02-Jul-2025 12:10:29 Asia/Kolkata] Email Template Usage: {"timestamp":"2025-07-02 12:10:29","template":"new_product","recipient_type":"customer","success":true}
[02-Jul-2025 12:10:29 Asia/Kolkata] Product Email Notifications - Total Sent: 7, Failed: 0
[02-Jul-2025 12:10:33 Asia/Kolkata] Email Template Usage: {"timestamp":"2025-07-02 12:10:33","template":"new_product","recipient_type":"customer","success":true}
[02-Jul-2025 12:10:37 Asia/Kolkata] Email Template Usage: {"timestamp":"2025-07-02 12:10:37","template":"new_product","recipient_type":"customer","success":true}
[02-Jul-2025 12:10:41 Asia/Kolkata] Email Template Usage: {"timestamp":"2025-07-02 12:10:41","template":"new_product","recipient_type":"customer","success":true}
[02-Jul-2025 12:10:45 Asia/Kolkata] Email Template Usage: {"timestamp":"2025-07-02 12:10:45","template":"new_product","recipient_type":"customer","success":true}
[02-Jul-2025 12:10:49 Asia/Kolkata] Email Template Usage: {"timestamp":"2025-07-02 12:10:49","template":"new_product","recipient_type":"customer","success":true}
[02-Jul-2025 12:10:53 Asia/Kolkata] Email Template Usage: {"timestamp":"2025-07-02 12:10:53","template":"new_product","recipient_type":"customer","success":true}
[02-Jul-2025 12:10:58 Asia/Kolkata] Email Template Usage: {"timestamp":"2025-07-02 12:10:58","template":"new_product","recipient_type":"customer","success":true}
[02-Jul-2025 12:11:03 Asia/Kolkata] Email Template Usage: {"timestamp":"2025-07-02 12:11:03","template":"new_product","recipient_type":"customer","success":true}
[02-Jul-2025 12:11:03 Asia/Kolkata] Product Email Notifications - Total Sent: 7, Failed: 0
[02-Jul-2025 12:11:26 Asia/Kolkata] Email Template Usage: {"timestamp":"2025-07-02 12:11:26","template":"new_product","recipient_type":"customer","success":true}
[02-Jul-2025 12:11:30 Asia/Kolkata] Email Template Usage: {"timestamp":"2025-07-02 12:11:30","template":"new_product","recipient_type":"customer","success":true}
[02-Jul-2025 12:11:35 Asia/Kolkata] Email Template Usage: {"timestamp":"2025-07-02 12:11:35","template":"new_product","recipient_type":"customer","success":true}
[02-Jul-2025 12:11:39 Asia/Kolkata] Email Template Usage: {"timestamp":"2025-07-02 12:11:39","template":"new_product","recipient_type":"customer","success":true}
[02-Jul-2025 12:11:43 Asia/Kolkata] Email Template Usage: {"timestamp":"2025-07-02 12:11:43","template":"new_product","recipient_type":"customer","success":true}
[02-Jul-2025 12:11:47 Asia/Kolkata] Email Template Usage: {"timestamp":"2025-07-02 12:11:47","template":"new_product","recipient_type":"customer","success":true}
[02-Jul-2025 12:11:51 Asia/Kolkata] Email Template Usage: {"timestamp":"2025-07-02 12:11:51","template":"new_product","recipient_type":"customer","success":true}
[02-Jul-2025 12:11:52 Asia/Kolkata] Product Email Notifications - Total Sent: 7, Failed: 0
[02-Jul-2025 12:11:56 Asia/Kolkata] Email Template Usage: {"timestamp":"2025-07-02 12:11:56","template":"new_product","recipient_type":"customer","success":true}
[02-Jul-2025 12:12:00 Asia/Kolkata] Email Template Usage: {"timestamp":"2025-07-02 12:12:00","template":"new_product","recipient_type":"customer","success":true}
[02-Jul-2025 12:12:04 Asia/Kolkata] Email Template Usage: {"timestamp":"2025-07-02 12:12:04","template":"new_product","recipient_type":"customer","success":true}
[02-Jul-2025 12:12:08 Asia/Kolkata] Email Template Usage: {"timestamp":"2025-07-02 12:12:08","template":"new_product","recipient_type":"customer","success":true}
[02-Jul-2025 12:12:12 Asia/Kolkata] Email Template Usage: {"timestamp":"2025-07-02 12:12:12","template":"new_product","recipient_type":"customer","success":true}
[02-Jul-2025 12:12:17 Asia/Kolkata] Email Template Usage: {"timestamp":"2025-07-02 12:12:17","template":"new_product","recipient_type":"customer","success":true}
[02-Jul-2025 12:12:21 Asia/Kolkata] Email Template Usage: {"timestamp":"2025-07-02 12:12:21","template":"new_product","recipient_type":"customer","success":true}
[02-Jul-2025 12:12:21 Asia/Kolkata] Product Email Notifications - Total Sent: 7, Failed: 0
