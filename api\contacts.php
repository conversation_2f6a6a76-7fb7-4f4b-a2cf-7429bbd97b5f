<?php
header('Content-Type: application/json');
require_once '../get_db_connection.php';
require_once '../includes/functions.php';

function sanitize($value) {
    return htmlspecialchars(strip_tags(trim($value)));
}

$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        // GET /api/contacts or /api/contacts?id=1
        if (isset($_GET['id'])) {
            $id = intval($_GET['id']);
            $stmt = mysqli_prepare($conn, "SELECT * FROM contacts WHERE id = ?");
            mysqli_stmt_bind_param($stmt, 'i', $id);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $contact = mysqli_fetch_assoc($result);
            echo json_encode($contact ?: []);
        } else {
            $result = mysqli_query($conn, "SELECT * FROM contacts");
            $contacts = [];
            while ($row = mysqli_fetch_assoc($result)) {
                $contacts[] = $row;
            }
            echo json_encode($contacts);
        }
        break;
    case 'POST':
        // POST /api/contacts (create)
        $data = json_decode(file_get_contents('php://input'), true);
        if (!$data) { http_response_code(400); echo json_encode(['error' => 'Invalid JSON']); exit; }
        $first_name = sanitize($data['first_name'] ?? '');
        $last_name = sanitize($data['last_name'] ?? '');
        $email = filter_var($data['email'] ?? '', FILTER_VALIDATE_EMAIL) ? $data['email'] : '';
        $phone = sanitize($data['phone'] ?? '');
        $address = sanitize($data['address'] ?? '');
        $customer_interest = sanitize($data['customer_interest'] ?? '');
        if (!$first_name || !$last_name || !$email) {
            http_response_code(400); echo json_encode(['error' => 'Missing required fields']); exit;
        }
        $stmt = mysqli_prepare($conn, "INSERT INTO contacts (first_name, last_name, email, phone, address, customer_interest) VALUES (?, ?, ?, ?, ?, ?)");
        mysqli_stmt_bind_param($stmt, 'ssssss', $first_name, $last_name, $email, $phone, $address, $customer_interest);
        $success = mysqli_stmt_execute($stmt);
        if ($success) {
            echo json_encode(['success' => true, 'id' => mysqli_insert_id($conn)]);
        } else {
            http_response_code(500); echo json_encode(['error' => 'Insert failed']);
        }
        break;
    case 'PUT':
        // PUT /api/contacts?id=1 (update)
        if (!isset($_GET['id'])) { http_response_code(400); echo json_encode(['error' => 'Missing id']); exit; }
        $id = intval($_GET['id']);
        $data = json_decode(file_get_contents('php://input'), true);
        if (!$data) { http_response_code(400); echo json_encode(['error' => 'Invalid JSON']); exit; }
        $first_name = sanitize($data['first_name'] ?? '');
        $last_name = sanitize($data['last_name'] ?? '');
        $email = filter_var($data['email'] ?? '', FILTER_VALIDATE_EMAIL) ? $data['email'] : '';
        $phone = sanitize($data['phone'] ?? '');
        $address = sanitize($data['address'] ?? '');
        $customer_interest = sanitize($data['customer_interest'] ?? '');
        if (!$first_name || !$last_name || !$email) {
            http_response_code(400); echo json_encode(['error' => 'Missing required fields']); exit;
        }
        $stmt = mysqli_prepare($conn, "UPDATE contacts SET first_name=?, last_name=?, email=?, phone=?, address=?, customer_interest=? WHERE id=?");
        mysqli_stmt_bind_param($stmt, 'ssssssi', $first_name, $last_name, $email, $phone, $address, $customer_interest, $id);
        $success = mysqli_stmt_execute($stmt);
        echo json_encode(['success' => $success]);
        break;
    case 'DELETE':
        // DELETE /api/contacts?id=1
        if (!isset($_GET['id'])) { http_response_code(400); echo json_encode(['error' => 'Missing id']); exit; }
        $id = intval($_GET['id']);
        $stmt = mysqli_prepare($conn, "DELETE FROM contacts WHERE id = ?");
        mysqli_stmt_bind_param($stmt, 'i', $id);
        $success = mysqli_stmt_execute($stmt);
        echo json_encode(['success' => $success]);
        break;
    default:
        http_response_code(405);
        echo json_encode(['error' => 'Method Not Allowed']);
}
