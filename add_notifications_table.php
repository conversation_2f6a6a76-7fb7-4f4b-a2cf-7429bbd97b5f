<?php
/**
 * Add Notifications Table Script
 * 
 * This script adds the missing notifications table to an existing Lead Management System database.
 * Run this script if you encounter the error: "Table 'lead_management.notifications' doesn't exist"
 */

// Include database configuration
$conn = require_once 'config/database.php';

echo "<h1>Adding Notifications Table</h1>";
echo "<p>This script will add the missing notifications table to your database.</p>";

// Function to execute SQL with error handling
function executeSqlWithLog($conn, $sql, $description) {
    echo "<p><strong>$description...</strong></p>";
    if (mysqli_query($conn, $sql)) {
        echo "<p style='color: green;'>✓ $description completed successfully.</p>";
        return true;
    } else {
        echo "<p style='color: red;'>✗ Error in $description: " . mysqli_error($conn) . "</p>";
        return false;
    }
}

try {
    // Check if the notifications table already exists
    $table_check_sql = "SHOW TABLES LIKE 'notifications'";
    $table_result = mysqli_query($conn, $table_check_sql);
    
    if (mysqli_num_rows($table_result) > 0) {
        echo "<p style='color: orange;'>The notifications table already exists. No action needed.</p>";
    } else {
        // Start transaction
        mysqli_begin_transaction($conn);
        
        // Create notifications table
        $sql = "CREATE TABLE IF NOT EXISTS notifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            message TEXT NOT NULL,
            link VARCHAR(255),
            is_read TINYINT(1) NOT NULL DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES employees(id) ON DELETE CASCADE
        )";
        
        if (executeSqlWithLog($conn, $sql, "Creating notifications table")) {
            // Commit transaction
            mysqli_commit($conn);
            
            echo "<h2 style='color: green;'>Notifications Table Added Successfully!</h2>";
            echo "<p>You can now use the notification features in the Lead Management System.</p>";
        } else {
            // Rollback transaction on error
            mysqli_rollback($conn);
            echo "<h2 style='color: red;'>Failed to add notifications table!</h2>";
        }
    }
    
    echo "<p><a href='admin.php' style='background: #1E3E62; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Return to Dashboard</a></p>";
    
} catch (Exception $e) {
    // Rollback transaction on error
    if (isset($conn) && $conn->connect_errno === 0) {
        mysqli_rollback($conn);
    }
    echo "<h2 style='color: red;'>Error!</h2>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration and try again.</p>";
}

// Close connection
mysqli_close($conn);
?>