<?php
/**
 * Database helper functions for secure database operations
 * 
 * This file contains functions to help with secure database operations,
 * including prepared statements, transaction handling, and error handling.
 */

/**
 * Execute a SELECT query with prepared statements
 * 
 * @param mysqli $conn Database connection
 * @param string $sql SQL query with placeholders
 * @param string $types Parameter types (i: integer, d: double, s: string, b: blob)
 * @param array $params Parameters to bind
 * @return array|false Array of results or false on failure
 */
function db_select($conn, $sql, $types = "", $params = []) {
    $results = [];
    
    try {
        // Prepare statement
        $stmt = mysqli_prepare($conn, $sql);
        
        // Bind parameters if any
        if (!empty($params)) {
            // Convert single parameter to array if needed
            if (!is_array($params)) {
                $params = [$params];
            }
            
            // Create references array for bind_param
            $bind_params = [];
            $bind_params[] = &$types;
            
            for ($i = 0; $i < count($params); $i++) {
                $bind_params[] = &$params[$i];
            }
            
            // Call bind_param with dynamic parameters
            call_user_func_array([$stmt, 'bind_param'], $bind_params);
        }
        
        // Execute statement
        mysqli_stmt_execute($stmt);
        
        // Get results
        $result = mysqli_stmt_get_result($stmt);
        
        // Fetch all results
        while ($row = mysqli_fetch_assoc($result)) {
            $results[] = $row;
        }
        
        // Close statement
        mysqli_stmt_close($stmt);
        
        return $results;
    } catch (Exception $e) {
        // Log error
        error_log("Database query error: " . $e->getMessage() . " in query: " . $sql);
        
        // Return false on error
        return false;
    }
}

/**
 * Execute a SELECT query and return a single row
 * 
 * @param mysqli $conn Database connection
 * @param string $sql SQL query with placeholders
 * @param string $types Parameter types (i: integer, d: double, s: string, b: blob)
 * @param array $params Parameters to bind
 * @return array|false Single row as associative array or false on failure
 */
function db_select_single($conn, $sql, $types = "", $params = []) {
    $results = db_select($conn, $sql, $types, $params);
    
    if ($results && count($results) > 0) {
        return $results[0];
    }
    
    return false;
}

/**
 * Execute a SELECT query and return a single value
 * 
 * @param mysqli $conn Database connection
 * @param string $sql SQL query with placeholders
 * @param string $types Parameter types (i: integer, d: double, s: string, b: blob)
 * @param array $params Parameters to bind
 * @return mixed|false Single value or false on failure
 */
function db_select_value($conn, $sql, $types = "", $params = []) {
    $row = db_select_single($conn, $sql, $types, $params);
    
    if ($row) {
        // Return the first column value
        return reset($row);
    }
    
    return false;
}

/**
 * Execute an INSERT, UPDATE, or DELETE query
 * 
 * @param mysqli $conn Database connection
 * @param string $sql SQL query with placeholders
 * @param string $types Parameter types (i: integer, d: double, s: string, b: blob)
 * @param array $params Parameters to bind
 * @return int|false Number of affected rows or false on failure
 */
function db_query($conn, $sql, $types = "", $params = []) {
    try {
        // Prepare statement
        $stmt = mysqli_prepare($conn, $sql);
        
        // Bind parameters if any
        if (!empty($params)) {
            // Convert single parameter to array if needed
            if (!is_array($params)) {
                $params = [$params];
            }
            
            // Create references array for bind_param
            $bind_params = [];
            $bind_params[] = &$types;
            
            for ($i = 0; $i < count($params); $i++) {
                $bind_params[] = &$params[$i];
            }
            
            // Call bind_param with dynamic parameters
            call_user_func_array([$stmt, 'bind_param'], $bind_params);
        }
        
        // Execute statement
        mysqli_stmt_execute($stmt);
        
        // Get affected rows
        $affected_rows = mysqli_stmt_affected_rows($stmt);
        
        // Close statement
        mysqli_stmt_close($stmt);
        
        return $affected_rows;
    } catch (Exception $e) {
        // Log error
        error_log("Database query error: " . $e->getMessage() . " in query: " . $sql);
        
        // Return false on error
        return false;
    }
}

/**
 * Execute an INSERT query and return the last insert ID
 * 
 * @param mysqli $conn Database connection
 * @param string $sql SQL query with placeholders
 * @param string $types Parameter types (i: integer, d: double, s: string, b: blob)
 * @param array $params Parameters to bind
 * @return int|false Last insert ID or false on failure
 */
function db_insert($conn, $sql, $types = "", $params = []) {
    try {
        // Prepare statement
        $stmt = mysqli_prepare($conn, $sql);
        
        // Bind parameters if any
        if (!empty($params)) {
            // Convert single parameter to array if needed
            if (!is_array($params)) {
                $params = [$params];
            }
            
            // Create references array for bind_param
            $bind_params = [];
            $bind_params[] = &$types;
            
            for ($i = 0; $i < count($params); $i++) {
                $bind_params[] = &$params[$i];
            }
            
            // Call bind_param with dynamic parameters
            call_user_func_array([$stmt, 'bind_param'], $bind_params);
        }
        
        // Execute statement
        mysqli_stmt_execute($stmt);
        
        // Get last insert ID
        $insert_id = mysqli_insert_id($conn);
        
        // Close statement
        mysqli_stmt_close($stmt);
        
        return $insert_id;
    } catch (Exception $e) {
        // Log error
        error_log("Database insert error: " . $e->getMessage() . " in query: " . $sql);
        
        // Return false on error
        return false;
    }
}

/**
 * Begin a transaction
 * 
 * @param mysqli $conn Database connection
 * @return bool True on success, false on failure
 */
function db_begin_transaction($conn) {
    return mysqli_begin_transaction($conn);
}

/**
 * Commit a transaction
 * 
 * @param mysqli $conn Database connection
 * @return bool True on success, false on failure
 */
function db_commit($conn) {
    return mysqli_commit($conn);
}

/**
 * Rollback a transaction
 * 
 * @param mysqli $conn Database connection
 * @return bool True on success, false on failure
 */
function db_rollback($conn) {
    return mysqli_rollback($conn);
}

/**
 * Escape a string for use in a query
 * 
 * @param mysqli $conn Database connection
 * @param string $string String to escape
 * @return string Escaped string
 */
function db_escape($conn, $string) {
    return mysqli_real_escape_string($conn, $string);
}

/**
 * Check if a table exists
 * 
 * @param mysqli $conn Database connection
 * @param string $table_name Table name to check
 * @return bool True if table exists, false otherwise
 */
function db_table_exists($conn, $table_name) {
    $result = db_select_value($conn, "SHOW TABLES LIKE ?", "s", [$table_name]);
    return ($result !== false);
}

/**
 * Check if a column exists in a table
 * 
 * @param mysqli $conn Database connection
 * @param string $table_name Table name
 * @param string $column_name Column name to check
 * @return bool True if column exists, false otherwise
 */
function db_column_exists($conn, $table_name, $column_name) {
    $result = db_select_value($conn, "SHOW COLUMNS FROM " . $table_name . " LIKE ?", "s", [$column_name]);
    return ($result !== false);
}