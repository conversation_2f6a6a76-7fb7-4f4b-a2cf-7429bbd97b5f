<?php
// Get database connection using reliable method
$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';

// Check if database is set up first
require_once 'check_database_setup.php';
redirect_to_setup_if_needed();

// Check if the user is logged in
ensure_session_started();
require_login();

// Get all sales with consolidated data
$sql = "SELECT s.*, CONCAT(c.first_name, ' ', c.last_name) as customer_name, p.name as product_name,
        CONCAT(u.first_name, ' ', u.last_name) as sales_rep_name
        FROM sales s
        LEFT JOIN contacts c ON s.contact_id = c.id
        LEFT JOIN products p ON s.product_id = p.id
        LEFT JOIN employees u ON s.sales_rep_id = u.id
        ORDER BY s.created_at DESC";
$sales_result = mysqli_query($conn, $sql);

// Get sales statistics
$total_sales_query = "SELECT COUNT(*) as total FROM sales";
$total_sales_result = mysqli_query($conn, $total_sales_query);
$total_sales_row = mysqli_fetch_assoc($total_sales_result);
$total_sales = $total_sales_row['total'];

$total_revenue_query = "SELECT SUM(total_amount) as total FROM sales";
$total_revenue_result = mysqli_query($conn, $total_revenue_query);
$total_revenue_row = mysqli_fetch_assoc($total_revenue_result);
$total_revenue = $total_revenue_row['total'] ? $total_revenue_row['total'] : 0;

// Get all contacts for dropdown (both leads and customers)
$customers_query = "SELECT id, CONCAT(first_name, ' ', last_name) as name FROM contacts ORDER BY first_name, last_name";
$customers_result = mysqli_query($conn, $customers_query);
$customers = [];
while ($row = mysqli_fetch_assoc($customers_result)) {
    $customers[] = $row;
}

// Get all products for dropdown
$products_query = "SELECT id, name, price FROM products ORDER BY name";
$products_result = mysqli_query($conn, $products_query);
$products = [];
while ($row = mysqli_fetch_assoc($products_result)) {
    $products[] = $row;
}

// Get sales by product for chart
$product_sales_query = "SELECT p.name as product_name, SUM(s.total_amount) as total_sales
                        FROM sales s
                        LEFT JOIN products p ON s.product_id = p.id
                        GROUP BY s.product_id
                        ORDER BY total_sales DESC";
$product_sales_result = mysqli_query($conn, $product_sales_query);
$product_sales_labels = [];
$product_sales_data = [];
while ($row = mysqli_fetch_assoc($product_sales_result)) {
    $product_sales_labels[] = $row['product_name'];
    $product_sales_data[] = $row['total_sales'];
}

// Get sales by sales rep for chart
$sales_rep_sales_query = "SELECT CONCAT(e.first_name, ' ', e.last_name) as sales_rep_name, SUM(s.total_amount) as total_sales
                          FROM sales s
                          LEFT JOIN employees e ON s.sales_rep_id = e.id
                          GROUP BY s.sales_rep_id
                          ORDER BY total_sales DESC";
$sales_rep_sales_result = mysqli_query($conn, $sales_rep_sales_query);
$sales_rep_sales_labels = [];
$sales_rep_sales_data = [];
while ($row = mysqli_fetch_assoc($sales_rep_sales_result)) {
    $sales_rep_sales_labels[] = $row['sales_rep_name'] ?? 'Unassigned';
    $sales_rep_sales_data[] = $row['total_sales'];
}


?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="img/minilogo.jpg" type="image/x-icon">
    <title>Sales Management - CRM System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="js/sales-report-fix.js"></script>
    <script src="js/direct-pdf.js"></script>
    <style>
        .text-color{ color: #FF6500; }
        .input-focus {
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        .input-focus:focus {
            border-color: #FF6500;
            box-shadow: 0 0 0 3px rgba(255, 101, 0, 0.1);
        }

        .button-hover {
            transition: background-color 0.3s ease, transform 0.2s ease;
        }

        .button-hover:hover {
            background-color: #e55b00;
            transform: scale(1.05);
        }
        
        @media print {
            body * {
                visibility: hidden;
            }
            
            .sales-report-section, .sales-report-section * {
                visibility: visible;
            }
            
            .sales-report-section {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
            }
            
            /* Hide elements with no-print class when printing or in PDF */
            .no-print, .report-parameters-section {
                display: none !important;
            }
            
            .no-print {
                display: none !important;
            }
        }
    </style>
</head>
<body class="">
<!-- Sidebar -->
<?php include 'includes/navigation.php'; ?>

<!-- Main Content -->
    <div class="ml-0 min-[870px]:ml-60 min-h-screen flex flex-col">
        <!-- Navbar -->
        <header class="bg-[#1E3E62] p-4 flex justify-between items-center z-10">
            <h1 class="text-2xl font-bold text-white mx-10 my-2">Sales Management</h1>
            <div class="mx-10">
                <?php include 'includes/top_notification.php'; ?>
            </div>
        </header>
        <main class="flex-1 overflow-y-auto space-y-10">
            <!-- Success message moved to above the sales entry form -->

            <!-- Error message -->
            <?php if (isset($_GET['error']) && $_GET['error'] == 1) : ?>
                <div class="mx-10 mt-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                    <strong class="font-bold">Error!</strong>
                    <span class="block sm:inline"><?php echo htmlspecialchars($_GET['message'] ?? 'An error occurred.'); ?></span>
                    <button class="absolute top-0 bottom-0 right-0 px-4 py-3" onclick="this.parentElement.style.display='none'">
                        <svg class="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
                    </button>
                </div>
            <?php endif; ?>
            
            <!-- Analytics Cards -->
            <section class="grid grid-cols-1 sm:grid-cols-3 lg:grid-cols-4 gap-6 mb-10 px-10" style="background: linear-gradient(to bottom, #1E3E62 60%, white 50%);">
                <div class="bg-white p-4 rounded-2xl shadow-xl h-[150px]">
                    <img src="img/totalsales.png" alt="" class="w-10">
                    <p class="text-sm text-gray-500 ">Total Sales</p>
                    <h2 class="text-xl font-bold mt-2 text-color"><?php echo $total_sales; ?></h2>
                </div>
                <div class="bg-white p-4 rounded-2xl shadow-xl h-[150px]">
                    <img src="img/revenue.png" alt="" class="w-10">
                    <p class="text-sm text-gray-500">Total Revenue</p>
                    <h2 class="text-xl font-bold mt-2 text-color"><?php echo format_currency($total_revenue); ?></h2>
                </div>

            </section>
            
            <!-- Sales Management Tab -->
            <div id="sales-management" class="tab-content mx-10">
                <!-- Success message -->
                <?php if (isset($_GET['success']) && $_GET['success'] == 1) : ?>
                    <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                        <strong class="font-bold">Success!</strong>
                        <span class="block sm:inline"><?php echo htmlspecialchars($_GET['message'] ?? 'Operation completed successfully.'); ?></span>
                        <button class="absolute top-0 bottom-0 right-0 px-4 py-3" onclick="this.parentElement.style.display='none'">
                            <svg class="fill-current h-6 w-6 text-green-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
                        </button>
                    </div>
                <?php endif; ?>
                
                <div class="grid grid-cols-1 gap-6 lg:grid-cols-2 mt-10">
                    <!-- Sales Entry Form -->
                    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                        <div class="px-6 py-5 border-b border-gray-200 sm:px-8" style="background: linear-gradient(to right, #1E3E62, #0B192C);">
                            <h3 class="text-lg leading-6 font-medium text-white">
                                Record New Sale
                            </h3>
                            <p class="mt-1 max-w-2xl text-sm text-white">
                                Add a new sale to the system
                            </p>
                        </div>
                        

                        
                        <div class="px-4 py-5 sm:p-6">
                            <form id="sale-form" class="space-y-4" method="POST" action="process_sale.php">
                                <div class="grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-2">
                                    <div>
                                        <label for="customer" class="block text-sm font-medium text-gray-700">Customer <span class="text-red-500">*</span></label>
                                        <select id="customer" name="contact_id" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                                            <option value="">Select Customer</option>
                                            <?php foreach ($customers as $customer) : ?>
                                                <option value="<?php echo $customer['id']; ?>"><?php echo htmlspecialchars($customer['name']); ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div>
                                        <label for="product" class="block text-sm font-medium text-gray-700">Product <span class="text-red-500">*</span></label>
                                        <select id="product" name="product_id" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                                            <option value="">Select Product</option>
                                            <?php foreach ($products as $product) : ?>
                                                <option value="<?php echo $product['id']; ?>" data-price="<?php echo $product['price']; ?>"><?php echo htmlspecialchars($product['name']); ?> (<?php echo format_currency($product['price']); ?>)</option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div>
                                        <label for="amount" class="block text-sm font-medium text-gray-700">Fixed Price</label>
                                        <input type="number" step="0.01" name="amount" id="amount" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md p-2 border input-focus bg-gray-100" readonly required>
                                    </div>
                                    
                                    <div>
                                        <label for="payment-method" class="block text-sm font-medium text-gray-700">Payment Method <span class="text-red-500">*</span></label>
                                        <select id="payment-method" name="payment_method" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                                            <option>Cash</option>
                                            <option>Credit Card</option>
                                            <option>Debit Card</option>
                                            <option>Bank Transfer</option>
                                            <option>UPI</option>
                                            <option>Other</option>
                                        </select>
                                    </div>
                                    <div class="sm:col-span-2">
                                        <label for="notes" class="block text-sm font-medium text-gray-700">Notes</label>
                                        <textarea id="notes" name="notes" rows="3" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md p-2 border input-focus"></textarea>
                                    </div>
                                </div>
                                <div class="pt-2">
                                    <button type="submit" class="inline-flex justify-center py-2 px-6 border border-transparent shadow-sm text-sm font-medium rounded-sm text-white bg-[#0b192c] hover:bg-[#1e3e62] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0b192c]">
                                        Record Sale
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Recent Sales -->
                    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                        <div class="px-6 py-5 border-b border-gray-200 sm:px-8" style="background: linear-gradient(to right, #1E3E62, #0B192C);">
                            <h3 class="text-lg leading-6 font-medium text-white">
                                Recent Sales
                            </h3>
                            <p class="mt-1 max-w-2xl text-sm text-white">
                                Most recent sales transactions
                            </p>
                        </div>
                        <div class="px-4 py-5 sm:p-6">
                            <div class="space-y-4" id="recent-sales-container">
                                <?php 
                                // Display the first 5 sales
                                $count = 0;
                                if (mysqli_num_rows($sales_result) > 0) {
                                    while ($row = mysqli_fetch_assoc($sales_result)) {
                                        if ($count >= 5) break;
                                        
                                        echo '<div class="flex items-center justify-between border-b pb-3">';
                                        echo '<div>';
                                        echo '<h4 class="text-sm font-medium text-gray-900">' . htmlspecialchars($row['customer_name']) . '</h4>';
                                        echo '<p class="text-sm text-gray-500">' . format_currency($row['total_amount']) . '</p>';
                                        echo '<p class="text-xs text-gray-400">' . date('d M, Y', strtotime($row['sale_date'])) . '</p>';
                                        echo '</div>';
                                        echo '<div>';
                                        echo '<span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">';
                                        echo $row['payment_method'];
                                        echo '</span>';
                                        echo '</div>';
                                        echo '</div>';
                                        
                                        $count++;
                                    }
                                } else {
                                    echo '<p class="text-gray-500">No sales recorded yet.</p>';
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
                
 
                 <!-- All Sales Table -->
                <div class="mt-10 bg-white shadow overflow-hidden sm:rounded-lg mb-10">
                    <div class="px-6 py-5 border-b border-gray-200 sm:px-8" style="background: linear-gradient(to right, #1E3E62, #0B192C);">
                        <h3 class="text-lg leading-6 font-medium text-white">
                            All Sales
                        </h3>
                        <p class="mt-1 max-w-2xl text-sm text-white">
                            Complete list of all sales transactions
                        </p>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">S.No</th>
                                    <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                    <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                                    <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                    <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment Method</th>
                                    <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sales Rep</th>
                                    <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php 
                                // Reset the result pointer
                                mysqli_data_seek($sales_result, 0);
                                
                                if (mysqli_num_rows($sales_result) > 0) {
                                    $serial_number = 1;
                                    while ($row = mysqli_fetch_assoc($sales_result)) {
                                        echo '<tr>';
                                        echo '<td class="px-3 py-3 whitespace-nowrap">';
                                        echo '<div class="text-sm font-medium text-gray-900">' . $serial_number . '</div>';
                                        echo '</td>';
                                        echo '<td class="px-3 py-3 whitespace-nowrap">';
                                        echo '<div class="text-sm font-medium text-gray-900">' . htmlspecialchars($row['customer_name']) . '</div>';
                                        echo '</td>';
                                        
                                        echo '<td class="px-3 py-3 whitespace-nowrap">';
                                        echo '<div class="text-sm font-medium text-gray-900">' . htmlspecialchars($row['product_name']) . '</div>';
                                        echo '</td>';
                                        
                                        $serial_number++;
                                        echo '<td class="px-3 py-3 whitespace-nowrap">';
                                        echo '<div class="text-sm text-gray-900">' . format_currency($row['total_amount']) . '</div>';
                                        echo '</td>';
                                        echo '<td class="px-3 py-3 whitespace-nowrap">';
                                        echo '<div class="text-sm text-gray-900">' . date('d M, Y', strtotime($row['sale_date'])) . '</div>';
                                        echo '</td>';
                                        echo '<td class="px-3 py-3 whitespace-nowrap">';
                                        echo '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">';
                                        echo $row['payment_method'];
                                        echo '</span>';
                                        echo '</td>';
                                        echo '<td class="px-3 py-3 whitespace-nowrap text-sm text-gray-500">';
                                        echo $row['sales_rep_name'] ? htmlspecialchars($row['sales_rep_name']) : 'Unassigned';
                                        echo '</td>';
                                        echo '<td class="px-3 py-3 whitespace-nowrap text-center text-sm font-medium">';
                                        echo '<div class="grid grid-cols-2 gap-2">';
                                        echo '<a href="view_sale.php?id=' . $row['id'] . '" class="text-blue-600 hover:text-blue-900" title="View"><i class="fas fa-eye"></i></a>';
                                        echo '<a href="edit_sale.php?id=' . $row['id'] . '" class="text-indigo-600 hover:text-indigo-900" title="Edit"><i class="fas fa-edit"></i></a>';
                                        echo '<a href="invoice.php?id=' . $row['id'] . '" class="text-green-600 hover:text-green-900" title="Generate Invoice"><i class="fas fa-file-invoice"></i></a>';
                                        echo '<a href="delete_sale.php?id=' . $row['id'] . '" class="text-red-600 hover:text-red-900" title="Delete" onclick="return confirm(\'Are you sure you want to delete this sale?\')"><i class="fas fa-trash-alt"></i></a>';
                                        echo '</div>';
                                        echo '</td>';
                                        echo '</tr>';
                                    }
                                } else {
                                    echo '<tr><td colspan="7" class="px-6 py-4 text-center text-gray-500">No sales recorded yet.</td></tr>';
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>
                </div>


                
                <!-- Sales Report & Print Section (Merged) -->
                <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-20">
                    <div class="px-6 py-5 border-b border-gray-200 sm:px-8" style="background: linear-gradient(to right, #1E3E62, #0B192C);">
                        <h3 class="text-lg leading-6 font-medium text-white">
                            Sales Report & Print
                        </h3>
                        <p class="mt-1 max-w-2xl text-sm text-white">
                            Generate and print a sales report for a custom date range
                        </p>
                    </div>
                    <div class="px-6 py-6">
                        <form method="GET" class="flex flex-col md:flex-row md:items-end gap-4">
                            <div>
                                <label for="report_from" class="block text-sm font-medium text-gray-700 mb-1">From Date</label>
                                <input type="date" id="report_from" name="report_from" class="border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent" value="<?php echo isset($_GET['report_from']) ? htmlspecialchars($_GET['report_from']) : ''; ?>">
                            </div>
                            <div>
                                <label for="report_to" class="block text-sm font-medium text-gray-700 mb-1">To Date</label>
                                <input type="date" id="report_to" name="report_to" class="border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent" value="<?php echo isset($_GET['report_to']) ? htmlspecialchars($_GET['report_to']) : ''; ?>">
                            </div>
                            <div>
                                <button type="submit" class="px-6 py-2 bg-[#0b192c] text-white rounded-sm hover:bg-[#1e3e62] button-hover mt-6 md:mt-0">Generate Report</button>
                            </div>
                        </form>

                        <?php
                        // Handle report generation
                        $report_from = isset($_GET['report_from']) ? $_GET['report_from'] : '';
                        $report_to = isset($_GET['report_to']) ? $_GET['report_to'] : '';
                        $report_sales = [];
                        $report_total = 0;
                        $report_revenue = 0;
                        if ($report_from && $report_to) {
                            $report_sql = "SELECT s.*, CONCAT(c.first_name, ' ', c.last_name) as customer_name, p.name as product_name, CONCAT(u.first_name, ' ', u.last_name) as sales_rep_name FROM sales s LEFT JOIN contacts c ON s.contact_id = c.id LEFT JOIN products p ON s.product_id = p.id LEFT JOIN employees u ON s.sales_rep_id = u.id WHERE s.sale_date BETWEEN ? AND ? ORDER BY s.sale_date DESC";
                            $report_stmt = mysqli_prepare($conn, $report_sql);
                            mysqli_stmt_bind_param($report_stmt, 'ss', $report_from, $report_to);
                            mysqli_stmt_execute($report_stmt);
                            $report_result = mysqli_stmt_get_result($report_stmt);
                            while ($row = mysqli_fetch_assoc($report_result)) {
                                $report_sales[] = $row;
                                $report_total++;
                                $report_revenue += $row['total_amount'];
                            }
                        ?>
                        <div class="mt-8" id="sales-report-print-section">
                            <div class="p-6">
                                <!-- Print-only header -->
                                <div class="hidden print:flex print:flex-row print:items-start print:mb-6 print:gap-8">
                                <div style="flex:1;">
                                <h2 style="font-size: 2rem; font-weight: bold; color: #0b192c; margin-bottom: 0.5rem;">Sales Report</h2>
                                <div style="font-size: 1rem; color: #333; margin-bottom: 0.5rem;">
                                <span style="font-weight: 600;">Date Range:</span> <?php echo htmlspecialchars($report_from); ?> to <?php echo htmlspecialchars($report_to); ?>
                                </div>
                                </div>
                                <div style="min-width:380px; text-align:right;">
                                <img src="img/logo.png" alt="Company Logo" style="height: 60px; margin-bottom: 8px; float:right;">
                                <div style="font-size: 0.95rem; color: #333;">123 Business Street<br>City, State, ZIP<br>Phone: (*************<br>Email: <EMAIL></div>
                                </div>
                                </div>
                                <h4 class="text-lg font-semibold text-gray-800 mb-2">Report Summary</h4>
                                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                                    <div class="bg-gray-100 p-4 rounded-lg shadow">
                                        <p class="text-sm text-gray-500">Total Sales</p>
                                        <h2 class="text-xl font-bold mt-2 text-color"><?php echo $report_total; ?></h2>
                                    </div>
                                    <div class="bg-gray-100 p-4 rounded-lg shadow">
                                        <p class="text-sm text-gray-500">Total Revenue</p>
                                        <h2 class="text-xl font-bold mt-2 text-color"><?php echo format_currency($report_revenue); ?></h2>
                                    </div>
                                </div>
                                <h4 class="text-lg font-semibold text-gray-800 mb-2">Sales in Selected Period</h4>
                                <div class="overflow-x-auto">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sales Rep</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                            <?php foreach ($report_sales as $sale): ?>
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo date('d M, Y', strtotime($sale['sale_date'])); ?></td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo htmlspecialchars($sale['customer_name']); ?></td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo htmlspecialchars($sale['product_name']); ?></td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo format_currency($sale['total_amount']); ?></td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo htmlspecialchars($sale['sales_rep_name']); ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                            <?php if (empty($report_sales)): ?>
                                            <tr><td colspan="5" class="px-6 py-4 text-center text-gray-500">No sales found for this period.</td></tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="mt-8 flex justify-end">
                                    <button onclick="printSalesReport()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-sm shadow-sm text-white bg-[#0b192c] hover:bg-[#1e3e62] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0b192c]">
                                        <i class="fas fa-print mr-2"></i> Print Report
                                    </button>
                                </div>
                            </div>
                        </div>
                        <style>
                        @media print {
                            body * { visibility: hidden; }
                            #sales-report-print-section, #sales-report-print-section * { visibility: visible; }
                            #sales-report-print-section { position: absolute; left: 0; top: 0; width: 100%; background: white; }
                        }
                        </style>
                        <script>
                        function printSalesReport() {
                            window.print();
                        }
                        </script>
                        <?php } ?>
                    </div>
                </div>                              
            </div>
        </main>
    </div>

    <script>
        // Auto-fill fixed price when product is selected
        document.getElementById('product').addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption.value) {
                const price = selectedOption.getAttribute('data-price');
                document.getElementById('amount').value = price;
                // Make it clear that the price is fixed
                document.getElementById('amount').style.backgroundColor = '#f3f4f6';
            } else {
                document.getElementById('amount').value = '';
            }
        });

        // Set default date to today
        document.getElementById('sale-date').valueAsDate = new Date();
        
        // Set default report date range (last 30 days)
        const today = new Date();
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(today.getDate() - 30);
        
        document.getElementById('report-date-to').valueAsDate = today;
        document.getElementById('report-date-from').valueAsDate = thirtyDaysAgo;
        
        // Initialize charts
        let monthlySalesChart;
        let productSalesChart;
        
        // Function to format currency
        function formatCurrency(amount) {
            return new Intl.NumberFormat('en-IN', {
                style: 'currency',
                currency: 'INR',
                minimumFractionDigits: 2
            }).format(amount);
        }
        
        // Function to generate sales report
        function generateSalesReport() {
            console.log('Generating sales report...');
            const fromDate = document.getElementById('report-date-from').value;
            const toDate = document.getElementById('report-date-to').value;
            
            console.log('Date range:', fromDate, 'to', toDate);
            
            // Validate dates
            if (!fromDate || !toDate) {
                alert('Please select both from and to dates');
                console.error('Missing date values');
                return;
            }
            
            if (new Date(fromDate) > new Date(toDate)) {
                alert('From date cannot be after to date');
                console.error('Invalid date range: from date is after to date');
                return;
            }
            
            // Format dates for display
            const fromDateFormatted = new Date(fromDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
            const toDateFormatted = new Date(toDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
            document.getElementById('report-date-display').textContent = `${fromDateFormatted} to ${toDateFormatted}`;
            
            // Fetch monthly sales data
            fetchMonthlySalesData(fromDate, toDate);
            
            // Fetch product sales data
            fetchProductSalesData(fromDate, toDate);
            
            // Fetch sales summary
            fetchSalesSummary(fromDate, toDate);
            
            // Fetch top customers
            fetchTopCustomers(fromDate, toDate);
            
            // Fetch top executives
            fetchTopExecutives(fromDate, toDate);
        }
        
        // Function to fetch monthly sales data
        function fetchMonthlySalesData(fromDate, toDate) {
            // Fetch data from the server
            const url = `get_sales_report.php?type=monthly&from_date=${fromDate}&to_date=${toDate}`;
            console.log('Fetching monthly sales data from:', url);
            
            // Fetch the data with the user-selected date range
            fetch(url)
                .then(response => {
                    console.log('Monthly sales response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Monthly sales data received:', data);
                    if (data.success) {
                        updateMonthlySalesChart(data.labels, data.data);
                    } else {
                        console.error('Error fetching monthly sales data:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error fetching monthly sales data:', error);
                });
        }
        
        // Function to update monthly sales chart
        function updateMonthlySalesChart(labels, data) {
            const ctx = document.getElementById('monthly-sales-chart').getContext('2d');
            
            // Destroy existing chart if it exists
            if (monthlySalesChart) {
                monthlySalesChart.destroy();
            }
            
            // Create new chart
            monthlySalesChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Monthly Sales',
                        data: data,
                        backgroundColor: 'rgba(30, 62, 98, 0.2)',
                        borderColor: '#1E3E62',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return formatCurrency(value);
                                }
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return formatCurrency(context.raw);
                                }
                            }
                        },
                        title: {
                            display: true,
                            text: 'Monthly Sales (in ₹)',
                            font: {
                                size: 16
                            }
                        }
                    }
                }
            });
        }
        
        // Function to fetch product sales data
        function fetchProductSalesData(fromDate, toDate) {
            // Fetch data from the server
            fetch(`get_sales_report.php?type=product&from_date=${fromDate}&to_date=${toDate}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateProductSalesChart(data.labels, data.data);
                    } else {
                        console.error('Error fetching product sales data:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        }
        
        // Function to update product sales chart
        function updateProductSalesChart(labels, data) {
            const ctx = document.getElementById('product-sales-chart').getContext('2d');
            
            // Destroy existing chart if it exists
            if (productSalesChart) {
                productSalesChart.destroy();
            }
            
            // Create new chart
            productSalesChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Product Sales',
                        data: data,
                        backgroundColor: [
                            'rgba(255, 101, 0, 0.8)',
                            'rgba(255, 101, 0, 0.7)',
                            'rgba(255, 101, 0, 0.6)',
                            'rgba(255, 101, 0, 0.5)',
                            'rgba(255, 101, 0, 0.4)'
                        ],
                        borderColor: [
                            '#FF6500',
                            '#FF6500',
                            '#FF6500',
                            '#FF6500',
                            '#FF6500'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return formatCurrency(value);
                                }
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return formatCurrency(context.raw);
                                }
                            }
                        },
                        title: {
                            display: true,
                            text: 'Product Sales (in ₹)',
                            font: {
                                size: 16
                            }
                        }
                    }
                }
            });
        }
        
        // Function to fetch sales summary
        function fetchSalesSummary(fromDate, toDate) {
            // Fetch data from the server
            const url = `get_sales_report.php?type=summary&from_date=${fromDate}&to_date=${toDate}`;
            console.log('Fetching sales summary from:', url);
            
            fetch(url)
                .then(response => {
                    console.log('Summary response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Summary data received:', data);
                    if (data.success && data.summary) {
                        // Update the summary cards
                        document.getElementById('report-total-sales').textContent = data.summary.total_sales;
                        document.getElementById('report-total-revenue').textContent = formatCurrency(data.summary.total_revenue);
                        document.getElementById('report-average-sale').textContent = formatCurrency(data.summary.average_sale);
                        document.getElementById('report-conversion-rate').textContent = data.summary.conversion_rate + '%';
                    } else {
                        console.error('Error fetching sales summary:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error fetching sales summary:', error);
                });
        }
        
        // Function to fetch top customers
        function fetchTopCustomers(fromDate, toDate) {
            // Fetch data from the server
            fetch(`get_sales_report.php?type=customers&from_date=${fromDate}&to_date=${toDate}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.customers) {
                        // Update the top customers table
                        const tableBody = document.getElementById('top-customers-body');
                        tableBody.innerHTML = '';
                        
                        if (data.customers.length === 0) {
                            const row = document.createElement('tr');
                            const emptyCell = document.createElement('td');
                            emptyCell.className = 'px-4 py-4 text-center text-sm text-gray-500';
                            emptyCell.colSpan = 4;
                            emptyCell.textContent = 'No customer data available for the selected period';
                            row.appendChild(emptyCell);
                            tableBody.appendChild(row);
                            return;
                        }
                        
                        data.customers.forEach((customer, index) => {
                            const row = document.createElement('tr');
                            row.className = index % 2 === 0 ? 'bg-white' : 'bg-gray-50';
                            
                            // Customer name with email tooltip
                            const nameCell = document.createElement('td');
                            nameCell.className = 'px-4 py-3 text-sm font-medium text-gray-900';
                            
                            const nameSpan = document.createElement('span');
                            nameSpan.textContent = customer.name;
                            nameSpan.className = 'cursor-pointer relative group';
                            nameSpan.title = `Email: ${customer.email}\nPhone: ${customer.phone}`;
                            
                            nameCell.appendChild(nameSpan);
                            
                            // Sales count
                            const salesCell = document.createElement('td');
                            salesCell.className = 'px-4 py-3 text-center text-sm text-gray-700';
                            
                            const salesSpan = document.createElement('span');
                            salesSpan.textContent = customer.sales;
                            salesSpan.title = `${customer.unique_products} unique products purchased`;
                            salesSpan.className = 'cursor-pointer';
                            
                            salesCell.appendChild(salesSpan);
                            
                            // Revenue
                            const revenueCell = document.createElement('td');
                            revenueCell.className = 'px-4 py-3 text-right text-sm font-medium text-gray-900';
                            revenueCell.textContent = formatCurrency(customer.revenue);
                            
                            // Top product
                            const productCell = document.createElement('td');
                            productCell.className = 'px-4 py-3 text-sm text-gray-700';
                            productCell.textContent = customer.top_product;
                            
                            // Product quantity
                            const quantityCell = document.createElement('td');
                            quantityCell.className = 'px-4 py-3 text-center text-sm text-gray-700';
                            quantityCell.textContent = customer.top_product_count;
                            
                            // Details cell with additional information
                            const detailsCell = document.createElement('td');
                            detailsCell.className = 'px-4 py-3 text-sm text-gray-700';
                            
                            // Format the last purchase date
                            let lastPurchaseDate = 'N/A';
                            if (customer.last_purchase_date && customer.last_purchase_date !== 'N/A') {
                                const date = new Date(customer.last_purchase_date);
                                lastPurchaseDate = date.toLocaleDateString('en-US', { 
                                    year: 'numeric', 
                                    month: 'short', 
                                    day: 'numeric' 
                                });
                            }
                            
                            // Create a details string with product category and last purchase
                            const detailsText = document.createElement('span');
                            detailsText.innerHTML = `
                                <span class="font-medium">Category:</span> ${customer.top_product_category || 'N/A'}<br>
                                <span class="font-medium">Last Purchase:</span> ${lastPurchaseDate}
                            `;
                            detailsCell.appendChild(detailsText);
                            
                            // Add all cells to the row
                            row.appendChild(nameCell);
                            row.appendChild(salesCell);
                            row.appendChild(revenueCell);
                            row.appendChild(productCell);
                            row.appendChild(quantityCell);
                            row.appendChild(detailsCell);
                            
                            tableBody.appendChild(row);
                        });
                    } else {
                        console.error('Error fetching top customers:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        }
        
        // Function to fetch top executives (sales representatives)
        function fetchTopExecutives(fromDate, toDate) {
            // Fetch data from the server
            fetch(`get_sales_report.php?type=executives&from_date=${fromDate}&to_date=${toDate}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.executives) {
                        // Update the top executives table
                        const tableBody = document.getElementById('top-executives-body');
                        tableBody.innerHTML = '';
                        
                        if (data.executives.length === 0) {
                            const row = document.createElement('tr');
                            const emptyCell = document.createElement('td');
                            emptyCell.className = 'px-4 py-4 text-center text-sm text-gray-500';
                            emptyCell.colSpan = 3;
                            emptyCell.textContent = 'No executive data available for the selected period';
                            row.appendChild(emptyCell);
                            tableBody.appendChild(row);
                            return;
                        }
                        
                        data.executives.forEach((executive, index) => {
                            const row = document.createElement('tr');
                            row.className = index % 2 === 0 ? 'bg-white' : 'bg-gray-50';
                            
                            const nameCell = document.createElement('td');
                            nameCell.className = 'px-4 py-3 text-sm font-medium text-gray-900';
                            nameCell.textContent = executive.name;
                            
                            const salesCell = document.createElement('td');
                            salesCell.className = 'px-4 py-3 text-center text-sm text-gray-700';
                            salesCell.textContent = executive.sales;
                            
                            const revenueCell = document.createElement('td');
                            revenueCell.className = 'px-4 py-3 text-right text-sm font-medium text-gray-900';
                            revenueCell.textContent = formatCurrency(executive.revenue);
                            
                            row.appendChild(nameCell);
                            row.appendChild(salesCell);
                            row.appendChild(revenueCell);
                            
                            tableBody.appendChild(row);
                        });
                    } else {
                        console.error('Error fetching top executives:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        }
        
        // Function to print the report
        function printReport() {
            window.print();
        }
        
        // Event listeners
        // Note: The generate-report-btn click event is now handled by sales-report-fix.js
        document.getElementById('print-report-btn').addEventListener('click', printReport);
        
        // Note: The download-report-btn click event is now handled by direct-pdf.js
        
        // Initial report generation is now handled by sales-report-fix.js
        

    </script>
</body>
</html>
