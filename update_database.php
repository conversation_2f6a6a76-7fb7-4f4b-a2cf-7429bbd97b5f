<?php
/**
 * Database Update Script
 * 
 * This script adds new tables and fields required for the enhanced lead management system
 */

// Include database configuration
$conn = require_once 'config/database.php';

echo "<h1>Database Update for Enhanced Lead Management</h1>";
echo "<p>Adding new tables and fields...</p>";

// Function to execute SQL with error handling
function executeSqlWithLog($conn, $sql, $description) {
    echo "<p><strong>$description...</strong></p>";
    if (mysqli_query($conn, $sql)) {
        echo "<p style='color: green;'>✓ $description completed successfully.</p>";
        return true;
    } else {
        echo "<p style='color: red;'>✗ Error in $description: " . mysqli_error($conn) . "</p>";
        return false;
    }
}

try {
    // Start transaction
    mysqli_begin_transaction($conn);
    
    echo "<h2>Creating New Tables...</h2>";
    
    // Create notifications table
    $sql = "CREATE TABLE IF NOT EXISTS notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        title VARCHAR(100) NOT NULL,
        message TEXT NOT NULL,
        type VARCHAR(50) NOT NULL,
        related_to VARCHAR(50) NOT NULL,
        related_id INT,
        is_read TINYINT(1) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        -- Foreign key constraints
        FOREIGN KEY (user_id) REFERENCES employees(id) ON DELETE CASCADE,
        
        -- Indexes for better performance
        INDEX idx_user_id (user_id),
        INDEX idx_is_read (is_read),
        INDEX idx_created_at (created_at)
    )";
    executeSqlWithLog($conn, $sql, "Creating notifications table");
    
    // Create email_templates table
    $sql = "CREATE TABLE IF NOT EXISTS email_templates (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        subject VARCHAR(255) NOT NULL,
        body TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    executeSqlWithLog($conn, $sql, "Creating email_templates table");
    
    // Create email_logs table
    $sql = "CREATE TABLE IF NOT EXISTS email_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        recipient VARCHAR(255) NOT NULL,
        subject VARCHAR(255) NOT NULL,
        template_id INT,
        status VARCHAR(50) NOT NULL,
        error_message TEXT,
        sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        -- Foreign key constraints
        FOREIGN KEY (template_id) REFERENCES email_templates(id) ON DELETE SET NULL
    )";
    executeSqlWithLog($conn, $sql, "Creating email_logs table");
    
    // Add default email templates
    $sql = "INSERT INTO email_templates (name, subject, body) VALUES 
    ('lead_created', 'Thank You for Your Interest - Lead Management System', 
    '<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;\">
        <div style=\"text-align: center; margin-bottom: 20px;\">
            <h2 style=\"color: #1E3E62;\">Thank You for Your Interest</h2>
        </div>
        <p>Dear {{first_name}},</p>
        <p>Thank you for your interest in our services. We have received your information and one of our representatives will contact you shortly.</p>
        <p>Here\'s a summary of the information you provided:</p>
        <ul>
            <li><strong>Name:</strong> {{first_name}} {{last_name}}</li>
            <li><strong>Email:</strong> {{email}}</li>
            <li><strong>Phone:</strong> {{phone}}</li>
            <li><strong>Interest:</strong> {{interest}}</li>
        </ul>
        <p>If you have any immediate questions, please don\'t hesitate to contact us.</p>
        <p>Best regards,<br>Lead Management Team</p>
        <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; font-size: 12px; color: #777; text-align: center;\">
            <p>This is an automated message. Please do not reply to this email.</p>
        </div>
    </div>'),
    
    ('lead_assigned', 'Your Lead Has Been Assigned - Lead Management System', 
    '<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;\">
        <div style=\"text-align: center; margin-bottom: 20px;\">
            <h2 style=\"color: #1E3E62;\">Lead Assignment Notification</h2>
        </div>
        <p>Dear {{first_name}},</p>
        <p>We\'re pleased to inform you that your inquiry has been assigned to one of our dedicated representatives, <strong>{{sales_rep_name}}</strong>.</p>
        <p>They will be contacting you shortly to discuss your requirements and provide personalized assistance.</p>
        <p>If you have any immediate questions, you can reach out to them directly at <a href=\"mailto:{{sales_rep_email}}\">{{sales_rep_email}}</a>.</p>
        <p>Thank you for your patience and interest in our services.</p>
        <p>Best regards,<br>Lead Management Team</p>
        <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; font-size: 12px; color: #777; text-align: center;\">
            <p>This is an automated message. Please do not reply to this email.</p>
        </div>
    </div>'),
    
    ('lead_converted', 'Welcome to Our Customer Family - Lead Management System', 
    '<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;\">
        <div style=\"text-align: center; margin-bottom: 20px;\">
            <h2 style=\"color: #1E3E62;\">Welcome to Our Customer Family!</h2>
        </div>
        <p>Dear {{first_name}},</p>
        <p>Congratulations and thank you for choosing our services! We\'re thrilled to welcome you as our valued customer.</p>
        <p>Your account has been successfully set up, and you can now enjoy all the benefits of our services.</p>
        <p>Your dedicated representative, <strong>{{sales_rep_name}}</strong>, will continue to be your point of contact for any assistance you may need.</p>
        <p>We look forward to a long and successful relationship with you.</p>
        <p>Best regards,<br>Lead Management Team</p>
        <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; font-size: 12px; color: #777; text-align: center;\">
            <p>This is an automated message. Please do not reply to this email.</p>
        </div>
    </div>'),
    
    ('lead_followup', 'Follow-up on Your Recent Inquiry - Lead Management System', 
    '<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;\">
        <div style=\"text-align: center; margin-bottom: 20px;\">
            <h2 style=\"color: #1E3E62;\">Following Up on Your Inquiry</h2>
        </div>
        <p>Dear {{first_name}},</p>
        <p>I hope this email finds you well. I wanted to follow up on your recent inquiry about our services.</p>
        <p>We\'re eager to assist you and provide more information about how our solutions can meet your needs.</p>
        <p>Your representative, <strong>{{sales_rep_name}}</strong>, has been trying to reach you to discuss your requirements in more detail.</p>
        <p>Please let us know when would be a convenient time for a call or if you have any specific questions we can address.</p>
        <p>Best regards,<br>Lead Management Team</p>
        <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; font-size: 12px; color: #777; text-align: center;\">
            <p>This is an automated message. Please do not reply to this email.</p>
        </div>
    </div>')";
    executeSqlWithLog($conn, $sql, "Adding default email templates");
    
    // Check if the contacts table has the necessary fields for leads
    $result = mysqli_query($conn, "SHOW COLUMNS FROM contacts LIKE 'is_referral'");
    if (mysqli_num_rows($result) == 0) {
        // Add is_referral field to contacts table
        $sql = "ALTER TABLE contacts ADD COLUMN is_referral TINYINT(1) DEFAULT 0";
        executeSqlWithLog($conn, $sql, "Adding is_referral field to contacts table");
    }
    
    $result = mysqli_query($conn, "SHOW COLUMNS FROM contacts LIKE 'referred_by_contact_id'");
    if (mysqli_num_rows($result) == 0) {
        // Add referred_by_contact_id field to contacts table
        $sql = "ALTER TABLE contacts ADD COLUMN referred_by_contact_id INT DEFAULT NULL, 
                ADD FOREIGN KEY (referred_by_contact_id) REFERENCES contacts(id) ON DELETE SET NULL";
        executeSqlWithLog($conn, $sql, "Adding referred_by_contact_id field to contacts table");
    }
    
    // Commit transaction
    mysqli_commit($conn);
    
    echo "<h2 style='color: green;'>Database Update Completed Successfully!</h2>";
    echo "<p>The following has been set up:</p>";
    echo "<ul>";
    echo "<li>✓ Notifications table created</li>";
    echo "<li>✓ Email templates table created</li>";
    echo "<li>✓ Email logs table created</li>";
    echo "<li>✓ Default email templates added</li>";
    echo "<li>✓ Additional fields added to contacts table</li>";
    echo "</ul>";
    
    echo "<p><a href='index.php' style='background: #1E3E62; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Return to Dashboard</a></p>";
    
} catch (Exception $e) {
    // Rollback transaction on error
    mysqli_rollback($conn);
    echo "<h2 style='color: red;'>Database Update Failed!</h2>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration and try again.</p>";
}

// Close connection
mysqli_close($conn);
?>