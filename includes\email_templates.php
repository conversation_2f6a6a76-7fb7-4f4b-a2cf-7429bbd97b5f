<?php
/**
 * Email Templates Configuration
 * Customizable email templates for different types of notifications
 */

/**
 * Get email template configuration
 */
function get_email_template_config() {
    return [
        'new_product' => [
            'subject' => '🎉 New Product Alert: {product_name} - Show Your Interest!',
            'greeting' => [
                'lead' => 'Dear Potential Customer',
                'customer' => 'Dear Valued Customer'
            ],
            'action_text' => [
                'lead' => 'Show Interest',
                'customer' => 'Purchase Now'
            ],
            'company_name' => 'Lead Management System',
            'company_email' => SMTP_FROM_EMAIL,
            'offer_days' => 7,
            'features' => [
                '✅ High-quality and reliable',
                '✅ Competitive pricing',
                '✅ Excellent customer support',
                '✅ Trusted by thousands of customers'
            ]
        ]
    ];
}

/**
 * Get customizable email content sections
 */
function get_email_content_sections() {
    return [
        'header' => [
            'title' => '🎉 New Product Alert!',
            'subtitle' => 'We have something exciting for you!'
        ],
        'intro' => [
            'lead' => "We're thrilled to announce the launch of our latest product that we think you'll love!",
            'customer' => "We're excited to share our newest product with our valued customers!"
        ],
        'product_highlight' => [
            'title' => '🎯 Perfect for you!',
            'description' => 'Based on your interests, this product could be exactly what you\'re looking for.'
        ],
        'offer' => [
            'title' => '🔥 Limited Time Offer:',
            'description' => 'Contact us within {offer_days} days to get special pricing and exclusive benefits!'
        ],
        'contact' => [
            'title' => '📞 Contact Us:',
            'description' => 'Reply to this email or call us to learn more!'
        ],
        'footer' => [
            'thanks' => 'Thank you for being a valued part of our community. We look forward to serving you!',
            'signature' => 'Lead Management Team',
            'unsubscribe' => 'This email was sent because you are registered in our system. If you no longer wish to receive these notifications, please contact us.'
        ]
    ];
}

/**
 * Generate email subject with variables
 */
function generate_email_subject($template_key, $variables = []) {
    $config = get_email_template_config();
    $subject = $config[$template_key]['subject'] ?? 'New Product Notification';
    
    // Replace variables in subject
    foreach ($variables as $key => $value) {
        $subject = str_replace('{' . $key . '}', $value, $subject);
    }
    
    return $subject;
}

/**
 * Generate personalized greeting
 */
function generate_greeting($recipient_type, $first_name) {
    $config = get_email_template_config();
    $greeting = $config['new_product']['greeting'][$recipient_type] ?? 'Dear Customer';
    
    return "Hello {$first_name}!\n\n{$greeting},";
}

/**
 * Generate product features list
 */
function generate_features_list($format = 'html') {
    $config = get_email_template_config();
    $features = $config['new_product']['features'];
    
    if ($format === 'html') {
        return '<ul>' . implode('', array_map(function($feature) {
            return "<li>{$feature}</li>";
        }, $features)) . '</ul>';
    } else {
        return implode("\n", $features);
    }
}

/**
 * Generate call-to-action button/text
 */
function generate_cta($recipient_type, $product_name, $format = 'html') {
    $config = get_email_template_config();
    $action_text = $config['new_product']['action_text'][$recipient_type] ?? 'Contact Us';
    $email = $config['new_product']['company_email'];
    
    if ($format === 'html') {
        return "<a href='mailto:{$email}?subject=Interest in {$product_name}&body=Hi, I am interested in the new product {$product_name}. Please provide more details.' class='cta-button'>{$action_text} 📧</a>";
    } else {
        return "To {$action_text}, simply reply to this email or contact us at: 📧 {$email}";
    }
}

/**
 * Get email styling CSS
 */
function get_email_styles() {
    return "
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #1E3E62, #0B192C); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .product-card { background: white; padding: 25px; border-radius: 8px; margin: 20px 0; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .price { font-size: 24px; font-weight: bold; color: #1E3E62; margin: 10px 0; }
        .category { background: #1E3E62; color: white; padding: 5px 15px; border-radius: 20px; font-size: 12px; display: inline-block; margin: 10px 0; }
        .cta-button { background: #1E3E62; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 20px 0; font-weight: bold; }
        .cta-button:hover { background: #0B192C; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 12px; }
        .highlight { background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0; border-left: 4px solid #ffc107; }
        .features { list-style: none; padding: 0; }
        .features li { padding: 5px 0; }
    </style>";
}

/**
 * Validate email template variables
 */
function validate_template_variables($variables) {
    $required = ['product_name', 'product_price', 'product_category', 'first_name'];
    $missing = [];
    
    foreach ($required as $var) {
        if (!isset($variables[$var]) || empty($variables[$var])) {
            $missing[] = $var;
        }
    }
    
    return [
        'valid' => empty($missing),
        'missing' => $missing
    ];
}

/**
 * Log email template usage for analytics
 */
function log_email_template_usage($template_key, $recipient_type, $success = true) {
    $log_entry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'template' => $template_key,
        'recipient_type' => $recipient_type,
        'success' => $success
    ];
    
    error_log("Email Template Usage: " . json_encode($log_entry));
}
?>
