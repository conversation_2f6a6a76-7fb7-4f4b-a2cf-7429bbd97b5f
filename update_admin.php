<?php
$conn = require_once 'config/database.php';

// credentials
$new_username = 'admin_123';
$new_password = 'Admin@123';
$current_username = 'admin';

// Validate the new password
function validate_password($password) {
    $basic_requirements = preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?]).{6,}$/', $password);
    $has_consecutive_digits = preg_match('/\d{4,}/', $password);
    return $basic_requirements && !$has_consecutive_digits;
}

// Check if the password meets requirements
if (!validate_password($new_password)) {
    die("Error: The new password does not meet the requirements. Password must have at least 6 characters with at least one uppercase letter, one lowercase letter, one number, and one special character. No 4 or more consecutive digits allowed.");
}

// Hash the new password
$hashed_password = password_hash($new_password, PASSWORD_DEFAULT);

// Update the admin user
$sql = "UPDATE employees SET username = ?, password = ? WHERE username = ?";
$stmt = mysqli_prepare($conn, $sql);
mysqli_stmt_bind_param($stmt, "sss", $new_username, $hashed_password, $current_username);

if (mysqli_stmt_execute($stmt)) {
    echo "Success: Admin user updated successfully.<br>";
    echo "New username: $new_username<br>";
    echo "New password: $new_password<br>";
} else {
    echo "Error: Failed to update admin user. " . mysqli_error($conn);
}

mysqli_stmt_close($stmt);
mysqli_close($conn);
?>