<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>LMS-Sales Management</title>
    <link rel="icon" type="image/png" href="/images/logo.title.png">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet" />
    <link
        href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Open+Sans:wght@400;500;600&display=swap"
        rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" />
    <!-- AOS CSS -->
    <link href="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.css" rel="stylesheet">
</head>

<body class="bg-white text-gray-800 font-sans">
    <!-- Navigation Bar -->
    <header class="fixed w-full top-0 z-50" x-data="{ isOpen: false, featuresOpen: false }">
    <div class="container lg:max-w-full mx-auto px-4 sm:px-4 md:px-6 lg:px-8 py-2 sm:py-3 md:py-4">
      <div
        class=" bg-white backdrop-blur-lg rounded-lg sm:rounded-xl lg:rounded-2xl shadow-lg border border-white/20 mx-1 sm:mx-2 md:mx-3 lg:mx-4">
        <div class="flex justify-between items-center h-12 sm:h-14 md:h-16 px-2 sm:px-4 md:px-6">
          <!-- Logo -->
          <div class="flex items-center">
            <h1
              class="text-lg sm:text-xl md:text-2xl bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent relative">
              <img src="images/logo.png" alt="LMS Logo" class="lg:w-32  w-20 object-contain">
            </h1>
          </div>

          <!-- Desktop Nav -->
          <nav class=" hidden lg:flex justify-between items-center">
            <div class="flex">
              <a href="index.html"
                class="flex nav-link px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black font-semisemibold hover:text-[#1E3E62] transition-all duration-300">
                Home
              </a>
              <a href="About.html"
                class="flex px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black font-semisemibold hover:text-[#1E3E62] transition-all duration-300">
                About
              </a>
              <a href="products.html"
                class=" flex px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black font-semisemibold transition-all duration-300">
                Products
              </a>

              <!-- Features Dropdown -->
              <div class="relative" x-data="{ open: false }" @mouseenter="open = true" @mouseleave="open = false">
                <button
                  class="px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black font-semisemibold hover:text-[#1E3E62] transition-all duration-300 flex items-center">
                  Features
                  <i class="ri-arrow-down-s-line ml-1 transition-transform duration-200"
                    :class="open ? 'rotate-180' : ''"></i>
                </button>

                <!-- Dropdown Menu -->
                <div x-show="open" x-transition:enter="transition ease-out duration-200"
                  x-transition:enter-start="opacity-0 transform scale-95"
                  x-transition:enter-end="opacity-100 transform scale-100"
                  x-transition:leave="transition ease-in duration-150"
                  x-transition:leave-start="opacity-100 transform scale-100"
                  x-transition:leave-end="opacity-0 transform scale-95"
                  class="absolute top-full left-0 mt-2 w-64 bg-white rounded-xl shadow-2xl border border-gray-100 py-2 z-50">

                  <a href="leadmanagement.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-brain-line text-blue-600"></i>
                    </div>
                    <div>
                      <div class="font-semibold">Lead Management</div>
                      <div class="text-xs text-gray-500">Smart lead prioritization</div>
                    </div>
                  </a>

                  <a href="salemanagement.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-settings-3-line text-green-600"></i>
                    </div>
                    <div>
                      <div class="font-semibold">Sales Management</div>
                      <div class="text-xs text-gray-500">Streamline your process</div>
                    </div>
                  </a>

                  <a href="analytics.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-pie-chart-line text-purple-600"></i>
                    </div>
                    <div>
                      <div class="font-semibold">Advanced Analytics</div>
                      <div class="text-xs text-gray-500">Detailed insights & reports</div>
                    </div>
                  </a>

                  <a href="taskmanager.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-smartphone-line text-orange-600"></i>
                    </div>
                    <div>
                      <div class="font-semibold">Task Manager</div>
                      <div class="text-xs text-gray-500">Capture from anywhere</div>
                    </div>
                  </a>

                  <a href="customersupport.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-team-line text-indigo-600"></i>
                    </div>
                    <div>
                      <div class="font-semibold">Customer Support</div>
                      <div class="text-xs text-gray-500">Work together effectively</div>
                    </div>
                  </a>

                  <a href="integration.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-teal-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-shield-check-line text-teal-600"></i>
                    </div>
                    <div>
                      <div class="font-semibold">Integration</div>
                      <div class="text-xs text-gray-500">Connect seamlessly with tools</div>
                    </div>
                  </a>
                </div>
              </div>
              <a href="contact.html"
                class=" flex items-center  px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black font-semisemibold transition-all duration-300">
                Contact 
              </a>
              
              <a href="/LEADManagement/auth/login.php"
                class=" flex items-center bg-orange-500 rounded-3xl  px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-semisemibase md:text-lg text-white font-bold transition-all duration-300">
                Login
              </a>
            
            </div>
            
          </nav>

          <!-- Mobile Menu Button -->
          <div class="lg:hidden z-50">
            <button @click="isOpen = !isOpen" class="focus:outline-none relative w-6 sm:w-7 md:w-8 h-6 sm:h-7 md:h-8">
              <span class="absolute top-1 left-0 w-6 sm:w-7 md:w-8 h-0.5 bg-black transition-transform duration-300"
                :class="isOpen ? 'rotate-45 top-3' : ''"></span>
              <span class="absolute top-3 left-0 w-6 sm:w-7 md:w-8 h-0.5 bg-black transition-opacity duration-300"
                :class="isOpen ? 'opacity-0' : ''"></span>
              <span class="absolute top-5 left-0 w-6 sm:w-7 md:w-8 h-0.5 bg-black transition-transform duration-300"
                :class="isOpen ? '-rotate-45 top-3' : ''"></span>
            </button>
          </div>
        </div>

        <!-- Mobile Nav Menu -->
        <div class="lg:hidden px-3 sm:px-4 md:px-6 pb-4 sm:pb-5 md:pb-6 pt-2" x-show="isOpen" x-transition>
          <div class="flex flex-col space-y-2 sm:space-y-3 md:space-y-4">
            <a href="index.html"
              class="text-sm sm:text-base md:text-lg text-black font-semibold hover:text-[#1E3E62] transition-colors duration-200">
              Home
            </a>
            <a href="About.html"
              class="text-sm sm:text-base md:text-lg text-black font-semibold hover:text-[#1E3E62] transition-colors duration-200">
              About
            </a>
            <a href="products.html" class="text-sm sm:text-base md:text-lg hover:text-[#1E3E62] font-semibold">
              Products
            </a>
            <a href="contact.html" class="text-sm sm:text-base md:text-lg hover:text-[#1E3E62] font-semibold">
              Contact
            </a>

            <!-- Mobile Features Section -->
            <div x-data="{ mobileFeatures: false }">
              <button @click="mobileFeatures = !mobileFeatures"
                class="w-full text-left text-sm sm:text-base md:text-lg text-black font-semibold hover:text-[#1E3E62] transition-colors duration-200 flex items-center justify-between">
                <span>Features</span>
                <i class="ri-arrow-down-s-line transition-transform duration-200"
                  :class="mobileFeatures ? 'rotate-180' : ''"></i>
              </button>

              <div x-show="mobileFeatures" x-transition class="ml-4 mt-2 space-y-2">
                <a href="leadmanagement.html" class="block text-xs sm:text-sm text-gray-600 hover:text-[#1E3E62] py-1">
                  <i class="ri-brain-line mr-2"></i>Lead Management
                </a>
                <a href="salemanagement.html" class="block text-xs sm:text-sm text-gray-600 hover:text-[#1E3E62] py-1">
                  <i class="ri-settings-3-line mr-2"></i>Sales Management 
                </a>
                <a href="analytics.html" class="block text-xs sm:text-sm text-gray-600 hover:text-[#1E3E62] py-1">
                  <i class="ri-pie-chart-line mr-2"></i>Advanced Analytics
                </a>
                <a href="taskmanager.html" class="block text-xs sm:text-sm text-gray-600 hover:text-[#1E3E62] py-1">
                  <i class="ri-smartphone-line mr-2"></i>Task Manager
                </a>
                <a href="customersupport.html" class="block text-xs sm:text-sm text-gray-600 hover:text-[#1E3E62] py-1">
                  <i class="ri-team-line mr-2">Customer Support</i>
                </a>
                <a href="integration.html" class="block text-xs sm:text-sm text-gray-600 hover:text-[#1E3E62] py-1">
                  <i class="ri-shield-check-line mr-2"></i>Integration
                </a>
              </div>
            </div>
            
            <a href="login.html"
              class="bg-[#FF6500] text-white px-4 sm:px-5 md:px-6 py-1.5 sm:py-2 md:py-2.5 text-sm sm:text-base md:text-lg rounded-full font-semibold text-center mt-2 hover:bg-[#e55a00] transition-colors duration-200">
              Login
            </a>
          </div>
        </div>
      </div>
    </div>
  </header>


<!-- Hero Section -->
<section class="bg-[#1E3E62] text-white py-20 px-6 text-center ">
    <div class="max-w-4xl mx-auto mt-10">
        <h1 class="text-4xl md:text-6xl font-bold mb-4">Boost Your Revenue with Smart <span class="text-orange-500">Sales Management</span></h1>
        <p class="text-lg md:text-xl mb-6">Streamline your pipeline. Automate. Track. Close more deals.</p>
    </div>
</section>

<!-- Features Section -->
<section class="py-16 px-6 bg-gray-50">
    <div class="max-w-6xl mx-auto text-center">
        <h2 class="text-3xl font-bold mb-12">Advanced Tools for Modern Sales Teams</h2>
        <div class="grid md:grid-cols-3 gap-10 text-left">
            <div class="bg-[#1E3E62] text-white p-6 rounded-xl shadow">
                <h3 class="text-xl text-white font-semibold mb-2">🎯 Lead Segmentation</h3>
                <p>Segment leads by interest, behavior, or region to engage them with targeted strategies.</p>
            </div>
            <div class="bg-[#1E3E62] text-white p-6 rounded-xl shadow">
                <h3 class="text-xl font-semibold mb-2">⚡ Sales Automation</h3>
                <p>Automate repetitive tasks like follow-ups and lead assignments to boost efficiency.</p>
            </div>
            <div class="bg-[#1E3E62] text-white p-6 rounded-xl shadow">
                <h3 class="text-xl font-semibold mb-2">📊 Sales Analytics</h3>
                <p>Monitor KPIs, conversion rates, and deal progress with real-time analytics and insights.</p>
            </div>
            <div class="bg-[#1E3E62] text-white p-6 rounded-xl shadow">
                <h3 class="text-xl font-semibold mb-2">🧪 Deal Testing</h3>
                <p>Test different sales pitches, timelines, and channels to optimize your conversion strategy.</p>
            </div>
            <div class="bg-[#1E3E62] text-white p-6 rounded-xl shadow">
                <h3 class="text-xl font-semibold mb-2">📥 CRM Integration</h3>
                <p>Connect with CRMs and tools you already use for a seamless lead-to-close process.</p>
            </div>
            <div class="bg-[#1E3E62] text-white p-6 rounded-xl shadow">
                <h3 class="text-xl font-semibold mb-2">🛡️ Data Security</h3>
                <p>Ensure your sales data is secure with role-based access and encryption standards.</p>
            </div>
        </div>
    </div>
</section>

<!-- Testimonials -->
<section class="bg-white py-16 px-6">
    <div class="max-w-3xl mx-auto text-center">
        <h2 class="text-3xl font-bold mb-10">Trusted by Sales Teams <span class="text-orange-500">Worldwide</span></h2>
        <blockquote class="text-lg italic mb-4">"The automation and tracking features have revolutionized our sales process. It’s like having an assistant for every rep!"</blockquote>
        <p class="font-semibold">— Rohan M., Sales Director @ GrowthWave</p>
    </div>
</section>

<!-- Pricing Section -->
<section class="py-20 bg-gray-100 px-6">
    <div class="max-w-6xl mx-auto text-center">
        <h2 class="text-3xl font-bold mb-12">Pick a Plan that Grows with You</h2>
        <div class="grid md:grid-cols-3 gap-8">
            <div class="bg-white p-8 rounded-xl shadow">
                <h3 class="text-xl font-semibold mb-4">Free</h3>
                <p class="text-3xl font-bold mb-6">Rs0<span class="text-sm font-normal">/mo</span></p>
                <ul class="mb-6 space-y-2 text-left text-gray-600">
                    <li>✔️ 50 leads/month</li>
                    <li>✔️ Basic Automation</li>
                    <li>✔️ CRM Sync</li>
                </ul>
                <a href="#" class="block bg-[#1E3E62] text-white py-2 rounded hover:bg-orange-600">Start Free</a>
            </div>
            <div class="bg-white p-8 rounded-xl shadow border-4 border-orange-500">
                <h3 class="text-xl font-semibold mb-4">Pro</h3>
                <p class="text-3xl font-bold mb-6">Rs299<span class="text-sm font-normal">/mo</span></p>
                <ul class="mb-6 space-y-2 text-left text-gray-600">
                    <li>✔️ 1,000 leads/month</li>
                    <li>✔️ Advanced Automation</li>
                    <li>✔️ Deal Testing Tools</li>
                    <li>✔️ Analytics Dashboard</li>
                </ul>
                <a href="#" class="block bg-[#1E3E62] text-white py-2 rounded hover:bg-orange-600">Get Pro</a>
            </div>
            <div class="bg-white p-8 rounded-xl shadow">
                <h3 class="text-xl font-semibold mb-4">Enterprise</h3>
                <p class="text-3xl font-bold mb-6">Custom</p>
                <ul class="mb-6 space-y-2 text-left text-gray-600">
                    <li>✔️ Unlimited leads</li>
                    <li>✔️ Dedicated Success Manager</li>
                    <li>✔️ Custom Integrations</li>
                </ul>
                <a href="#" class="block bg-[#1E3E62] text-white py-2 rounded hover:bg-orange-600">Contact Us</a>
            </div>
        </div>
    </div>
</section>

<!-- FAQ -->
<section class="bg-white py-16 px-6">
    <div class="max-w-4xl mx-auto">
        <h2 class="text-3xl font-bold mb-8 text-center">Frequently Asked Questions</h2>
        <div class="space-y-6">
            <div>
                <h4 class="font-semibold text-lg">Is there a free trial?</h4>
                <p class="text-gray-600">Yes, our free plan gives you access to core sales features to get started easily.</p>
            </div>
            <div>
                <h4 class="font-semibold text-lg">Can I cancel anytime?</h4>
                <p class="text-gray-600">Yes, you can cancel your subscription anytime with no extra charges.</p>
            </div>
            <div>
                <h4 class="font-semibold text-lg">What kind of support is included?</h4>
                <p class="text-gray-600">We provide 24/7 email support and live chat for Pro and Enterprise plans.</p>
            </div>
            <div>
                <h4 class="font-semibold text-lg">Do you support CRM integrations?</h4>
                <p class="text-gray-600">Yes, we offer integrations with major CRM platforms for seamless operations.</p>
            </div>
            <div>
                <h4 class="font-semibold text-lg">How do I import my leads?</h4>
                <p class="text-gray-600">You can upload leads via CSV, Excel, or import from integrated tools.</p>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action -->
<section class="bg-[#1E3E62] text-white py-16 px-6 text-center ">
    <div class="max-w-4xl mx-auto">
        <h2 class="text-3xl md:text-4xl font-bold mb-4">Ready to Close More Deals?</h2>
        <p class="text-lg mb-6">Join thousands of top-performing sales teams and take your process to the next level.</p>
        <a href="#"
            class="inline-block bg-white text-indigo-600 font-semibold px-6 py-3 rounded-full shadow-md hover:bg-gray-100 transition">Start Managing Sales</a>
    </div>
</section>

    <!-- Footer -->
    <footer class="bg-black text-white px-4 sm:px-6 lg:px-10 w-full mt-1">
    <div class="max-w-7xl mx-auto">
      <div class=" py-8 grid grid-cols-1 md:grid-cols-4 lg:grid-cols-4 gap-8">
        <!-- Logo & Description -->
        <div class="flex flex-col items-start lg:items-start space-y-4">
          <img src="images/logo.png" alt="Logo" class="w-32 sm:w-40 h-auto bg-white" />
          <p class="text-gray-400 text-sm max-w-sm">
            Effortless payroll, seamless success – simplify your pay process today.
          </p>
          <div class="flex gap-4">
            <!-- Social Icons -->
            <!-- Facebook -->
            <a href="#" class="text-gray-400 hover:text-white transition">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path
                  d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"" />
            </svg>
          </a>
          <!-- Twitter -->
          <a href=" #" class="text-gray-400 hover:text-white transition">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path
                      d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"" />
            </svg>
          </a>
          <!-- Instagram -->
          <a href=" #" class="text-gray-400 hover:text-white transition">
                      <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path
                          d="M7.75 2h8.5A5.75 5.75 0 0122 7.75v8.5A5.75 5.75 0 0116.25 22h-8.5A5.75 5.75 0 012 16.25v-8.5A5.75 5.75 0 017.75 2zm0 1.5A4.25 4.25 0 003.5 7.75v8.5A4.25 4.25 0 007.75 20.5h8.5a4.25 4.25 0 004.25-4.25v-8.5A4.25 4.25 0 0016.25 3.5h-8.5zm8.75 2a1 1 0 110 2 1 1 0 010-2zM12 7a5 5 0 110 10 5 5 0 010-10zm0 1.5a3.5 3.5 0 100 7 3.5 3.5 0 000-7z" " />
            </svg>
          </a>
          <!-- LinkedIn -->
          <a href=" #" class="text-gray-400 hover:text-white transition">
                          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                            <path
                              d="M4.98 3.5C4.98 4.61 4.1 5.5 3 5.5S1.02 4.61 1.02 3.5 1.9 1.5 3 1.5 4.98 2.39 4.98 3.5zM.5 6h5V20h-5V6zm7.5 0h4.7v1.785h.066c.655-1.24 2.255-2.535 4.634-2.535 4.953 0 5.867 3.26 5.867 7.5V20h-5v-6.417c0-1.531-.027-3.5-2.134-3.5-2.138 0-2.466 1.668-2.466 3.39V20h-5V6z" " />
            </svg>
          </a>
        </div>
      </div>

      <!-- Quick Links -->
      <div class=" space-y-4">
                              <h3 class="text-lg font-semibold">Quick Links</h3>
                              <ul class="space-y-2 text-gray-400 text-sm">
                                <li><a href="index.html" class="hover:text-white">Home</a></li>
                                <li><a href="about.html" class="hover:text-white">About Us</a></li>
                                <li><a href="products.html" class="hover:text-white">Products</a></li>
                                <li><a href="contact.html" class="hover:text-white">Contactus</a></li>
                                <li><a href="login.html" class="hover:text-white">Login</a></li>
                                
                              </ul>
          </div>

          <!-- Products -->
          <div class="space-y-4">
            <h3 class="text-lg font-semibold">Products</h3>
            <ul class="space-y-2 text-gray-400 text-sm">
              <li><a href="leadmanagement.html" class="hover:text-white">Lead Management</a></li>
              <li><a href="salemanagement.html" class="hover:text-white">Sales Management</a></li>
              <li><a href="analytics.html" class="hover:text-white">Advance Analytics</a></li>
              <li><a href="customersupport.html" class="hover:text-white">Customer Management</a></li>
              <li><a href="integration.html" class="hover:text-white">Integration</a></li>
            </ul>
          </div>

          <!-- Contact -->
          <div class="space-y-4">
            <h3 class="text-lg font-semibold">Contact</h3>
            <ul class="space-y-3 text-gray-400 text-sm">
              <li class="flex items-center gap-2">
                <svg class="w-4 h-4" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <a href="mailto:<EMAIL>" class="hover:text-white"><EMAIL></a>
              </li>
              <li class="flex items-center gap-2">
                <svg class="w-4 h-4" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
                +1 (555) 482-9316
              </li>
              <li class="flex items-start gap-2">
                <svg class="w-4 h-4" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <span>
                  1234 Elm Street, Suite 567,<br />
                  Springfield, IL 62701, USA
                </span>
              </li>
            </ul>
          </div>
        </div>

        <!-- Bottom -->
        <div
          class="border-t border-gray-800  py-3 sm:text-left flex flex-col sm:flex-row justify-between items-center text-gray-400 text-sm gap-4">
          <p>© 2025 LMS. All rights reserved.</p>
          <div class="flex gap-4">
            <a href="#" class="hover:text-white">Terms & Conditions</a>
            <a href="#" class="hover:text-white">Privacy Policy</a>
          </div>
        </div>
      </div>
  </footer>

    <script>
        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true,
            offset: 100
        });

        // Add smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
    <script src="https://unpkg.com/remixicon/fonts/remixicon.css"></script>
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        const animatedCards = document.querySelectorAll(".animated-card");

        const cardObserver = new IntersectionObserver((entries) => {
            entries.forEach((entry) => {
                if (entry.isIntersecting) {
                    entry.target.classList.add("visible");
                    cardObserver.unobserve(entry.target); // Only animate once
                }
            });
        }, {
            threshold: 0.1,
        });

        animatedCards.forEach((card) => cardObserver.observe(card));


        const cards = document.querySelectorAll(".stat-card");

        const observer = new IntersectionObserver(entries => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add("visible");
                    observer.unobserve(entry.target); // Animate only once
                }
            });
        }, {
            threshold: 0.1,
        });

        cards.forEach(card => {
            observer.observe(card);
        });
        document.querySelectorAll('.toggle-btn').forEach(button => {
            button.addEventListener('click', () => {
                const targetId = button.getAttribute('data-target');
                const content = document.getElementById(targetId);
                const plusIcon = button.querySelector('.plus-icon path');

                if (content.classList.contains('hidden')) {
                    content.classList.remove('hidden');
                    // Change plus to minus (rotate + hide vertical stroke)
                    button.querySelector('.plus-icon').innerHTML = `
                <path stroke-linecap="round" stroke-linejoin="round" d="M6 12h12"></path>`;
                } else {
                    content.classList.add('hidden');
                    // revert back to plus
                    button.querySelector('.plus-icon').innerHTML = `
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v12m6-6H6"></path>
            `;
                }
            });
        });

        // Animate counting up function
        function animateCount(id, target, duration = 2000) {
            const el = document.getElementById(id);
            let start = 0;
            const isK = typeof target === 'string' && target.includes('K');
            const isPlus = typeof target === 'string' && target.includes('+');

            // Extract number value, e.g. "24K+" => 24
            const targetNum = typeof target === 'string'
                ? parseFloat(target.replace(/[^\d.]/g, ''))
                : target;

            const stepTime = Math.abs(Math.floor(duration / targetNum));
            const increment = targetNum / (duration / stepTime);

            let current = 0;
            const interval = setInterval(() => {
                current += increment;
                if (current >= targetNum) {
                    clearInterval(interval);
                    el.textContent = target;
                } else {
                    let displayVal = Math.floor(current);
                    if (isK) displayVal += 'K';
                    if (isPlus) displayVal += '+';
                    el.textContent = displayVal;
                }
            }, stepTime);
        }

        // On page load, animate all counters
        window.addEventListener('DOMContentLoaded', () => {
            animateCount('counter1', '140+');
            animateCount('counter2', '24K+');
            animateCount('counter3', '8+');
        });


        AOS.init({
            duration: 1000,
            once: true
        });

        // Simple animation for elements
        document.addEventListener('DOMContentLoaded', function () {
            // Add animation delay to stagger the animations
            const animatedElements = document.querySelectorAll('.animate-fade-in');
            animatedElements.forEach((el, index) => {
                el.style.opacity = '0';
                setTimeout(() => {
                    el.style.opacity = '1';
                }, 100 * index);
            });

            // Smooth scroll for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href');
                    if (targetId === '#') return;

                    const targetElement = document.querySelector(targetId);
                    if (targetElement) {
                        window.scrollTo({
                            top: targetElement.offsetTop - 100,
                            behavior: 'smooth'
                        });
                    }
                });
            });

            // Simple slider logic
            const slides = document.querySelectorAll('.testimonial-slide');
            let current = 0;

            function showSlide(idx) {
                slides.forEach((slide, i) => {
                    slide.classList.toggle('hidden', i !== idx);
                });
            }

            document.getElementById('prev-slide').onclick = function () {
                current = (current - 1 + slides.length) % slides.length;
                showSlide(current);
            };
            document.getElementById('next-slide').onclick = function () {
                current = (current + 1) % slides.length;
                showSlide(current);
            };
        });

        window.addEventListener('load', function () {
            setTimeout(function () {
                openModal();
            }, 1000);
        });

        function openModal() {
            document.getElementById('contactModal').classList.add('show');
            document.body.style.overflow = 'hidden';
        }

        function closeModal() {
            document.getElementById('contactModal').classList.remove('show');
            document.body.style.overflow = 'auto';
        }

        // Close modal when clicking outside
        document.getElementById('contactModal').addEventListener('click', function (e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js"></script>
    <script>
        AOS.init();
    </script>
    <script>
        // Initialize AOS (Animate On Scroll)
        AOS.init({
            duration: 1000, // Animation duration in milliseconds
            once: true, // Whether animation should happen only once
        });
    </script>
</body>

</html>