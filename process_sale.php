<?php
// Get database connection using reliable method
$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';

// Check if database is set up first
require_once 'check_database_setup.php';
redirect_to_setup_if_needed();

// Check if the user is logged in
ensure_session_started();
require_login();

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Sanitize input data
    $contact_id = sanitize_input($_POST['contact_id']);
    $product_id = sanitize_input($_POST['product_id']);
    $payment_method = sanitize_input($_POST['payment_method']);
    $notes = sanitize_input($_POST['notes']);
    $sales_rep_id = $_SESSION['user_id']; // Current logged-in user
    
    // Set sale date to current date
    $sale_date = date('Y-m-d');
    
    // Validate required fields
    if (empty($contact_id) || empty($product_id) || empty($payment_method)) {
        // Redirect back with error
        header("Location: sales.php?error=1&message=" . urlencode("All fields are required"));
        exit;
    }
    
    // Always get the current price from the products table to ensure we use the correct price
    // This ensures that even if someone tries to manipulate the form data, we use the official price
    $get_product_price_query = "SELECT price FROM products WHERE id = ?";
    $get_price_stmt = mysqli_prepare($conn, $get_product_price_query);
    mysqli_stmt_bind_param($get_price_stmt, "i", $product_id);
    mysqli_stmt_execute($get_price_stmt);
    $price_result = mysqli_stmt_get_result($get_price_stmt);
    
    if (mysqli_num_rows($price_result) == 0) {
        // Product not found
        header("Location: sales.php?error=1&message=" . urlencode("Selected product not found"));
        exit;
    }
    
    $price_row = mysqli_fetch_assoc($price_result);
    $total_amount = $price_row['price']; // Use the price from the database
    $quantity = 1; // Default quantity

    // Insert sale into database with new structure
    $sql = "INSERT INTO sales (contact_id, product_id, quantity, unit_price, total_amount, sale_date, payment_method, sales_rep_id, notes, invoice_status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending')";

    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "iiiddssss", $contact_id, $product_id, $quantity, $total_amount, $total_amount, $sale_date, $payment_method, $sales_rep_id, $notes);
    
    if (mysqli_stmt_execute($stmt)) {
        // Get the sale ID
        $sale_id = mysqli_insert_id($conn);

        // Since we're using unified sales table, no separate invoice table needed
        // The invoice_status is already set in the sales record

        // Redirect to success page
        header("Location: sales.php?success=1&message=" . urlencode("Sale recorded successfully"));
        exit;
    } else {
        // Redirect back with error
        header("Location: sales.php?error=1&message=" . urlencode("Error recording sale: " . mysqli_error($conn)));
        exit;
    }
} else {
    // Redirect to sales page if accessed directly
    header("Location: sales.php");
    exit;
}
?>
