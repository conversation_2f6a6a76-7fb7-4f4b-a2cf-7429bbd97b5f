CREATE DATABASE IF NOT EXISTS lead_management;
USE lead_management;

-- employees table
CREATE TABLE IF NOT EXISTS employees (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    role ENUM('admin', 'sales_rep', 'manager') NOT NULL,
    first_name VA<PERSON>HA<PERSON>(50) NOT NULL,
    last_name VA<PERSON>HAR(50) NOT NULL,
    remember_token VARCHAR(64) DEFAULT NULL,
    token_expiry DATETIME DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- contacts table 
CREATE TABLE IF NOT EXISTS contacts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    first_name VARCHAR(50) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    country_code VARCHAR(10) DEFAULT '+91',
    address TEXT,
    aadhar_card VARCHAR(12),
    pan_card VARCHAR(10),
    source VARCHAR(50),
    customer_interest TEXT,
    category VARCHAR(50),

    -- Referral tracking
    is_referral BOOLEAN DEFAULT FALSE,
    referred_by_contact_id INT NULL,
    referrer_name VARCHAR(100) NULL,
    referrer_phone VARCHAR(20) NULL,
    referrer_relationship VARCHAR(50) NULL,

    -- Assignment
    assigned_to INT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- Foreign key constraints
    FOREIGN KEY (assigned_to) REFERENCES employees(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES employees(id) ON DELETE SET NULL,

    -- Indexes for better performance
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_assigned_to (assigned_to),
    INDEX idx_created_by (created_by),
    INDEX idx_first_name (first_name),
    INDEX idx_last_name (last_name)
);

-- Create nominees table (linked to contacts)
CREATE TABLE IF NOT EXISTS nominees (
    id INT AUTO_INCREMENT PRIMARY KEY,
    contact_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    relationship VARCHAR(50) NOT NULL,
    aadhar_id VARCHAR(12) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (contact_id) REFERENCES contacts(id) ON DELETE CASCADE
);

-- Create products table
CREATE TABLE IF NOT EXISTS products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    category VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create consolidated sales table with all required columns
CREATE TABLE IF NOT EXISTS sales (
    id INT AUTO_INCREMENT PRIMARY KEY,
    contact_id INT NOT NULL,
    sales_rep_id INT,
    sale_date DATE NOT NULL,
    payment_method VARCHAR(50),
    notes TEXT,

    -- Product and pricing 
    product_id INT NOT NULL,
    quantity DECIMAL(10,2) NOT NULL DEFAULT 1,
    unit_price DECIMAL(15,2) NOT NULL,
    subtotal DECIMAL(15,2) NOT NULL,
    tax DECIMAL(15,2) DEFAULT 0,
    total_amount DECIMAL(15,2) NOT NULL,

    -- Invoice information
    invoice_number VARCHAR(50),
    invoice_status ENUM('pending', 'paid', 'cancelled') NOT NULL DEFAULT 'pending',
    invoice_date DATE,

    -- Serial number for product tracking
    serial_number VARCHAR(100),

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- Foreign key constraints
    FOREIGN KEY (contact_id) REFERENCES contacts(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (sales_rep_id) REFERENCES employees(id) ON DELETE SET NULL,

    -- Indexes for better performance
    INDEX idx_contact_id (contact_id),
    INDEX idx_product_id (product_id),
    INDEX idx_sales_rep_id (sales_rep_id),
    INDEX idx_sale_date (sale_date),
    INDEX idx_invoice_number (invoice_number)
);


-- Username: admin_123, Password: Admin@123
INSERT INTO employees (username, password, email, role, first_name, last_name)
VALUES ('admin_123', '$2y$10$OKSVcFcz5dmG4OIeatUOBeMJo2zKQu1yDzWXbkoyQJ3E7lLflNZYq', '<EMAIL>', 'admin', 'Admin', 'User')
ON DUPLICATE KEY UPDATE username = username;