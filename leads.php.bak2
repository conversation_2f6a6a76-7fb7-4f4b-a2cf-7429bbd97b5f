<?php
// Get database connection using reliable method
$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';

// Check if database is set up first
require_once 'check_database_setup.php';
redirect_to_setup_if_needed();

// Check if the user is logged in
ensure_session_started();
require_login();

$is_sales_rep = isset($_SESSION['role']) && $_SESSION['role'] === 'sales_rep';
$sales_rep_id = $is_sales_rep ? $_SESSION['user_id'] : 0;

// Handle bulk actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['bulk_action'])) {
    $action = $_POST['bulk_action'];
    $selected_leads = isset($_POST['selected_leads']) ? $_POST['selected_leads'] : [];

    if (!empty($selected_leads) && !empty($action)) {
        $lead_ids = array_map('intval', $selected_leads);
        $placeholders = str_repeat('?,', count($lead_ids) - 1) . '?';

        if ($action === 'delete') {
            $delete_sql = "DELETE FROM contacts WHERE id IN ($placeholders) AND status LIKE 'lead_%'";
            $stmt = mysqli_prepare($conn, $delete_sql);
            mysqli_stmt_bind_param($stmt, str_repeat('i', count($lead_ids)), ...$lead_ids);

            if (mysqli_stmt_execute($stmt)) {
                $success_message = count($lead_ids) . " lead(s) deleted successfully.";
            } else {
                $error_message = "Error deleting leads: " . mysqli_error($conn);
            }
        } elseif ($action === 'assign' && isset($_POST['assign_to_user']) && !empty($_POST['assign_to_user'])) {
            $assign_to = intval($_POST['assign_to_user']);
            if ($assign_to > 0) {
                $assign_sql = "UPDATE contacts SET assigned_to = ? WHERE id IN ($placeholders) AND status LIKE 'lead_%'";
                $stmt = mysqli_prepare($conn, $assign_sql);
                $params = array_merge([$assign_to], $lead_ids);
                mysqli_stmt_bind_param($stmt, 'i' . str_repeat('i', count($lead_ids)), ...$params);

                if (mysqli_stmt_execute($stmt)) {
                    $success_message = count($lead_ids) . " lead(s) assigned successfully.";
                    
                    // Get the sales rep details for email notification
                    $sales_rep_sql = "SELECT first_name, last_name, email FROM employees WHERE id = ?";
                    $sales_rep_stmt = mysqli_prepare($conn, $sales_rep_sql);
                    mysqli_stmt_bind_param($sales_rep_stmt, "i", $assign_to);
                    mysqli_stmt_execute($sales_rep_stmt);
                    $sales_rep_result = mysqli_stmt_get_result($sales_rep_stmt);
                    $sales_rep = mysqli_fetch_assoc($sales_rep_result);
                    
                    // Create notification for the sales rep
                    $notification_title = "Leads Assigned";
                    $notification_message = count($lead_ids) . " lead(s) have been assigned to you.";
                    
                    $notify_sql = "INSERT INTO notifications (user_id, title, message, type, related_to, related_id, created_at) 
                                  VALUES (?, ?, ?, 'lead_assigned', 'contacts', NULL, NOW())";
                    $notify_stmt = mysqli_prepare($conn, $notify_sql);
                    mysqli_stmt_bind_param($notify_stmt, "iss", $assign_to, $notification_title, $notification_message);
                    mysqli_stmt_execute($notify_stmt);
                    
                    // Send email notifications to each lead
                    foreach ($lead_ids as $lead_id) {
                        // Get lead details
                        $lead_sql = "SELECT first_name, last_name, email FROM contacts WHERE id = ?";
                        $lead_stmt = mysqli_prepare($conn, $lead_sql);
                        mysqli_stmt_bind_param($lead_stmt, "i", $lead_id);
                        mysqli_stmt_execute($lead_stmt);
                        $lead_result = mysqli_stmt_get_result($lead_stmt);
                        $lead = mysqli_fetch_assoc($lead_result);
                        
                        // Get the lead_assigned email template
                        $template_sql = "SELECT subject, body FROM email_templates WHERE name = 'lead_assigned' LIMIT 1";
                        $template_result = mysqli_query($conn, $template_sql);
                        
                        if ($template = mysqli_fetch_assoc($template_result)) {
                            $subject = $template['subject'];
                            $body = $template['body'];
                            
                            // Replace placeholders with actual data
                            $body = str_replace('{{first_name}}', $lead['first_name'], $body);
                            $body = str_replace('{{sales_rep_name}}', $sales_rep['first_name'] . ' ' . $sales_rep['last_name'], $body);
                            $body = str_replace('{{sales_rep_email}}', $sales_rep['email'], $body);
                            
                            // Send the email
                            $email_result = send_email($lead['email'], $subject, $body);
                            
                            // Log the email
                            $log_status = $email_result['success'] ? 'sent' : 'failed';
                            $log_error = $email_result['success'] ? NULL : $email_result['message'];
                            
                            $log_sql = "INSERT INTO email_logs (recipient, subject, template_id, status, error_message, sent_at) 
                                       VALUES (?, ?, (SELECT id FROM email_templates WHERE name = 'lead_assigned' LIMIT 1), ?, ?, NOW())";
                            $log_stmt = mysqli_prepare($conn, $log_sql);
                            mysqli_stmt_bind_param($log_stmt, "ssss", $lead['email'], $subject, $log_status, $log_error);
                            mysqli_stmt_execute($log_stmt);
                        }
                    }
                } else {
                    $error_message = "Error assigning leads: " . mysqli_error($conn);
                }
            } else {
                $error_message = "Please select a valid user to assign leads to.";
            }
        } elseif ($action === 'convert' && isset($_POST['convert_status'])) {
            $new_status = $_POST['convert_status'];
            
            if ($new_status === 'customer_active') {
                // Convert leads to customers
                $convert_sql = "UPDATE contacts SET status = 'customer_active' WHERE id IN ($placeholders) AND status LIKE 'lead_%'";
                $stmt = mysqli_prepare($conn, $convert_sql);
                mysqli_stmt_bind_param($stmt, str_repeat('i', count($lead_ids)), ...$lead_ids);
                
                if (mysqli_stmt_execute($stmt)) {
                    $success_message = count($lead_ids) . " lead(s) converted to customers successfully.";
                    
                    // Send email notifications to each converted lead
                    foreach ($lead_ids as $lead_id) {
                        // Get lead details
                        $lead_sql = "SELECT c.first_name, c.last_name, c.email, c.assigned_to, 
                                    CONCAT(e.first_name, ' ', e.last_name) as sales_rep_name 
                                    FROM contacts c
                                    LEFT JOIN employees e ON c.assigned_to = e.id
                                    WHERE c.id = ?";
                        $lead_stmt = mysqli_prepare($conn, $lead_sql);
                        mysqli_stmt_bind_param($lead_stmt, "i", $lead_id);
                        mysqli_stmt_execute($lead_stmt);
                        $lead_result = mysqli_stmt_get_result($lead_stmt);
                        $lead = mysqli_fetch_assoc($lead_result);
                        
                        // Create notification for the assigned sales rep
                        if ($lead['assigned_to']) {
                            $notification_title = "Lead Converted";
                            $notification_message = "Lead {$lead['first_name']} {$lead['last_name']} has been converted to a customer.";
                            
                            $notify_sql = "INSERT INTO notifications (user_id, title, message, type, related_to, related_id, created_at) 
                                          VALUES (?, ?, ?, 'lead_converted', 'contacts', ?, NOW())";
                            $notify_stmt = mysqli_prepare($conn, $notify_sql);
                            mysqli_stmt_bind_param($notify_stmt, "issi", $lead['assigned_to'], $notification_title, $notification_message, $lead_id);
                            mysqli_stmt_execute($notify_stmt);
                        }
                        
                        // Get the lead_converted email template
                        $template_sql = "SELECT subject, body FROM email_templates WHERE name = 'lead_converted' LIMIT 1";
                        $template_result = mysqli_query($conn, $template_sql);
                        
                        if ($template = mysqli_fetch_assoc($template_result)) {
                            $subject = $template['subject'];
                            $body = $template['body'];
                            
                            // Replace placeholders with actual data
                            $body = str_replace('{{first_name}}', $lead['first_name'], $body);
                            $body = str_replace('{{sales_rep_name}}', $lead['sales_rep_name'] ?? 'our team', $body);
                            
                            // Send the email
                            $email_result = send_email($lead['email'], $subject, $body);
                            
                            // Log the email
                            $log_status = $email_result['success'] ? 'sent' : 'failed';
                            $log_error = $email_result['success'] ? NULL : $email_result['message'];
                            
                            $log_sql = "INSERT INTO email_logs (recipient, subject, template_id, status, error_message, sent_at) 
                                       VALUES (?, ?, (SELECT id FROM email_templates WHERE name = 'lead_converted' LIMIT 1), ?, ?, NOW())";
                            $log_stmt = mysqli_prepare($conn, $log_sql);
                            mysqli_stmt_bind_param($log_stmt, "ssss", $lead['email'], $subject, $log_status, $log_error);
                            mysqli_stmt_execute($log_stmt);
                        }
                    }
                } else {
                    $error_message = "Error converting leads: " . mysqli_error($conn);
                }
            } else {
                // Update lead status
                $update_sql = "UPDATE contacts SET status = ? WHERE id IN ($placeholders) AND status LIKE 'lead_%'";
                $stmt = mysqli_prepare($conn, $update_sql);
                $params = array_merge([$new_status], $lead_ids);
                mysqli_stmt_bind_param($stmt, 's' . str_repeat('i', count($lead_ids)), ...$params);
                
                if (mysqli_stmt_execute($stmt)) {
                    $status_text = str_replace('lead_', '', $new_status);
                    $status_text = ucfirst($status_text);
                    $success_message = count($lead_ids) . " lead(s) marked as $status_text successfully.";
                } else {
                    $error_message = "Error updating lead status: " . mysqli_error($conn);
                }
            }
        } elseif ($action === 'followup') {
            // Send follow-up emails to selected leads
            $success_count = 0;
            $error_count = 0;
            
            foreach ($lead_ids as $lead_id) {
                // Get lead details
                $lead_sql = "SELECT c.first_name, c.last_name, c.email, c.assigned_to, 
                            CONCAT(e.first_name, ' ', e.last_name) as sales_rep_name 
                            FROM contacts c
                            LEFT JOIN employees e ON c.assigned_to = e.id
                            WHERE c.id = ?";
                $lead_stmt = mysqli_prepare($conn, $lead_sql);
                mysqli_stmt_bind_param($lead_stmt, "i", $lead_id);
                mysqli_stmt_execute($lead_stmt);
                $lead_result = mysqli_stmt_get_result($lead_stmt);
                $lead = mysqli_fetch_assoc($lead_result);
                
                // Get the lead_followup email template
                $template_sql = "SELECT subject, body FROM email_templates WHERE name = 'lead_followup' LIMIT 1";
                $template_result = mysqli_query($conn, $template_sql);
                
                if ($template = mysqli_fetch_assoc($template_result)) {
                    $subject = $template['subject'];
                    $body = $template['body'];
                    
                    // Replace placeholders with actual data
                    $body = str_replace('{{first_name}}', $lead['first_name'], $body);
                    $body = str_replace('{{sales_rep_name}}', $lead['sales_rep_name'] ?? 'our team', $body);
                    
                    // Send the email
                    $email_result = send_email($lead['email'], $subject, $body);
                    
                    // Log the email
                    $log_status = $email_result['success'] ? 'sent' : 'failed';
                    $log_error = $email_result['success'] ? NULL : $email_result['message'];
                    
                    $log_sql = "INSERT INTO email_logs (recipient, subject, template_id, status, error_message, sent_at) 
                               VALUES (?, ?, (SELECT id FROM email_templates WHERE name = 'lead_followup' LIMIT 1), ?, ?, NOW())";
                    $log_stmt = mysqli_prepare($conn, $log_sql);
                    mysqli_stmt_bind_param($log_stmt, "ssss", $lead['email'], $subject, $log_status, $log_error);
                    mysqli_stmt_execute($log_stmt);
                    
                    if ($email_result['success']) {
                        $success_count++;
                    } else {
                        $error_count++;
                    }
                }
            }
            
            if ($success_count > 0) {
                $success_message = "Follow-up emails sent to $success_count lead(s) successfully.";
                if ($error_count > 0) {
                    $success_message .= " $error_count email(s) failed to send.";
                }
            } else {
                $error_message = "Error sending follow-up emails.";
            }
        }
    } else {
        if (empty($selected_leads)) {
            $error_message = "Please select at least one lead.";
        } elseif (empty($action)) {
            $error_message = "Please choose an action.";
        } else {
            $error_message = "Please select leads and choose an action.";
        }
    }
}

// Get filter parameters
$filter = isset($_GET['filter']) ? $_GET['filter'] : '';
$search = isset($_GET['search']) ? $_GET['search'] : '';

// Pagination parameters
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$records_per_page = 10;
$offset = ($page - 1) * $records_per_page;

// Build filtered query conditions
$where_conditions = ["status LIKE 'lead_%'"]; // Only show leads, not customers
$params = [];
$param_types = '';

if ($is_sales_rep) {
    $where_conditions[] = "assigned_to = ?";
    $params[] = $sales_rep_id;
    $param_types .= 'i';
}

// For the main query with table aliases
$where_conditions_with_alias = [];
foreach ($where_conditions as $condition) {
    $where_conditions_with_alias[] = str_replace(['status', 'assigned_to', 'first_name', 'last_name', 'email', 'phone'], 
                                               ['c.status', 'c.assigned_to', 'c.first_name', 'c.last_name', 'c.email', 'c.phone'], 
                                               $condition);
}

if (!empty($filter)) {
    // Handle different filter types
    switch ($filter) {
        case 'new':
            $where_conditions[] = "status = 'lead_new'";
            break;
        case 'contacted':
            $where_conditions[] = "status = 'lead_contacted'";
            break;
        case 'qualified':
            $where_conditions[] = "status = 'lead_qualified'";
            break;
        case 'lost':
            $where_conditions[] = "status = 'lead_lost'";
            break;
        case 'assigned':
            $where_conditions[] = "assigned_to IS NOT NULL";
            break;
        case 'unassigned':
            $where_conditions[] = "assigned_to IS NULL";
            break;
    }
}

if (!empty($search)) {
    $where_conditions[] = "(first_name LIKE ? OR last_name LIKE ? OR email LIKE ? OR phone LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $param_types .= 'ssss';
}

$where_clause = !empty($where_conditions) ? " WHERE " . implode(" AND ", $where_conditions) : "";

// Get total count for pagination
$count_sql = "SELECT COUNT(*) as total FROM contacts " . $where_clause;
if (!empty($params)) {
    $count_stmt = mysqli_prepare($conn, $count_sql);
    mysqli_stmt_bind_param($count_stmt, $param_types, ...$params);
    mysqli_stmt_execute($count_stmt);
    $count_result = mysqli_stmt_get_result($count_stmt);
} else {
    $count_result = mysqli_query($conn, $count_sql);
}
$total_records = mysqli_fetch_assoc($count_result)['total'];
$total_pages = ceil($total_records / $records_per_page);

// Build main query with pagination
$sql = "SELECT c.*,
        CONCAT(c.first_name, ' ', c.last_name) as full_name,
        CONCAT(u.first_name, ' ', u.last_name) as assigned_to_name,
        CONCAT(u2.first_name, ' ', u2.last_name) as created_by_name
        FROM contacts c
        LEFT JOIN employees u ON c.assigned_to = u.id
        LEFT JOIN employees u2 ON c.created_by = u2.id" .
        $where_clause .
        " ORDER BY c.created_at DESC LIMIT ?, ?";

// Add pagination parameters
$params[] = $offset;
$params[] = $records_per_page;
$param_types .= 'ii';

// Execute the query
$stmt = mysqli_prepare($conn, $sql);
mysqli_stmt_bind_param($stmt, $param_types, ...$params);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

// Get all employees for assignment dropdown
$employees_sql = "SELECT id, CONCAT(first_name, ' ', last_name) as name FROM employees WHERE role IN ('sales_rep', 'manager')";
$employees_result = mysqli_query($conn, $employees_sql);
$employees = [];
while ($row = mysqli_fetch_assoc($employees_result)) {
    $employees[] = $row;
}

// Get statistics
if ($is_sales_rep) {
    // Stats for Sales Rep
    $stats_sql = "SELECT 
                 COUNT(*) as total_leads,
                 SUM(CASE WHEN status = 'lead_new' THEN 1 ELSE 0 END) as new_leads,
                 SUM(CASE WHEN status = 'lead_contacted' THEN 1 ELSE 0 END) as contacted_leads,
                 SUM(CASE WHEN status = 'lead_qualified' THEN 1 ELSE 0 END) as qualified_leads
                 FROM contacts WHERE assigned_to = ? AND status LIKE 'lead_%'";
    $stmt = mysqli_prepare($conn, $stats_sql);
    mysqli_stmt_bind_param($stmt, 'i', $sales_rep_id);
    mysqli_stmt_execute($stmt);
    $stats = mysqli_fetch_assoc(mysqli_stmt_get_result($stmt));
} else {
    // Stats for Admin/Manager
    $stats_sql = "SELECT 
                 COUNT(*) as total_leads,
                 SUM(CASE WHEN status = 'lead_new' THEN 1 ELSE 0 END) as new_leads,
                 SUM(CASE WHEN status = 'lead_contacted' THEN 1 ELSE 0 END) as contacted_leads,
                 SUM(CASE WHEN status = 'lead_qualified' THEN 1 ELSE 0 END) as qualified_leads,
                 SUM(CASE WHEN assigned_to IS NOT NULL THEN 1 ELSE 0 END) as assigned_leads,
                 SUM(CASE WHEN assigned_to IS NULL THEN 1 ELSE 0 END) as unassigned_leads
                 FROM contacts WHERE status LIKE 'lead_%'";
    $stats_result = mysqli_query($conn, $stats_sql);
    $stats = mysqli_fetch_assoc($stats_result);
}

// Include header
$page_title = "Leads Management";
include 'includes/header.php';
?>

<div class="flex flex-col min-h-screen bg-gray-100">
    <?php include 'includes/navigation.php'; ?>
    
    <div class="flex-grow p-4 md:p-8 ml-0 min-[870px]:ml-60 mt-16">
        <div class="max-w-7xl mx-auto">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
                <h1 class="text-2xl md:text-3xl font-bold text-gray-800 mb-4 md:mb-0">
                    <?php echo $is_sales_rep ? "My Leads" : "Leads Management"; ?>
                </h1>
                
                <div class="flex flex-col sm:flex-row gap-3">
                    <a href="add_lead.php" class="inline-flex items-center justify-center bg-[#1E3E62] text-white py-2 px-4 rounded-md hover:bg-[#0B192C] transition-colors">
                        <i class="fas fa-plus mr-2"></i> Add New Lead
                    </a>
                    
                    <?php if (!$is_sales_rep): ?>
                    <a href="import_leads.php" class="inline-flex items-center justify-center bg-[#FF6500] text-white py-2 px-4 rounded-md hover:bg-orange-600 transition-colors">
                        <i class="fas fa-file-import mr-2"></i> Import Leads
                    </a>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Stats Cards -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mb-6">
                <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-blue-500">
                    <h3 class="text-gray-500 text-sm font-medium">Total Leads</h3>
                    <p class="text-2xl font-bold text-gray-800"><?php echo $stats['total_leads']; ?></p>
                </div>
                
                <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-green-500">
                    <h3 class="text-gray-500 text-sm font-medium">New Leads</h3>
                    <p class="text-2xl font-bold text-gray-800"><?php echo $stats['new_leads']; ?></p>
                </div>
                
                <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-yellow-500">
                    <h3 class="text-gray-500 text-sm font-medium">Contacted</h3>
                    <p class="text-2xl font-bold text-gray-800"><?php echo $stats['contacted_leads']; ?></p>
                </div>
                
                <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-purple-500">
                    <h3 class="text-gray-500 text-sm font-medium">Qualified</h3>
                    <p class="text-2xl font-bold text-gray-800"><?php echo $stats['qualified_leads']; ?></p>
                </div>
                
                <?php if (!$is_sales_rep): ?>
                <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-indigo-500">
                    <h3 class="text-gray-500 text-sm font-medium">Assigned</h3>
                    <p class="text-2xl font-bold text-gray-800"><?php echo $stats['assigned_leads']; ?></p>
                </div>
                
                <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-red-500">
                    <h3 class="text-gray-500 text-sm font-medium">Unassigned</h3>
                    <p class="text-2xl font-bold text-gray-800"><?php echo $stats['unassigned_leads']; ?></p>
                </div>
                <?php endif; ?>
            </div>
            
            <!-- Filters and Search -->
            <div class="bg-white rounded-lg shadow-md p-4 mb-6">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                    <div class="flex flex-wrap gap-2">
                        <a href="?filter=" class="px-3 py-1 rounded-full text-sm <?php echo empty($filter) ? 'bg-[#1E3E62] text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'; ?>">
                            All
                        </a>
                        <a href="?filter=new" class="px-3 py-1 rounded-full text-sm <?php echo $filter === 'new' ? 'bg-[#1E3E62] text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'; ?>">
                            New
                        </a>
                        <a href="?filter=contacted" class="px-3 py-1 rounded-full text-sm <?php echo $filter === 'contacted' ? 'bg-[#1E3E62] text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'; ?>">
                            Contacted
                        </a>
                        <a href="?filter=qualified" class="px-3 py-1 rounded-full text-sm <?php echo $filter === 'qualified' ? 'bg-[#1E3E62] text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'; ?>">
                            Qualified
                        </a>
                        <a href="?filter=lost" class="px-3 py-1 rounded-full text-sm <?php echo $filter === 'lost' ? 'bg-[#1E3E62] text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'; ?>">
                            Lost
                        </a>
                        <?php if (!$is_sales_rep): ?>
                        <a href="?filter=assigned" class="px-3 py-1 rounded-full text-sm <?php echo $filter === 'assigned' ? 'bg-[#1E3E62] text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'; ?>">
                            Assigned
                        </a>
                        <a href="?filter=unassigned" class="px-3 py-1 rounded-full text-sm <?php echo $filter === 'unassigned' ? 'bg-[#1E3E62] text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'; ?>">
                            Unassigned
                        </a>
                        <?php endif; ?>
                    </div>
                    
                    <form action="" method="get" class="flex items-center">
                        <input type="hidden" name="filter" value="<?php echo htmlspecialchars($filter); ?>">
                        <div class="relative">
                            <input 
                                type="text" 
                                name="search" 
                                placeholder="Search leads..." 
                                value="<?php echo htmlspecialchars($search); ?>"
                                class="border border-gray-300 rounded-md py-2 px-4 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            >
                            <button type="submit" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <?php if (isset($success_message)): ?>
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6" role="alert">
                <p><?php echo $success_message; ?></p>
            </div>
            <?php endif; ?>
            
            <?php if (isset($error_message)): ?>
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
                <p><?php echo $error_message; ?></p>
            </div>
            <?php endif; ?>
            
            <!-- Leads Table -->
            <form action="" method="post">
                <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
                    <?php if (mysqli_num_rows($result) > 0): ?>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <div class="flex items-center">
                                            <input type="checkbox" id="select-all" class="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                            <label for="select-all" class="sr-only">Select All</label>
                                            Name
                                        </div>
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Contact Info
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Status
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Assigned To
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Created
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php while ($row = mysqli_fetch_assoc($result)): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <input type="checkbox" name="selected_leads[]" value="<?php echo $row['id']; ?>" class="lead-checkbox mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">
                                                    <?php echo htmlspecialchars($row['full_name']); ?>
                                                </div>
                                                <?php if (!empty($row['company'])): ?>
                                                <div class="text-sm text-gray-500">
                                                    <?php echo htmlspecialchars($row['company']); ?>
                                                </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="text-sm text-gray-900">
                                            <a href="mailto:<?php echo htmlspecialchars($row['email']); ?>" class="hover:text-blue-600">
                                                <?php echo htmlspecialchars($row['email']); ?>
                                            </a>
                                        </div>
                                        <?php if (!empty($row['phone'])): ?>
                                        <div class="text-sm text-gray-500">
                                            <a href="tel:<?php echo htmlspecialchars($row['country_code'] . $row['phone']); ?>" class="hover:text-blue-600">
                                                <?php echo htmlspecialchars($row['country_code'] . ' ' . $row['phone']); ?>
                                            </a>
                                        </div>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php
                                        $status_class = '';
                                        $status_text = '';
                                        
                                        switch ($row['status']) {
                                            case 'lead_new':
                                                $status_class = 'bg-blue-100 text-blue-800';
                                                $status_text = 'New';
                                                break;
                                            case 'lead_contacted':
                                                $status_class = 'bg-yellow-100 text-yellow-800';
                                                $status_text = 'Contacted';
                                                break;
                                            case 'lead_qualified':
                                                $status_class = 'bg-green-100 text-green-800';
                                                $status_text = 'Qualified';
                                                break;
                                            case 'lead_lost':
                                                $status_class = 'bg-red-100 text-red-800';
                                                $status_text = 'Lost';
                                                break;
                                            default:
                                                $status_class = 'bg-gray-100 text-gray-800';
                                                $status_text = ucfirst(str_replace('lead_', '', $row['status']));
                                        }
                                        ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $status_class; ?>">
                                            <?php echo $status_text; ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php if (!empty($row['assigned_to_name'])): ?>
                                            <?php echo htmlspecialchars($row['assigned_to_name']); ?>
                                        <?php else: ?>
                                            <span class="text-gray-400">Unassigned</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php echo date('M j, Y', strtotime($row['created_at'])); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <a href="view_lead.php?id=<?php echo $row['id']; ?>" class="text-blue-600 hover:text-blue-900 mr-3">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="edit_lead.php?id=<?php echo $row['id']; ?>" class="text-indigo-600 hover:text-indigo-900 mr-3">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="javascript:void(0);" onclick="confirmDelete(<?php echo $row['id']; ?>)" class="text-red-600 hover:text-red-900">
                                            <i class="fas fa-trash-alt"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Bulk Actions -->
                    <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex flex-wrap gap-4 items-center">
                        <select name="bulk_action" class="border border-gray-300 rounded-md py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Bulk Actions</option>
                            <option value="delete">Delete Selected</option>
                            <option value="convert">Convert to Customer</option>
                            <option value="followup">Send Follow-up Email</option>
                            <option value="assign">Assign to Sales Rep</option>
                            <option value="convert">Change Status</option>
                        </select>
                        
                        <select name="assign_to_user" class="border border-gray-300 rounded-md py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 hidden" id="assign-to-select">
                            <option value="">Select Sales Rep</option>
                            <?php foreach ($employees as $employee): ?>
                            <option value="<?php echo $employee['id']; ?>"><?php echo htmlspecialchars($employee['name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                        
                        <select name="convert_status" class="border border-gray-300 rounded-md py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 hidden" id="status-select">
                            <option value="">Change Status To</option>
                            <option value="lead_new">New</option>
                            <option value="lead_contacted">Contacted</option>
                            <option value="lead_qualified">Qualified</option>
                            <option value="lead_lost">Lost</option>
                            <option value="customer_active">Convert to Customer</option>
                        </select>
                        
                        <button type="submit" class="bg-[#1E3E62] text-white py-2 px-4 rounded-md hover:bg-[#0B192C] transition-colors">
                            Apply
                        </button>
                        
                        <span class="text-sm text-gray-500">
                            <?php echo $total_records; ?> lead(s) found
                        </span>
                    </div>
                    
                    <?php else: ?>
                    <div class="p-6 text-center">
                        <p class="text-gray-500 mb-4">No leads found.</p>
                        <a href="add_lead.php" class="inline-flex items-center justify-center bg-[#1E3E62] text-white py-2 px-4 rounded-md hover:bg-[#0B192C] transition-colors">
                            <i class="fas fa-plus mr-2"></i> Add New Lead
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </form>
            
            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
            <div class="flex justify-center mt-6">
                <nav class="inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <?php if ($page > 1): ?>
                    <a href="?page=<?php echo $page - 1; ?>&filter=<?php echo urlencode($filter); ?>&search=<?php echo urlencode($search); ?>" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <span class="sr-only">Previous</span>
                        <i class="fas fa-chevron-left"></i>
                    </a>
                    <?php else: ?>
                    <span class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-400 cursor-not-allowed">
                        <span class="sr-only">Previous</span>
                        <i class="fas fa-chevron-left"></i>
                    </span>
                    <?php endif; ?>
                    
                    <?php
                    $start_page = max(1, $page - 2);
                    $end_page = min($total_pages, $start_page + 4);
                    
                    if ($end_page - $start_page < 4 && $start_page > 1) {
                        $start_page = max(1, $end_page - 4);
                    }
                    
                    for ($i = $start_page; $i <= $end_page; $i++):
                    ?>
                    <a href="?page=<?php echo $i; ?>&filter=<?php echo urlencode($filter); ?>&search=<?php echo urlencode($search); ?>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 <?php echo $i === $page ? 'bg-blue-50 text-blue-600 z-10' : 'bg-white text-gray-500 hover:bg-gray-50'; ?> text-sm font-medium">
                        <?php echo $i; ?>
                    </a>
                    <?php endfor; ?>
                    
                    <?php if ($page < $total_pages): ?>
                    <a href="?page=<?php echo $page + 1; ?>&filter=<?php echo urlencode($filter); ?>&search=<?php echo urlencode($search); ?>" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <span class="sr-only">Next</span>
                        <i class="fas fa-chevron-right"></i>
                    </a>
                    <?php else: ?>
                    <span class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-400 cursor-not-allowed">
                        <span class="sr-only">Next</span>
                        <i class="fas fa-chevron-right"></i>
                    </span>
                    <?php endif; ?>
                </nav>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
// Select all checkbox functionality
document.getElementById('select-all').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.lead-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// Show/hide additional fields based on bulk action selection
document.querySelector('select[name="bulk_action"]').addEventListener('change', function() {
    const assignSelect = document.getElementById('assign-to-select');
    const statusSelect = document.getElementById('status-select');
    
    // Hide all conditional selects first
    assignSelect.classList.add('hidden');
    statusSelect.classList.add('hidden');
    
    // Show relevant select based on action
    if (this.value === 'assign') {
        assignSelect.classList.remove('hidden');
    } else if (this.value === 'convert') {
        statusSelect.classList.remove('hidden');
    }
});

// Confirm delete function
function confirmDelete(id) {
    if (confirm('Are you sure you want to delete this lead? This action cannot be undone.')) {
        window.location.href = 'delete_lead.php?id=' + id;
    }
}
</script>

<?php include 'includes/footer.php'; ?>