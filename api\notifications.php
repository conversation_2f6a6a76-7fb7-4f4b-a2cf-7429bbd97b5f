<?php
/**
 * Notifications API
 * 
 * This API handles fetching and managing notifications
 */

// Include necessary files
$conn = require_once '../get_db_connection.php';
require_once '../includes/functions.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if user is logged in
ensure_session_started();
if (!is_logged_in()) {
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized'
    ]);
    exit;
}

// Get current user ID
$user_id = $_SESSION['user_id'];

// Handle POST requests (mark notifications as read)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get JSON data
    $json_data = file_get_contents('php://input');
    $data = json_decode($json_data, true);
    
    if (isset($data['action']) && $data['action'] === 'mark_read') {
        // Mark all notifications as read for the current user
        $sql = "UPDATE notifications SET is_read = 1 WHERE user_id = ? AND is_read = 0";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "i", $user_id);
        
        if (mysqli_stmt_execute($stmt)) {
            echo json_encode([
                'success' => true,
                'message' => 'Notifications marked as read'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Error marking notifications as read'
            ]);
        }
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid action'
        ]);
    }
    exit;
}

// Handle GET requests (fetch notifications)
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    // Get unread notification count
    $count_sql = "SELECT COUNT(*) as unread_count FROM notifications WHERE user_id = ? AND is_read = 0";
    $count_stmt = mysqli_prepare($conn, $count_sql);
    mysqli_stmt_bind_param($count_stmt, "i", $user_id);
    mysqli_stmt_execute($count_stmt);
    $count_result = mysqli_stmt_get_result($count_stmt);
    $unread_count = mysqli_fetch_assoc($count_result)['unread_count'];
    
    // Get recent notifications (limit to 10)
    $sql = "SELECT * FROM notifications WHERE user_id = ? ORDER BY created_at DESC LIMIT 10";
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "i", $user_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    $notifications = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $notifications[] = [
            'id' => $row['id'],
            'title' => $row['title'],
            'message' => $row['message'],
            'type' => $row['type'],
            'related_to' => $row['related_to'],
            'related_id' => $row['related_id'],
            'is_read' => (bool)$row['is_read'],
            'created_at' => $row['created_at']
        ];
    }
    
    echo json_encode([
        'success' => true,
        'unread_count' => $unread_count,
        'notifications' => $notifications
    ]);
    exit;
}

// Handle other request methods
echo json_encode([
    'success' => false,
    'message' => 'Invalid request method'
]);
exit;