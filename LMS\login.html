<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>LMS - Login</title>
  <link rel="icon" type="image/png" href="/images/logo.title.png">
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: { primary: "#4A77A8", secondary: "#F7A041" },
          borderRadius: {
            none: "0px",
            sm: "4px",
            DEFAULT: "8px",
            md: "12px",
            lg: "16px",
            xl: "20px",
            "2xl": "24px",
            "3xl": "32px",
            full: "9999px",
            button: "8px",
          },
        },
      },
    };
  </script>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet" />
  <link
    href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Open+Sans:wght@400;500;600&display=swap"
    rel="stylesheet" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" />
  <link href="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.css" rel="stylesheet" />
  <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
  <style>
    :where([class^="ri-"])::before {
      content: "\f3c2";
    }

    body {
      /* font-family: 'Inter', sans-serif; */
      background-color: #fafafa;
    }

    input:focus {
      outline: none;
    }

    input:-webkit-autofill,
    input:-webkit-autofill:hover,
    input:-webkit-autofill:focus {
      -webkit-box-shadow: 0 0 0px 1000px rgb(201, 180, 180) inset;
      transition: background-color 5000s ease-in-out 0s;
    }

    input[type="password"] {
      letter-spacing: 0.2em;
    }

    .login-container {
      /* background-image: linear-gradient(to right, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.8)), url('https://readdy.ai/api/search-image?query=Futuristic%20space-themed%20digital%20landscape%20with%20purple%20and%20blue%20gradient%20night%20sky%2C%20geometric%20modern%20buildings%20with%20warm%20golden%20lighting%2C%20a%20bright%20moon%2C%20and%20mountains%20in%20the%20background.%20The%20scene%20has%20a%20dreamy%2C%20otherworldly%20quality%20with%20subtle%20floating%20elements%20and%20a%20curved%20edge%20design.%20The%20illustration%20style%20is%20clean%2C%20minimalist%20with%20soft%20gradients%20and%20geometric%20shapes.&width=800&height=600&seq=inspace-bg&orientation=landscape'); */
      background-size: cover;
      background-position: center;
    }

    .illustration {
      background-image: url('images/login.jpg');
      background-size: cover;
      background-position: center;
      border-radius: 0 0 0 120px;
    }
  </style>
  <script>
    function loadNavbar() {
      fetch('index.html')
        .then(response => response.text())
        .then(data => {
          const parser = new DOMParser();
          const doc = parser.parseFromString(data, 'text/html');
          const header = doc.querySelector('header');
          if (header) {
            document.getElementById('navbar-container').innerHTML = header.outerHTML;
          } else {
            console.error('Header not found in index.html');
          }
        })
        .catch(error => console.error('Error loading navbar:', error));
    }
  </script>
</head>

<body class="bg-white text-black font-sans">


  <header class="fixed w-full top-0 z-50">
    <div class="lg:max-w-full mx-auto px-4 sm:px-4 md:px-6 lg:px-8 py-2 sm:py-3 md:py-4">
      <div
        class=" bg-white backdrop-blur-lg rounded-lg sm:rounded-xl lg:rounded-2xl shadow-lg border border-white/20 mx-1 sm:mx-2 md:mx-3 lg:mx-4">
        <div class=" flex justify-between items-center h-12 sm:h-14 md:h-16 px-2 sm:px-4 md:px-6">

          <div>
            <h1
              class="text-lg sm:text-xl md:text-2xl bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent relative">
              <img src="images/logo.png" alt="LMS Logo" class="lg:w-32  w-20 object-contain">
            </h1>
          </div>

          <!-- Desktop Nav -->
          <nav class="hidden lg:flex justify-between items-center">
            <!-- logo -->


            <div class="flex  ">
              <a href="index.html"
                class="flex nav-link px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-all duration-300">
                Home
              </a>
              <a href="About.html"
                class="flex px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-all duration-300">
                About
              </a>
              <a href="products.html"
                class=" flex px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-all duration-300">
                Products
              </a>

              <!-- Features Dropdown -->
              <div class="relative">
                <button id="featuresDropdownBtn"
                  class="px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-all duration-300 flex items-center">
                  Features
                  <i class="ri-arrow-down-s-line ml-1 transition-transform duration-200"></i>
                </button>
                <div id="featuresDropdownMenu"
                  class="absolute top-full left-0 mt-2 w-64 bg-white rounded-xl shadow-2xl border border-gray-100 py-2 z-50 hidden">
                  <a href="leadmanagement.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-brain-line text-blue-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Lead Management</div>
                      <div class="text-xs text-gray-500">Smart lead prioritization</div>
                    </div>
                  </a>
                  <a href="salemanagement.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-settings-3-line text-green-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Sales Management</div>
                      <div class="text-xs text-gray-500">Streamline your process</div>
                    </div>
                  </a>
                  <a href="analytics.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-pie-chart-line text-purple-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Advanced Analytics</div>
                      <div class="text-xs text-gray-500">Detailed insights & reports</div>
                    </div>
                  </a>
                  <a href="taskmanager.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-smartphone-line text-orange-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Task Manager</div>
                      <div class="text-xs text-gray-500">Capture from anywhere</div>
                    </div>
                  </a>
                  <a href="customersupport.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-team-line text-indigo-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Customer Support</div>
                      <div class="text-xs text-gray-500">Work together effectively</div>
                    </div>
                  </a>
                  <a href="integration.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-teal-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-shield-check-line text-teal-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Integration</div>
                      <div class="text-xs text-gray-500">Connect seamlessly with tools</div>
                    </div>
                  </a>
                </div>
              </div>

            </div>
            <!-- login -->
            <div class="flex ">
              <a href="contact.html"
                class=" flex items-center font-bold px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black  transition-all duration-300">
                Contact
              </a>

              <a href="login.html"
                class=" flex items-center bg-orange-500 rounded-3xl font-bold px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-white  transition-all duration-300">
                Login
              </a>
            </div>

          </nav>

          <!-- Mobile Menu Button -->
          <div class="lg:hidden z-50">
            <button id="mobileMenuBtn" class="focus:outline-none relative w-6 sm:w-7 md:w-8 h-6 sm:h-7 md:h-8">
              <span
                class="absolute top-1 left-0 w-6 sm:w-7 md:w-8 h-0.5 bg-black transition-transform duration-300"></span>
              <span
                class="absolute top-3 left-0 w-6 sm:w-7 md:w-8 h-0.5 bg-black transition-opacity duration-300"></span>
              <span
                class="absolute top-5 left-0 w-6 sm:w-7 md:w-8 h-0.5 bg-black transition-transform duration-300"></span>
            </button>
          </div>
        </div>

        <!-- Mobile Nav Menu -->
        <div id="mobileMenu" class="lg:hidden px-3 sm:px-4 md:px-6 pb-4 sm:pb-5 md:pb-6 pt-2 hidden">
          <div class="flex flex-col space-y-2 sm:space-y-3 md:space-y-4">
            <a href="index.html"
              class="text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200">
              Home
            </a>
            <a href="About.html"
              class="text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200">
              About
            </a>
            <a href="products.html"
              class="text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200">
              Products
            </a>
            <a href="contact.html"
              class="text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200">
              Contact
            </a>

            <!-- Mobile Features Section -->
            <div>
              <button id="mobileFeaturesBtn"
                class="w-full text-left text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200 flex items-center justify-between">
                <span>Features</span>
                <i class="ri-arrow-down-s-line transition-transform duration-200"></i>
              </button>

              <div id="mobileFeaturesMenu" class="ml-4 mt-2 space-y-2 hidden">
                <a href="leadmanagement.html"
                  class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-brain-line mr-2"></i>Lead Management
                </a>
                <a href="salemanagement.html"
                  class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-settings-3-line mr-2"></i>Email
                  Marketing
                </a>
                <a href="analytics.html"
                  class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-pie-chart-line mr-2"></i>Advanced Analytics
                </a>
                <a href="taskmanager.html"
                  class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-smartphone-line mr-2"></i>Task Manager
                </a>
                <a href="customersupport.html"
                  class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-team-line mr-2"></i>Customer Support
                </a>
                <a href="integration.html"
                  class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-shield-check-line mr-2"></i>Integration
                </a>
              </div>
            </div>

            <a href="login.html"
              class="bg-[#FF6500] text-white px-4 sm:px-5 md:px-6 py-1.5 sm:py-2 md:py-2.5 text-sm sm:text-base md:text-lg rounded-full font-bold text-center mt-2 hover:bg-[#e55a00] transition-colors duration-200">
              Login
            </a>
          </div>
        </div>
      </div>
    </div>
  </header>
  
 <!-- Login Section -->
 <section class="min-h-screen flex items-center justify-center px-2 sm:px-4 pt-24 pb-10">
  <div class="w-full max-w-5xl bg-white rounded-xl shadow-2xl overflow-hidden flex flex-col md:flex-row">
      <!-- Left side - Login form -->
      <div class="w-full md:w-1/2 p-4 sm:p-8 flex flex-col justify-center bg-white max-w-md mx-auto md:max-w-none">
          <div class="mb-6">
              <div class="w-4 h-4 bg-primary rounded-full mb-6"></div>
              <h2 class="text-primary text-2xl font-bold mb-1">Welcome Back!</h2>
              <h1 class="text-3xl sm:text-4xl font-extrabold mb-2">
                  <span class="text-primary">Lead</span>
                  <span class="text-[#FF6500]">Management</span>
              </h1>
              <p class="text-gray-500 text-sm mb-4">Sign in to your account to continue</p>
          </div>
          <form class="space-y-6">
              <div class="space-y-2">
                  <label for="email" class="block text-sm text-primary font-medium">Email</label>
                  <input type="email" id="email"
                      class="w-full px-4 py-3 border border-gray-200 rounded-lg text-gray-700 focus:border-primary focus:ring-2 focus:ring-primary/30 transition-colors bg-gray-50 placeholder-gray-400"
                      placeholder="<EMAIL>" required />
              </div>
              <div class="space-y-2 relative">
                  <label for="password" class="block text-sm text-primary font-medium">Password</label>
                  <input type="password" id="password"
                      class="w-full px-4 py-3 border border-gray-200 rounded-lg text-gray-700 focus:border-primary focus:ring-2 focus:ring-primary/30 transition-colors bg-gray-50 placeholder-gray-400 pr-12"
                      placeholder="••••••••" required />
                  <button type="button" id="togglePassword" tabindex="-1"
                      class="absolute right-3 top-9 text-gray-400 hover:text-primary focus:outline-none">
                      <i class="ri-eye-off-line" id="eyeIcon"></i>
                  </button>
              </div>
              <button type="submit"
                  class="w-full bg-gradient-to-r from-orange-500 to-blue-500 text-white px-8 py-3 rounded-xl font-bold shadow-lg hover:from-blue-500 hover:to-orange-500 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-400">
                  LOGIN
              </button>
          </form>
          <div class="mt-6 text-center text-sm text-gray-500">
              Don't have an account?
              <a href="#" class="text-secondary font-semibold hover:underline">Contact Admin</a>
          </div>
      </div>

      <!-- Right side - Illustration -->
      <div class="w-full md:w-1/2 h-40 sm:h-64 md:h-auto flex items-center justify-center bg-gradient-to-br from-primary/80 to-secondary/80">
          <img src="images/login.jpg" alt="Login Illustration"
              class="object-cover w-full h-full hidden md:block rounded-r-xl" />
          <!-- Show a small illustration on mobile -->
          <img src="images/login.jpg" alt="Login Illustration Mobile"
              class="object-cover w-full h-full block md:hidden rounded-b-xl max-h-40" />
      </div>
  </div>
</section>


<!-- Footer -->
<footer class="bg-black text-white px-6 sm:px-8 lg:px-12 w-full text-sm sm:text-base">
  <div class="w-full">
    <div class="py-16 grid grid-cols-1 md:grid-cols-4 gap-10 text-center md:text-left">
      <!-- Logo & Description -->
      <div class="flex flex-col items-center md:items-start space-y-6">
        <img src="images/logo.png" alt="Logo" class="w-40 sm:w-48 h-auto bg-white" />
        <p class="text-gray-400 text-sm sm:text-base max-w-sm text-center md:text-left">
          Effortless payroll, seamless success – simplify your pay process today.
        </p>
        <div class="flex gap-5 justify-center md:justify-start">
         <!-- Social Icons -->
         <a href="#" class="text-gray-400 hover:text-white transition">
          <!-- Facebook -->
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"/>
          </svg>
        </a>
        <!-- Twitter -->
        <a href="#" class="text-gray-400 hover:text-white transition">
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"/>
          </svg>
        </a>
        <!-- Instagram -->
        <a href="#" class="text-gray-400 hover:text-white transition">
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M7.75 2h8.5A5.75 5.75 0 0122 7.75v8.5A5.75 5.75 0 0116.25 22h-8.5A5.75 5.75 0 012 16.25v-8.5A5.75 5.75 0 017.75 2zm0 1.5A4.25 4.25 0 003.5 7.75v8.5A4.25 4.25 0 007.75 20.5h8.5a4.25 4.25 0 004.25-4.25v-8.5A4.25 4.25 0 0016.25 3.5h-8.5zm8.75 2a1 1 0 110 2 1 1 0 010-2zM12 7a5 5 0 110 10 5 5 0 010-10zm0 1.5a3.5 3.5 0 100 7 3.5 3.5 0 000-7z"/>
          </svg>
        </a>
        <!-- LinkedIn -->
        <a href="#" class="text-gray-400 hover:text-white transition">
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M4.98 3.5C4.98 4.61 4.1 5.5 3 5.5S1.02 4.61 1.02 3.5 1.9 1.5 3 1.5 4.98 2.39 4.98 3.5zM.5 6h5V20h-5V6zm7.5 0h4.7v1.785h.066c.655-1.24 2.255-2.535 4.634-2.535 4.953 0 5.867 3.26 5.867 7.5V20h-5v-6.417c0-1.531-.027-3.5-2.134-3.5-2.138 0-2.466 1.668-2.466 3.39V20h-5V6z"/>
          </svg>
        </a>
        </div>
      </div>

      <!-- Quick Links -->
      <div class="space-y-4">
        <h3 class="text-base font-extrabold">Quick Links</h3>
        <ul class="space-y-2 text-gray-400 text-sm sm:text-base">
          <li><a href="index.html" class="hover:text-white">Home</a></li>
          <li><a href="about.html" class="hover:text-white">About Us</a></li>
          <li><a href="products.html" class="hover:text-white">Products</a></li>
          <li><a href="contact.html" class="hover:text-white">Contact Us</a></li>
          <li><a href="login.html" class="hover:text-white">Login</a></li>
        </ul>
      </div>

      <!-- Products -->
      <div class="space-y-4">
        <h3 class="text-base font-extrabold">Products</h3>
        <ul class="space-y-2 text-gray-400 text-sm sm:text-base">
          <li><a href="leadmanagement.html" class="hover:text-white">Lead Management</a></li>
          <li><a href="salemanagement.html" class="hover:text-white">Sales Management</a></li>
          <li><a href="analytics.html" class="hover:text-white">Advance Analytics</a></li>
          <li><a href="customersupport.html" class="hover:text-white">Customer Management</a></li>
          <li><a href="integration.html" class="hover:text-white">Integration</a></li>
        </ul>
      </div>

      <!-- Contact -->
      <div class="space-y-4">
        <h3 class="text-lg font-extrabold">Contact</h3>
        <ul class="space-y-3 text-gray-400 text-sm">
          <li class="flex items-center justify-center md:justify-start gap-2">
            <svg class="w-4 h-4" stroke="currentColor" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            <a href="mailto:<EMAIL>" class="hover:text-white"><EMAIL></a>
          </li>
          <li class="flex items-center justify-center md:justify-start gap-2">
            <svg class="w-4 h-4" stroke="currentColor" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
            </svg>
            +1 (555) 482-9316
          </li>
          <li class="flex items-start justify-center md:justify-start gap-2">
            <svg class="w-4 h-4" stroke="currentColor" fill="none" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            <span>
             Gurgaon, Haryana
            </span>
          </li>
        </ul>
      </div>
    </div>

    <!-- Bottom -->
    <div class="border-t border-gray-800 py-4 flex flex-col sm:flex-row justify-between items-center text-gray-400 text-sm sm:text-base gap-4 text-center">
      <p>© 2025 LMS. All rights reserved.</p>
      <div class="flex gap-4">
        <a href="#" class="hover:text-white">Terms & Conditions</a>
        <a href="#" class="hover:text-white">Privacy Policy</a>
      </div>
    </div>
  </div>
</footer>


  <script>
    // Simple dropdown toggle for Features
    document.addEventListener('DOMContentLoaded', function () {
      const btn = document.getElementById('featuresDropdownBtn');
      const menu = document.getElementById('featuresDropdownMenu');
      btn.addEventListener('click', function (e) {
        e.stopPropagation();
        menu.classList.toggle('hidden');
      });
      // Close dropdown when clicking outside
      document.addEventListener('click', function (e) {
        if (!btn.contains(e.target) && !menu.contains(e.target)) {
          menu.classList.add('hidden');
        }
      });
    });

    // Mobile menu toggle
    document.addEventListener('DOMContentLoaded', function () {
      const mobileMenuBtn = document.getElementById('mobileMenuBtn');
      const mobileMenu = document.getElementById('mobileMenu');
      mobileMenuBtn.addEventListener('click', function (e) {
        e.stopPropagation();
        mobileMenu.classList.toggle('hidden');
      });
      document.addEventListener('click', function (e) {
        if (!mobileMenu.contains(e.target) && !mobileMenuBtn.contains(e.target)) {
          mobileMenu.classList.add('hidden');
        }
      });

      // Mobile features dropdown
      const mobileFeaturesBtn = document.getElementById('mobileFeaturesBtn');
      const mobileFeaturesMenu = document.getElementById('mobileFeaturesMenu');
      mobileFeaturesBtn.addEventListener('click', function (e) {
        e.stopPropagation();
        mobileFeaturesMenu.classList.toggle('hidden');
      });
      document.addEventListener('click', function (e) {
        if (!mobileFeaturesMenu.contains(e.target) && !mobileFeaturesBtn.contains(e.target)) {
          mobileFeaturesMenu.classList.add('hidden');
        }
      });
    });
  </script>

  <script src="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js"></script>
  <script>
    AOS.init();
  </script>
</body>

</html>