<?php
header('Content-Type: application/json');
require_once '../get_db_connection.php';

// --- API Key Authentication ---
$API_KEY = '12345'; // Change this to a secure value
$provided_key = $_SERVER['HTTP_X_API_KEY'] ?? '';
if ($provided_key !== $API_KEY) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

function sanitize($value) {
    return htmlspecialchars(strip_tags(trim($value)));
}

$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        if (isset($_GET['id'])) {
            $id = intval($_GET['id']);
            $stmt = mysqli_prepare($conn, "SELECT * FROM employees WHERE id = ?");
            mysqli_stmt_bind_param($stmt, 'i', $id);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $employee = mysqli_fetch_assoc($result);
            echo json_encode($employee ?: []);
        } else {
            $result = mysqli_query($conn, "SELECT * FROM employees");
            $employees = [];
            while ($row = mysqli_fetch_assoc($result)) { $employees[] = $row; }
            echo json_encode($employees);
        }
        break;
    case 'POST':
        $data = json_decode(file_get_contents('php://input'), true);
        if (!$data) { http_response_code(400); echo json_encode(['error' => 'Invalid JSON']); exit; }
        $username = sanitize($data['username'] ?? '');
        $password = $data['password'] ?? '';
        $email = filter_var($data['email'] ?? '', FILTER_VALIDATE_EMAIL) ? $data['email'] : '';
        $role = sanitize($data['role'] ?? '');
        $first_name = sanitize($data['first_name'] ?? '');
        $last_name = sanitize($data['last_name'] ?? '');
        if (!$username || !$password || !$email || !$role || !$first_name || !$last_name) {
            http_response_code(400); echo json_encode(['error' => 'Missing required fields']); exit;
        }
        $hashed = password_hash($password, PASSWORD_DEFAULT);
        $stmt = mysqli_prepare($conn, "INSERT INTO employees (username, password, email, role, first_name, last_name) VALUES (?, ?, ?, ?, ?, ?)");
        mysqli_stmt_bind_param($stmt, 'ssssss', $username, $hashed, $email, $role, $first_name, $last_name);
        $success = mysqli_stmt_execute($stmt);
        echo json_encode(['success' => $success, 'id' => mysqli_insert_id($conn)]);
        break;
    case 'PUT':
        if (!isset($_GET['id'])) { http_response_code(400); echo json_encode(['error' => 'Missing id']); exit; }
        $id = intval($_GET['id']);
        $data = json_decode(file_get_contents('php://input'), true);
        if (!$data) { http_response_code(400); echo json_encode(['error' => 'Invalid JSON']); exit; }
        $username = sanitize($data['username'] ?? '');
        $email = filter_var($data['email'] ?? '', FILTER_VALIDATE_EMAIL) ? $data['email'] : '';
        $role = sanitize($data['role'] ?? '');
        $first_name = sanitize($data['first_name'] ?? '');
        $last_name = sanitize($data['last_name'] ?? '');
        if (!$username || !$email || !$role || !$first_name || !$last_name) {
            http_response_code(400); echo json_encode(['error' => 'Missing required fields']); exit;
        }
        $stmt = mysqli_prepare($conn, "UPDATE employees SET username=?, email=?, role=?, first_name=?, last_name=? WHERE id=?");
        mysqli_stmt_bind_param($stmt, 'sssssi', $username, $email, $role, $first_name, $last_name, $id);
        $success = mysqli_stmt_execute($stmt);
        echo json_encode(['success' => $success]);
        break;
    case 'DELETE':
        if (!isset($_GET['id'])) { http_response_code(400); echo json_encode(['error' => 'Missing id']); exit; }
        $id = intval($_GET['id']);
        $stmt = mysqli_prepare($conn, "DELETE FROM employees WHERE id = ?");
        mysqli_stmt_bind_param($stmt, 'i', $id);
        $success = mysqli_stmt_execute($stmt);
        echo json_encode(['success' => $success]);
        break;
    default:
        http_response_code(405);
        echo json_encode(['error' => 'Method Not Allowed']);
}
