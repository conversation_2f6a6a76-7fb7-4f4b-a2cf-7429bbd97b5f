<?php
/**
 * Product Email Notification System
 * Sends emails to all leads and customers when new products are added
 */

require_once __DIR__ . '/phpmailer_config.php';
require_once __DIR__ . '/notification_helper.php';
require_once __DIR__ . '/email_templates.php';

/**
 * Send new product notification emails to all leads and customers
 * 
 * @param array $product Product details (id, name, price, category)
 * @param mysqli $conn Database connection
 * @return array Result with success status and statistics
 */
function send_new_product_notifications($product, $conn) {
    $results = [
        'success' => true,
        'total_sent' => 0,
        'leads_sent' => 0,
        'customers_sent' => 0,
        'failed' => 0,
        'errors' => []
    ];
    
    try {
        // Get all leads (from leads table)
        $leads_sql = "SELECT id, first_name, last_name, email, customer_interest 
                      FROM leads 
                      WHERE email IS NOT NULL AND email != ''";
        $leads_result = mysqli_query($conn, $leads_sql);
        
        // Get all customers (from contacts table)
        $customers_sql = "SELECT id, first_name, last_name, email, customer_interest 
                          FROM contacts 
                          WHERE email IS NOT NULL AND email != ''";
        $customers_result = mysqli_query($conn, $customers_sql);
        
        // Send emails to leads
        if ($leads_result) {
            while ($lead = mysqli_fetch_assoc($leads_result)) {
                $email_result = send_product_email($lead, $product, 'lead');
                if ($email_result['success']) {
                    $results['leads_sent']++;
                    $results['total_sent']++;
                } else {
                    $results['failed']++;
                    $results['errors'][] = "Lead {$lead['email']}: " . $email_result['message'];
                }
                
                // Add small delay to prevent overwhelming the SMTP server
                usleep(100000); // 0.1 second delay
            }
        }
        
        // Send emails to customers
        if ($customers_result) {
            while ($customer = mysqli_fetch_assoc($customers_result)) {
                $email_result = send_product_email($customer, $product, 'customer');
                if ($email_result['success']) {
                    $results['customers_sent']++;
                    $results['total_sent']++;
                } else {
                    $results['failed']++;
                    $results['errors'][] = "Customer {$customer['email']}: " . $email_result['message'];
                }
                
                // Add small delay to prevent overwhelming the SMTP server
                usleep(100000); // 0.1 second delay
            }
        }
        
        // Add notification to session
        if ($results['total_sent'] > 0) {
            add_success_notification(
                "Product Notifications Sent",
                "New product '{$product['name']}' notification sent to {$results['total_sent']} recipients ({$results['leads_sent']} leads, {$results['customers_sent']} customers)"
            );
        }
        
        if ($results['failed'] > 0) {
            add_warning_notification(
                "Some Email Notifications Failed",
                "{$results['failed']} email notifications failed to send. Check error logs for details."
            );
        }
        
    } catch (Exception $e) {
        $results['success'] = false;
        $results['errors'][] = "System error: " . $e->getMessage();
        
        add_error_notification(
            "Email Notification Error",
            "Failed to send product notifications: " . $e->getMessage()
        );
    }
    
    return $results;
}

/**
 * Send individual product notification email
 * 
 * @param array $recipient Recipient details (first_name, last_name, email, customer_interest)
 * @param array $product Product details (name, price, category)
 * @param string $type Type of recipient ('lead' or 'customer')
 * @return array Result with success status
 */
function send_product_email($recipient, $product, $type = 'customer') {
    // Generate subject using template system
    $subject = generate_email_subject('new_product', ['product_name' => $product['name']]);

    // Validate template variables
    $variables = [
        'product_name' => $product['name'],
        'product_price' => $product['price'],
        'product_category' => $product['category'],
        'first_name' => $recipient['first_name']
    ];

    $validation = validate_template_variables($variables);
    if (!$validation['valid']) {
        return [
            'success' => false,
            'message' => 'Missing template variables: ' . implode(', ', $validation['missing'])
        ];
    }

    // Create personalized email content
    $body = create_product_email_template($recipient, $product, $type);

    // Create plain text version
    $altBody = create_product_email_text($recipient, $product, $type);

    // Send email
    $result = send_email($recipient['email'], $subject, $body, $altBody);

    // Log template usage
    log_email_template_usage('new_product', $type, $result['success']);

    return $result;
}

/**
 * Create HTML email template for new product notification
 */
function create_product_email_template($recipient, $product, $type) {
    $first_name = htmlspecialchars($recipient['first_name']);
    $product_name = htmlspecialchars($product['name']);
    $product_price = number_format($product['price'], 2);
    $product_category = htmlspecialchars($product['category']);

    // Get template configuration
    $config = get_email_template_config();
    $content = get_email_content_sections();

    // Generate dynamic content
    $greeting = generate_greeting($type, $first_name);
    $features_list = generate_features_list('html');
    $cta_button = generate_cta($type, $product_name, 'html');
    $offer_days = $config['new_product']['offer_days'];

    $html = "
    <!DOCTYPE html>
    <html lang='en'>
    <head>
        <meta charset='UTF-8'>
        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
        <title>New Product Alert</title>
        " . get_email_styles() . "
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>{$content['header']['title']}</h1>
                <p>{$content['header']['subtitle']}</p>
            </div>

            <div class='content'>
                <h2>{$greeting}</h2>

                <p>{$content['intro'][$type]}</p>

                <div class='product-card'>
                    <h3>✨ {$product_name}</h3>
                    <div class='category'>{$product_category}</div>
                    <div class='price'>₹{$product_price}</div>

                    <div class='highlight'>
                        <strong>{$content['product_highlight']['title']}</strong><br>
                        {$content['product_highlight']['description']}
                    </div>
                </div>

                <p><strong>Why you'll love this product:</strong></p>
                {$features_list}

                <div style='text-align: center; margin: 30px 0;'>
                    {$cta_button}
                </div>

                <p><strong>{$content['offer']['title']}</strong> " . str_replace('{offer_days}', $offer_days, $content['offer']['description']) . "</p>

                <div class='highlight'>
                    <strong>{$content['contact']['title']}</strong><br>
                    Email: " . SMTP_FROM_EMAIL . "<br>
                    {$content['contact']['description']}
                </div>

                <p>{$content['footer']['thanks']}</p>

                <p>Best regards,<br>
                <strong>{$content['footer']['signature']}</strong></p>
            </div>

            <div class='footer'>
                <p>{$content['footer']['unsubscribe']}</p>
            </div>
        </div>
    </body>
    </html>";

    return $html;
}

/**
 * Create plain text version of the email
 */
function create_product_email_text($recipient, $product, $type) {
    $first_name = $recipient['first_name'];
    $product_name = $product['name'];
    $product_price = number_format($product['price'], 2);
    $product_category = $product['category'];

    // Get template configuration
    $config = get_email_template_config();
    $content = get_email_content_sections();

    // Generate dynamic content
    $greeting = generate_greeting($type, $first_name);
    $features_list = generate_features_list('text');
    $cta_text = generate_cta($type, $product_name, 'text');
    $offer_days = $config['new_product']['offer_days'];

    return "
🎉 NEW PRODUCT ALERT!

{$greeting}

{$content['intro'][$type]}

✨ {$product_name}
Category: {$product_category}
Price: ₹{$product_price}

🎯 Perfect for you!
{$content['product_highlight']['description']}

Why you'll love this product:
{$features_list}

🔥 LIMITED TIME OFFER: Contact us within {$offer_days} days for special pricing!

{$cta_text}

{$content['footer']['thanks']}

Best regards,
{$content['footer']['signature']}

---
{$content['footer']['unsubscribe']}
    ";
}
?>
