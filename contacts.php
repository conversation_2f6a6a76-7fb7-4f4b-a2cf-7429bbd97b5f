<?php
// Get database connection using reliable method
$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';

// Check if database is set up first
require_once 'check_database_setup.php';
redirect_to_setup_if_needed();

// Check if the user is logged in
ensure_session_started();
require_login();

$is_sales_rep = isset($_SESSION['role']) && $_SESSION['role'] === 'sales_rep';
$sales_rep_id = $is_sales_rep ? $_SESSION['user_id'] : 0;

// Handle bulk actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['bulk_action'])) {
    $action = $_POST['bulk_action'];
    $selected_contacts = isset($_POST['selected_contacts']) ? $_POST['selected_contacts'] : [];

    // Debug logging - remove this after testing
    error_log("Bulk action debug: " . print_r($_POST, true));

    if (!empty($selected_contacts) && !empty($action)) {
        $contact_ids = array_map('intval', $selected_contacts);
        $placeholders = str_repeat('?,', count($contact_ids) - 1) . '?';

        if ($action === 'delete') {
            $delete_sql = "DELETE FROM contacts WHERE id IN ($placeholders)";
            $stmt = mysqli_prepare($conn, $delete_sql);
            mysqli_stmt_bind_param($stmt, str_repeat('i', count($contact_ids)), ...$contact_ids);

            if (mysqli_stmt_execute($stmt)) {
                $success_message = count($contact_ids) . " contact(s) deleted successfully.";
            } else {
                $error_message = "Error deleting contacts: " . mysqli_error($conn);
            }
        } elseif ($action === 'assign' && isset($_POST['assign_to_user']) && !empty($_POST['assign_to_user'])) {
            $assign_to = intval($_POST['assign_to_user']);
            if ($assign_to > 0) {
                $assign_sql = "UPDATE contacts SET assigned_to = ? WHERE id IN ($placeholders)";
                $stmt = mysqli_prepare($conn, $assign_sql);
                $params = array_merge([$assign_to], $contact_ids);
                mysqli_stmt_bind_param($stmt, 'i' . str_repeat('i', count($contact_ids)), ...$params);

                if (mysqli_stmt_execute($stmt)) {
                    $success_message = count($contact_ids) . " contact(s) assigned successfully.";
                } else {
                    $error_message = "Error assigning contacts: " . mysqli_error($conn);
                }
            } else {
                $error_message = "Please select a valid user to assign contacts to.";
            }
        } elseif ($action === 'assign' && (!isset($_POST['assign_to_user']) || empty($_POST['assign_to_user']))) {
            $error_message = "Please select a user to assign contacts to.";
        } elseif ($action === 'unassign') {
            $unassign_sql = "UPDATE contacts SET assigned_to = NULL WHERE id IN ($placeholders)";
            $stmt = mysqli_prepare($conn, $unassign_sql);
            mysqli_stmt_bind_param($stmt, str_repeat('i', count($contact_ids)), ...$contact_ids);

            if (mysqli_stmt_execute($stmt)) {
                $success_message = count($contact_ids) . " contact(s) unassigned successfully.";
            } else {
                $error_message = "Error unassigning contacts: " . mysqli_error($conn);
            }
        }
    } else {
        if (empty($selected_contacts)) {
            $error_message = "Please select at least one contact.";
        } elseif (empty($action)) {
            $error_message = "Please choose an action.";
        } else {
            $error_message = "Please select contacts and choose an action.";
        }
    }
}

// Handle nominee actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['nominee_action'])) {
    $action = $_POST['nominee_action'];

    if ($action === 'add' && isset($_POST['contact_id'], $_POST['nominee_name'], $_POST['relationship'], $_POST['aadhar_id'])) {
        $contact_id = intval($_POST['contact_id']);
        $nominee_name = trim($_POST['nominee_name']);
        $relationship = trim($_POST['relationship']);
        $aadhar_id = trim($_POST['aadhar_id']);

        // Validate inputs
        if (!empty($nominee_name) && !empty($relationship) && !empty($aadhar_id)) {
            // Check if Aadhar ID already exists
            $check_sql = "SELECT id FROM nominees WHERE aadhar_id = ?";
            $check_stmt = mysqli_prepare($conn, $check_sql);
            mysqli_stmt_bind_param($check_stmt, 's', $aadhar_id);
            mysqli_stmt_execute($check_stmt);
            $check_result = mysqli_stmt_get_result($check_stmt);

            if (mysqli_num_rows($check_result) > 0) {
                $error_message = "A nominee with this Aadhar ID already exists.";
            } else {
                $insert_sql = "INSERT INTO nominees (contact_id, name, relationship, aadhar_id) VALUES (?, ?, ?, ?)";
                $stmt = mysqli_prepare($conn, $insert_sql);
                mysqli_stmt_bind_param($stmt, 'isss', $contact_id, $nominee_name, $relationship, $aadhar_id);

                if (mysqli_stmt_execute($stmt)) {
                    $success_message = "Nominee added successfully.";
                } else {
                    $error_message = "Error adding nominee: " . mysqli_error($conn);
                }
            }
        } else {
            $error_message = "Please fill in all nominee fields.";
        }
    } elseif ($action === 'delete' && isset($_POST['nominee_id'])) {
        $nominee_id = intval($_POST['nominee_id']);

        $delete_sql = "DELETE FROM nominees WHERE id = ?";
        $stmt = mysqli_prepare($conn, $delete_sql);
        mysqli_stmt_bind_param($stmt, 'i', $nominee_id);

        if (mysqli_stmt_execute($stmt)) {
            $success_message = "Nominee deleted successfully.";
        } else {
            $error_message = "Error deleting nominee: " . mysqli_error($conn);
        }
    }
}

// Get filter parameters
$filter = isset($_GET['filter']) ? $_GET['filter'] : '';
$search = isset($_GET['search']) ? $_GET['search'] : '';

// Pagination parameters
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$records_per_page = 10;
$offset = ($page - 1) * $records_per_page;

// Build filtered query conditions
$where_conditions = [];
$params = [];
$param_types = '';

if ($is_sales_rep) {
    $where_conditions[] = "c.assigned_to = ?";
    $params[] = $sales_rep_id;
    $param_types .= 'i';
}

if (!empty($filter)) {
    // Handle different filter types
    switch ($filter) {
        case 'assigned':
            $where_conditions[] = "c.assigned_to IS NOT NULL";
            break;
        case 'unassigned':
            $where_conditions[] = "c.assigned_to IS NULL";
            break;
        case 'referral':
            $where_conditions[] = "(c.is_referral = 1 OR c.referred_by_contact_id IS NOT NULL)";
            break;
        case 'direct':
            $where_conditions[] = "(c.is_referral IS NULL OR c.is_referral = 0) AND c.referred_by_contact_id IS NULL AND c.assigned_to IS NOT NULL";
            break;
    }
}

if (!empty($search)) {
    $where_conditions[] = "(c.first_name LIKE ? OR c.last_name LIKE ? OR c.email LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $param_types .= 'sss';
}

$where_clause = !empty($where_conditions) ? " WHERE " . implode(" AND ", $where_conditions) : "";

// Get total count for pagination
$count_sql = "SELECT COUNT(*) as total FROM contacts c " . $where_clause;
if (!empty($params)) {
    $count_stmt = mysqli_prepare($conn, $count_sql);
    mysqli_stmt_bind_param($count_stmt, $param_types, ...$params);
    mysqli_stmt_execute($count_stmt);
    $count_result = mysqli_stmt_get_result($count_stmt);
} else {
    $count_result = mysqli_query($conn, $count_sql);
}
$total_records = mysqli_fetch_assoc($count_result)['total'];
$total_pages = ceil($total_records / $records_per_page);

// Build main query with pagination - using customer_interest field for product interests
$sql = "SELECT c.*,
        CONCAT(c.first_name, ' ', c.last_name) as full_name,
        CONCAT(u.first_name, ' ', u.last_name) as assigned_to_name,
        CONCAT(u2.first_name, ' ', u2.last_name) as created_by_name,
        CONCAT(ref.first_name, ' ', ref.last_name) as referred_by_name,
        c.customer_interest as product_interests
        FROM contacts c
        LEFT JOIN employees u ON c.assigned_to = u.id
        LEFT JOIN employees u2 ON c.created_by = u2.id
        LEFT JOIN contacts ref ON c.referred_by_contact_id = ref.id" .
        $where_clause .
        " ORDER BY c.created_at DESC
        LIMIT ? OFFSET ?";

// Add pagination parameters
$params[] = $records_per_page;
$params[] = $offset;
$param_types .= 'ii';

if (!empty($params)) {
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, $param_types, ...$params);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
} else {
    $result = mysqli_query($conn, $sql);
}

// Get statistics
if ($is_sales_rep) {
    // Stats for Sales Rep
    $stats_sql = "SELECT COUNT(*) as total_contacts FROM contacts WHERE assigned_to = ?";
    $stmt = mysqli_prepare($conn, $stats_sql);
    mysqli_stmt_bind_param($stmt, 'i', $sales_rep_id);
    mysqli_stmt_execute($stmt);
    $total_contacts = mysqli_fetch_assoc(mysqli_stmt_get_result($stmt))['total_contacts'];

    $sales_sql = "SELECT COUNT(*) as total_sales, COUNT(DISTINCT contact_id) as converted_contacts FROM sales WHERE sales_rep_id = ?";
    $stmt = mysqli_prepare($conn, $sales_sql);
    mysqli_stmt_bind_param($stmt, 'i', $sales_rep_id);
    mysqli_stmt_execute($stmt);
    $sales_stats = mysqli_fetch_assoc(mysqli_stmt_get_result($stmt));
    $total_sales = $sales_stats['total_sales'];
    $converted_contacts = $sales_stats['converted_contacts'];

    $conversion_rate = ($total_contacts > 0) ? ($converted_contacts / $total_contacts) * 100 : 0;

    $stats = [
        'total_contacts' => $total_contacts,
        'total_sales' => $total_sales,
        'conversion_rate' => $conversion_rate
    ];

} else {
    // Stats for Admin/Manager
    $stats_sql = "SELECT
        COUNT(*) as total_contacts,
        SUM(CASE WHEN assigned_to IS NOT NULL THEN 1 ELSE 0 END) as assigned_contacts,
        SUM(CASE WHEN assigned_to IS NULL THEN 1 ELSE 0 END) as unassigned_contacts
        FROM contacts";
    $stats_result = mysqli_query($conn, $stats_sql);
    $stats = mysqli_fetch_assoc($stats_result);
}

// Get all nominees with contact information
$nominees_sql = "SELECT n.*, c.first_name, c.last_name, CONCAT(c.first_name, ' ', c.last_name) as contact_name
                 FROM nominees n
                 LEFT JOIN contacts c ON n.contact_id = c.id
                 ORDER BY n.created_at DESC";
$nominees_result = mysqli_query($conn, $nominees_sql);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="img/minilogo.jpg" type="image/x-icon">
    <title>Contacts Management - CRM System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .text-color { color: #FF6500; }
        .bg-color { background-color: #FF6500; }

        /* Contact dropdown styling */
        .contact-option:hover {
            background-color: #eff6ff;
        }

        .contact-option:last-child {
            border-bottom: none;
        }

        /* Scrollbar styling for dropdown */
        #contact_dropdown::-webkit-scrollbar {
            width: 6px;
        }

        #contact_dropdown::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        #contact_dropdown::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        #contact_dropdown::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>
</head>
<body class="">

<?php include 'includes/navigation.php'; ?>

<!-- Main Content -->
<div class="ml-0 min-[870px]:ml-60 min-h-screen flex flex-col">
    <header class="bg-[#1E3E62] p-4 flex justify-between items-center z-10">
        <h1 class="text-2xl font-bold text-white mx-10 my-2">Contacts Management</h1>
        <div class="mx-10">
            <?php include 'includes/top_notification.php'; ?>
        </div>
    </header>

    <main class="flex-1 overflow-y-auto space-y-10">
        <!-- Statistics Cards -->
        <section class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-10 px-10" style="background: linear-gradient(to bottom, #1E3E62 60%, white 50%);">
            <?php if ($is_sales_rep): ?>
                <div class="bg-white p-4 rounded-2xl shadow-xl h-[150px]">
                    <img src="img/totalsales.png" alt="" class="w-10">
                    <p class="text-sm text-gray-500">My Assigned Contacts</p>
                    <h2 class="text-xl font-bold mt-2 text-color"><?php echo $stats['total_contacts']; ?></h2>
                </div>
                <div class="bg-white p-4 rounded-2xl shadow-xl h-[150px]">
                    <img src="img/revenue.png" alt="" class="w-10">
                    <p class="text-sm text-gray-500">My Total Sales</p>
                    <h2 class="text-xl font-bold mt-2 text-color"><?php echo $stats['total_sales']; ?></h2>
                </div>
                <div class="bg-white p-4 rounded-2xl shadow-xl h-[150px]">
                    <img src="img/referrals.png" alt="" class="w-10">
                    <p class="text-sm text-gray-500">Conversion Rate</p>
                    <h2 class="text-xl font-bold mt-2 text-color"><?php echo number_format($stats['conversion_rate'], 2); ?>%</h2>
                </div>
            <?php else: ?>
                <div class="bg-white p-4 rounded-2xl shadow-xl h-[150px]">
                    <img src="img/totalsales.png" alt="" class="w-10">
                    <p class="text-sm text-gray-500">Total Contacts</p>
                    <h2 class="text-xl font-bold mt-2 text-color"><?php echo $stats['total_contacts']; ?></h2>
                </div>
                <div class="bg-white p-4 rounded-2xl shadow-xl h-[150px]">
                    <img src="img/revenue.png" alt="" class="w-10">
                    <p class="text-sm text-gray-500">Assigned Contacts</p>
                    <h2 class="text-xl font-bold mt-2 text-color"><?php echo $stats['assigned_contacts']; ?></h2>
                </div>
                <div class="bg-white p-4 rounded-2xl shadow-xl h-[150px]">
                    <img src="img/referrals.png" alt="" class="w-10">
                    <p class="text-sm text-gray-500">Unassigned Contacts</p>
                    <h2 class="text-xl font-bold mt-2 text-color"><?php echo $stats['unassigned_contacts']; ?></h2>
                </div>
            <?php endif; ?>
        </section>

        <div class="px-10">
            <!-- Product interests are now stored in the customer_interest field -->

            <!-- Success/Error Messages -->
            <?php if (isset($success_message)): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                <i class="fas fa-check-circle mr-2"></i><?php echo $success_message; ?>
            </div>
            <?php endif; ?>

            <?php if (isset($error_message)): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                <i class="fas fa-exclamation-circle mr-2"></i><?php echo $error_message; ?>
            </div>
            <?php endif; ?>

        <!-- Filters and Actions -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                <div class="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
                    <!-- Search -->
                    <form method="GET" class="flex space-x-2">
                        <input type="text" name="search" value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="Search contacts..." 
                               class="px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        
                        <!-- Filter -->
                        <select name="filter" class="px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">All Contacts</option>
                            <optgroup label="">
                                <option value="assigned" <?php echo $filter === 'assigned' ? 'selected' : ''; ?>>Assigned</option>
                                <option value="unassigned" <?php echo $filter === 'unassigned' ? 'selected' : ''; ?>>Unassigned</option>
                                <option value="referral" <?php echo $filter === 'referral' ? 'selected' : ''; ?>>Referral</option>
                                <option value="direct" <?php echo $filter === 'direct' ? 'selected' : ''; ?>>Direct</option>
                            </optgroup>
                                                    </select>
                        
                        <button type="submit" class="px-4 py-2 bg-[#0b192c] text-white rounded-sm hover:bg-[#1e3e62]">
                            <i class="fas fa-search"></i> Filter
                        </button>

                        <?php if (!empty($search) || !empty($filter)): ?>
                        <a href="contacts.php" class="px-4 py-2 bg-[#0b192c] text-white rounded-sm hover:bg-[#1e3e62]">
                            <i class="fas fa-times"></i> Clear
                        </a>
                        <?php endif; ?>
                    </form>
                </div>
                
                <div class="flex space-x-2">
                    <?php if (isset($_SESSION['role']) && $_SESSION['role'] !== 'sales_rep'): ?>
                    <a href="create_contact.php" class="px-4 py-2 bg-[#0b192c] text-white rounded-sm hover:bg-[#1e3e62]">
                        <i class="fas fa-plus"></i> Add Contact
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Bulk Actions -->
        <div id="bulk-actions" class="bg-white rounded-lg shadow p-4 mb-6" style="display: none;">
            <form method="POST" id="bulk-form">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                    <div class="flex items-center space-x-4">
                        <span class="text-sm font-medium text-gray-700">
                            <span id="selected-count">0</span> contact(s) selected
                        </span>

                        <select name="bulk_action" id="bulk_action" class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">Choose Action</option>
                            <option value="assign">Assign To</option>
                            <option value="unassign">Unassign</option>
                            <option value="delete">Delete</option>
                        </select>

                        <select name="assign_to_user" id="assign_to_user" class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent" style="display: none;">
                            <option value="">Select User</option>
                            <?php
                            // Get users for assignment
                            $assignable_roles = "('sales_rep')";
                            if (isset($_SESSION['role']) && $_SESSION['role'] === 'manager') {
                                $assignable_roles = "('sales_rep')"; // Managers can only assign to sales reps
                            }
                            $users_query = "SELECT id, first_name, last_name FROM employees WHERE role IN $assignable_roles ORDER BY first_name, last_name";
                            $users_result = mysqli_query($conn, $users_query);
                            while ($user = mysqli_fetch_assoc($users_result)):
                            ?>
                            <option value="<?php echo $user['id']; ?>">
                                <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>
                            </option>
                            <?php endwhile; ?>
                        </select>
                    </div>

                    <div class="flex space-x-2">
                        <button type="submit" class="px-4 py-2 bg-[#0b192c] text-white rounded-sm hover:bg-[#1e3e62]">
                            <i class="fas fa-check"></i> Apply Action
                        </button>
                        <button type="button" onclick="clearSelection()" class="px-4 py-2 bg-[#0b192c] text-white rounded-sm hover:bg-[#1e3e62]">
                            <i class="fas fa-times"></i> Clear Selection
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Contacts Table -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" id="select-all" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">S.No</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Interest</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assigned To</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php
                        $serial_number = $offset + 1;
                        while ($row = mysqli_fetch_assoc($result)):
                        ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" name="selected_contacts[]" value="<?php echo $row['id']; ?>"
                                       class="contact-checkbox h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><?php echo $serial_number++; ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($row['full_name'] ?? 'N/A'); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php
                                if (!empty($row['product_interests'])) {
                                    $interests = array_map('trim', explode(',', $row['product_interests']));
                                    foreach ($interests as $index => $interest): ?>
                                        <div class="flex items-center py-1">
                                            <span class="bg-[#0b192c] text-white text-xs font-medium px-1.5 py-0.5 rounded-full mr-2 min-w-[20px] text-center">
                                                <?php echo $index + 1; ?>
                                            </span>
                                            <span class="text-sm text-gray-800">
                                                <?php echo htmlspecialchars($interest); ?>
                                            </span>
                                        </div>
                                    <?php endforeach;
                                } else {
                                    echo '<span class="text-gray-400">No interests</span>';
                                }
                                ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php
                                // Determine contact type based on priority: Referral > Assigned > Unassigned > Direct
                                $type = 'Direct';
                                $type_color = 'bg-blue-100 text-blue-800';

                                // Check if it's a referral (highest priority)
                                if ((!empty($row['is_referral']) && $row['is_referral']) ||
                                    !empty($row['referred_by_contact_id'])) {
                                    $type = 'Referral';
                                    $type_color = 'bg-purple-100 text-purple-800';
                                }
                                // Check assignment status
                                elseif (!empty($row['assigned_to'])) {
                                    $type = 'Assigned';
                                    $type_color = 'bg-green-100 text-green-800';
                                }
                                elseif (empty($row['assigned_to'])) {
                                    $type = 'Unassigned';
                                    $type_color = 'bg-gray-100 text-gray-800';
                                }
                                ?>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $type_color; ?>">
                                    <?php echo $type; ?>
                                </span>
                                <?php
                                // Show referrer information if available
                                if ($type === 'Referral'):
                                    if (!empty($row['referrer_name'])): ?>
                                    <div class="text-xs text-gray-500 mt-1">
                                        by <?php echo htmlspecialchars($row['referrer_name']); ?>
                                    </div>
                                    <?php elseif (!empty($row['referred_by_name'])): ?>
                                    <div class="text-xs text-gray-500 mt-1">
                                        by <?php echo htmlspecialchars($row['referred_by_name']); ?>
                                    </div>
                                    <?php endif;
                                endif; ?>
                            </td>
                                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo htmlspecialchars($row['assigned_to_name'] ?? 'Unassigned'); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo date('M j, Y', strtotime($row['created_at'])); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <a href="view_contact.php?id=<?php echo $row['id']; ?>" class="text-blue-600 hover:text-blue-900 mr-3" title="View">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <?php if (isset($_SESSION['role']) && $_SESSION['role'] !== 'sales_rep'): ?>
                                <a href="edit_contact.php?id=<?php echo $row['id']; ?>" class="text-green-600 hover:text-green-900 mr-3" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="delete_contact.php?id=<?php echo $row['id']; ?>" class="text-red-600 hover:text-red-900" title="Delete" 
                                   onclick="return confirm('Are you sure you want to delete this contact?')">
                                    <i class="fas fa-trash"></i>
                                </a>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div class="flex-1 flex justify-between sm:hidden">
                    <!-- Mobile pagination -->
                    <?php if ($page > 1): ?>
                    <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>"
                       class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Previous
                    </a>
                    <?php endif; ?>

                    <?php if ($page < $total_pages): ?>
                    <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>"
                       class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Next
                    </a>
                    <?php endif; ?>
                </div>

                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            Showing <span class="font-medium"><?php echo $offset + 1; ?></span> to
                            <span class="font-medium"><?php echo min($offset + $records_per_page, $total_records); ?></span> of
                            <span class="font-medium"><?php echo $total_records; ?></span> results
                        </p>
                    </div>

                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <!-- Previous Page Link -->
                            <?php if ($page > 1): ?>
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>"
                               class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                            <?php else: ?>
                            <span class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-300">
                                <i class="fas fa-chevron-left"></i>
                            </span>
                            <?php endif; ?>

                            <!-- Page Numbers -->
                            <?php
                            $start_page = max(1, $page - 2);
                            $end_page = min($total_pages, $page + 2);

                            // Show first page if not in range
                            if ($start_page > 1): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => 1])); ?>"
                                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">1</a>
                                <?php if ($start_page > 2): ?>
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>
                                <?php endif; ?>
                            <?php endif; ?>

                            <!-- Current page range -->
                            <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                                <?php if ($i == $page): ?>
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600"><?php echo $i; ?></span>
                                <?php else: ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"
                                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"><?php echo $i; ?></a>
                                <?php endif; ?>
                            <?php endfor; ?>

                            <!-- Show last page if not in range -->
                            <?php if ($end_page < $total_pages): ?>
                                <?php if ($end_page < $total_pages - 1): ?>
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>
                                <?php endif; ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $total_pages])); ?>"
                                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"><?php echo $total_pages; ?></a>
                            <?php endif; ?>

                            <!-- Next Page Link -->
                            <?php if ($page < $total_pages): ?>
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>"
                               class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                            <?php else: ?>
                            <span class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-300">
                                <i class="fas fa-chevron-right"></i>
                            </span>
                            <?php endif; ?>
                        </nav>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Nominees Section -->
        <div class="bg-white rounded-lg shadow mt-8 mb-20">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-xl font-semibold text-gray-900">
                        <i class="fas fa-users mr-2"></i>Nominees Management
                    </h2>
                    <button onclick="toggleNomineeForm()" class="px-4 py-2 bg-[#0b192c] text-white rounded-sm hover:bg-[#1e3e62]">
                        <i class="fas fa-plus"></i> Add Nominee
                    </button>
                </div>
            </div>

            <!-- Add Nominee Form -->
            <div id="nominee-form" class="p-6 border-b border-gray-200" style="display: none;">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Add New Nominee</h3>
                <form method="POST" class="space-y-4">
                    <input type="hidden" name="nominee_action" value="add">

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="contact_search" class="block text-sm font-medium text-gray-700 mb-1">Select Contact</label>
                            <div class="relative">
                                <input type="text"
                                       id="contact_search"
                                       placeholder="Type to search contacts..."
                                       autocomplete="off"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <input type="hidden" name="contact_id" id="contact_id" required>

                                <!-- Dropdown list -->
                                <div id="contact_dropdown" class="absolute z-50 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto hidden">
                                    <?php
                                    // Get contacts for dropdown (separate query)
                                    $contacts_dropdown_query = "SELECT id, first_name, last_name, CONCAT(first_name, ' ', last_name) as full_name, email, phone FROM contacts ORDER BY first_name, last_name";
                                    $contacts_dropdown_result = mysqli_query($conn, $contacts_dropdown_query);
                                    while ($contact = mysqli_fetch_assoc($contacts_dropdown_result)):
                                    ?>
                                    <div class="contact-option px-3 py-2 hover:bg-blue-50 cursor-pointer border-b border-gray-100"
                                         data-id="<?php echo $contact['id']; ?>"
                                         data-name="<?php echo htmlspecialchars($contact['full_name']); ?>"
                                         data-email="<?php echo htmlspecialchars($contact['email']); ?>"
                                         data-phone="<?php echo htmlspecialchars($contact['phone'] ?? ''); ?>">
                                        <div class="font-medium text-gray-900"><?php echo htmlspecialchars($contact['full_name']); ?></div>
                                        <div class="text-sm text-gray-500">
                                            <?php echo htmlspecialchars($contact['email']); ?>
                                            <?php if (!empty($contact['phone'])): ?>
                                                • <?php echo htmlspecialchars($contact['phone']); ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <?php endwhile; ?>
                                </div>
                            </div>
                        </div>

                        <div>
                            <label for="nominee_name" class="block text-sm font-medium text-gray-700 mb-1">Nominee Name</label>
                            <input type="text" name="nominee_name" id="nominee_name" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   placeholder="Enter nominee's full name">
                        </div>

                        <div>
                            <label for="relationship" class="block text-sm font-medium text-gray-700 mb-1">Relationship</label>
                            <select name="relationship" id="relationship" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="">Select relationship...</option>
                                <option value="Spouse">Spouse</option>
                                <option value="Son">Son</option>
                                <option value="Daughter">Daughter</option>
                                <option value="Father">Father</option>
                                <option value="Mother">Mother</option>
                                <option value="Brother">Brother</option>
                                <option value="Sister">Sister</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>

                        <div>
                            <label for="aadhar_id" class="block text-sm font-medium text-gray-700 mb-1">Aadhar ID</label>
                            <input type="text" name="aadhar_id" id="aadhar_id" required
                                   pattern="[0-9]{12}" maxlength="12"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   placeholder="Enter 12-digit Aadhar number">
                        </div>
                    </div>

                    <div class="flex space-x-2">
                        <button type="submit" class="px-4 py-2 bg-[#0b192c] text-white rounded-sm hover:bg-[#1e3e62]">
                            <i class="fas fa-save"></i> Add Nominee
                        </button>
                        <button type="button" onclick="toggleNomineeForm()" class="px-4 py-2 bg-[#0b192c] text-white rounded-sm hover:bg-[#1e3e62]">
                            <i class="fas fa-times"></i> Cancel
                        </button>
                    </div>
                </form>
            </div>

            <!-- Nominees Table -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">S.No</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nominee Name</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Relationship</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Aadhar ID</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Added On</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php
                        $serial_no = 1;
                        if (mysqli_num_rows($nominees_result) > 0):
                            while ($nominee = mysqli_fetch_assoc($nominees_result)):
                        ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo $serial_no++; ?></td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($nominee['name']); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900"><?php echo htmlspecialchars($nominee['contact_name'] ?? 'N/A'); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                    <?php echo htmlspecialchars($nominee['relationship']); ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo htmlspecialchars($nominee['aadhar_id']); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo date('M d, Y', strtotime($nominee['created_at'])); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this nominee?');">
                                    <input type="hidden" name="nominee_action" value="delete">
                                    <input type="hidden" name="nominee_id" value="<?php echo $nominee['id']; ?>">
                                    <button type="submit" class="text-red-600 hover:text-red-900" title="Delete Nominee">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </td>
                        </tr>
                        <?php
                            endwhile;
                        else:
                        ?>
                        <tr>
                            <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                <i class="fas fa-users text-4xl mb-2"></i>
                                <p>No nominees found. Add a nominee to get started.</p>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
        </div> <!-- Close px-10 wrapper -->
    </main>
</div>

<script>
// Bulk actions functionality
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('select-all');
    const contactCheckboxes = document.querySelectorAll('.contact-checkbox');
    const bulkActionsDiv = document.getElementById('bulk-actions');
    const selectedCountSpan = document.getElementById('selected-count');
    const bulkActionSelect = document.getElementById('bulk_action');
    const assignToUserSelect = document.getElementById('assign_to_user');
    const bulkForm = document.getElementById('bulk-form');

    // Select all functionality
    selectAllCheckbox.addEventListener('change', function() {
        contactCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActions();
    });

    // Individual checkbox functionality
    contactCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectAllState();
            updateBulkActions();
        });
    });

    // Show/hide assign to user dropdown
    bulkActionSelect.addEventListener('change', function() {
        if (this.value === 'assign') {
            assignToUserSelect.style.display = 'block';
            assignToUserSelect.required = true;
        } else {
            assignToUserSelect.style.display = 'none';
            assignToUserSelect.required = false;
            assignToUserSelect.value = ''; // Reset selection
        }
    });

    // Form submission with confirmation
    bulkForm.addEventListener('submit', function(e) {
        const selectedContacts = document.querySelectorAll('.contact-checkbox:checked');
        const action = bulkActionSelect.value;

        // Debug logging
        console.log('Form submission:', {
            selectedContacts: selectedContacts.length,
            action: action,
            assignToUser: assignToUserSelect.value
        });

        if (selectedContacts.length === 0) {
            e.preventDefault();
            alert('Please select at least one contact.');
            return;
        }

        if (!action) {
            e.preventDefault();
            alert('Please choose an action.');
            return;
        }

        // Remove any existing hidden inputs for selected contacts
        const existingInputs = bulkForm.querySelectorAll('input[name="selected_contacts[]"]');
        existingInputs.forEach(input => input.remove());

        // Add selected contact IDs as hidden inputs to the form
        selectedContacts.forEach(checkbox => {
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = 'selected_contacts[]';
            hiddenInput.value = checkbox.value;
            bulkForm.appendChild(hiddenInput);
        });

        if (action === 'delete') {
            if (!confirm(`Are you sure you want to delete ${selectedContacts.length} contact(s)? This action cannot be undone.`)) {
                e.preventDefault();
                return;
            }
        } else if (action === 'assign') {
            if (!assignToUserSelect.value || assignToUserSelect.value === '') {
                e.preventDefault();
                alert('Please select a user to assign contacts to.');
                assignToUserSelect.focus();
                return;
            }
            const selectedUserText = assignToUserSelect.options[assignToUserSelect.selectedIndex].text;
            if (!confirm(`Are you sure you want to assign ${selectedContacts.length} contact(s) to ${selectedUserText}?`)) {
                e.preventDefault();
                return;
            }
        } else if (action === 'unassign') {
            if (!confirm(`Are you sure you want to unassign ${selectedContacts.length} contact(s)?`)) {
                e.preventDefault();
                return;
            }
        }
    });

    function updateSelectAllState() {
        const checkedBoxes = document.querySelectorAll('.contact-checkbox:checked');
        const totalBoxes = contactCheckboxes.length;

        if (checkedBoxes.length === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (checkedBoxes.length === totalBoxes) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else {
            selectAllCheckbox.indeterminate = true;
            selectAllCheckbox.checked = false;
        }
    }

    function updateBulkActions() {
        const checkedBoxes = document.querySelectorAll('.contact-checkbox:checked');
        const count = checkedBoxes.length;

        selectedCountSpan.textContent = count;

        if (count > 0) {
            bulkActionsDiv.style.display = 'block';
        } else {
            bulkActionsDiv.style.display = 'none';
            bulkActionSelect.value = '';
            assignToUserSelect.style.display = 'none';
        }
    }
});

function clearSelection() {
    document.querySelectorAll('.contact-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    document.getElementById('select-all').checked = false;
    document.getElementById('select-all').indeterminate = false;
    document.getElementById('bulk-actions').style.display = 'none';
    document.getElementById('bulk_action').value = '';
    document.getElementById('assign_to_user').style.display = 'none';
}

// Nominee form toggle functionality
function toggleNomineeForm() {
    const form = document.getElementById('nominee-form');
    if (form.style.display === 'none' || form.style.display === '') {
        form.style.display = 'block';
        // Scroll to the form
        form.scrollIntoView({ behavior: 'smooth', block: 'start' });
    } else {
        form.style.display = 'none';
        // Clear form fields
        document.getElementById('contact_search').value = '';
        document.getElementById('contact_id').value = '';
        document.getElementById('nominee_name').value = '';
        document.getElementById('relationship').value = '';
        document.getElementById('aadhar_id').value = '';
        // Hide dropdown
        document.getElementById('contact_dropdown').classList.add('hidden');
    }
}

// Contact search functionality
document.addEventListener('DOMContentLoaded', function() {
    const contactSearch = document.getElementById('contact_search');
    const contactDropdown = document.getElementById('contact_dropdown');
    const contactIdInput = document.getElementById('contact_id');
    const contactOptions = document.querySelectorAll('.contact-option');

    if (contactSearch && contactDropdown) {
        // Show dropdown when input is focused
        contactSearch.addEventListener('focus', function() {
            contactDropdown.classList.remove('hidden');
            filterContacts(''); // Show all contacts initially
        });

        // Filter contacts as user types
        contactSearch.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            filterContacts(searchTerm);
            contactDropdown.classList.remove('hidden');

            // Clear hidden input if search is cleared
            if (searchTerm === '') {
                contactIdInput.value = '';
            }
        });

        // Handle contact selection
        contactOptions.forEach(option => {
            option.addEventListener('click', function() {
                const contactId = this.getAttribute('data-id');
                const contactName = this.getAttribute('data-name');
                const contactEmail = this.getAttribute('data-email');
                const contactPhone = this.getAttribute('data-phone');

                // Set the search input to show selected contact
                contactSearch.value = contactName + ' (' + contactEmail + ')';
                contactIdInput.value = contactId;

                // Hide dropdown
                contactDropdown.classList.add('hidden');
            });
        });

        // Hide dropdown when clicking outside
        document.addEventListener('click', function(event) {
            if (!contactSearch.contains(event.target) && !contactDropdown.contains(event.target)) {
                contactDropdown.classList.add('hidden');
            }
        });

        // Filter function
        function filterContacts(searchTerm) {
            contactOptions.forEach(option => {
                const name = option.getAttribute('data-name').toLowerCase();
                const email = option.getAttribute('data-email').toLowerCase();
                const phone = option.getAttribute('data-phone').toLowerCase();

                if (name.includes(searchTerm) || email.includes(searchTerm) || phone.includes(searchTerm)) {
                    option.style.display = 'block';
                } else {
                    option.style.display = 'none';
                }
            });
        }
    }

    // Aadhar ID validation
    const aadharInput = document.getElementById('aadhar_id');
    if (aadharInput) {
        aadharInput.addEventListener('input', function() {
            // Remove any non-digit characters
            this.value = this.value.replace(/\D/g, '');

            // Limit to 12 digits
            if (this.value.length > 12) {
                this.value = this.value.slice(0, 12);
            }
        });
    }
});
</script>

</body>
</html>
