<?php
// Get database connection using reliable method
$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';

// Check if database is set up first
require_once 'check_database_setup.php';
redirect_to_setup_if_needed();

// Check if the user is logged in
ensure_session_started();
require_login();

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    // Redirect back to sales page with error
    header("Location: sales.php?error=1&message=" . urlencode("No sale ID provided"));
    exit;
}

$sale_id = intval($_GET['id']);

// Process form submission for updating a sale
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['action']) && $_POST['action'] == 'update_sale') {
    $contact_id = sanitize_input($_POST['contact_id']);
    $product_id = sanitize_input($_POST['product_id']);
    $quantity = sanitize_input($_POST['quantity']);
    $sale_date = sanitize_input($_POST['sale_date']);
    $payment_method = sanitize_input($_POST['payment_method']);
    $notes = sanitize_input($_POST['notes']);

    // Calculate total amount based on quantity and product price
    $product_price = 0;
    $product_query = "SELECT price FROM products WHERE id = ?";
    $product_stmt = mysqli_prepare($conn, $product_query);
    mysqli_stmt_bind_param($product_stmt, "i", $product_id);
    mysqli_stmt_execute($product_stmt);
    $product_result = mysqli_stmt_get_result($product_stmt);
    if ($product_row = mysqli_fetch_assoc($product_result)) {
        $product_price = $product_row['price'];
    }
    $total_amount = $quantity * $product_price;

    $sql = "UPDATE sales SET
            contact_id = ?,
            product_id = ?,
            quantity = ?,
            total_amount = ?,
            sale_date = ?,
            payment_method = ?,
            notes = ?
            WHERE id = ?";

    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "iiidsssi", $contact_id, $product_id, $quantity, $total_amount, $sale_date, $payment_method, $notes, $sale_id);

    if (mysqli_stmt_execute($stmt)) {
        // Since we're using unified sales table, no separate invoice table update needed
        
        // Redirect to avoid form resubmission
        header("Location: sales.php?success=1&message=" . urlencode("Sale updated successfully"));
        exit;
    } else {
        $error_message = "Error: " . mysqli_error($conn);
    }
}

// Get the sale data
$sql = "SELECT * FROM sales WHERE id = ?";
$stmt = mysqli_prepare($conn, $sql);
mysqli_stmt_bind_param($stmt, "i", $sale_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) == 0) {
    // Sale doesn't exist
    header("Location: sales.php?error=1&message=" . urlencode("Sale not found"));
    exit;
}

$sale = mysqli_fetch_assoc($result);

// Get all contacts for dropdown
$customers_query = "SELECT id, CONCAT(first_name, ' ', last_name) as name FROM contacts ORDER BY first_name, last_name";
$customers_result = mysqli_query($conn, $customers_query);
$customers = [];
while ($row = mysqli_fetch_assoc($customers_result)) {
    $customers[] = $row;
}

// Get all products for dropdown
$products_query = "SELECT id, name, price FROM products ORDER BY name";
$products_result = mysqli_query($conn, $products_query);
$products = [];
while ($row = mysqli_fetch_assoc($products_result)) {
    $products[] = $row;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="img/minilogo.jpg" type="image/x-icon">
    <title>Edit Sale - CRM System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .text-color{ color: #FF6500; }
        .input-focus {
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        .input-focus:focus {
            border-color: #FF6500;
            box-shadow: 0 0 0 3px rgba(255, 101, 0, 0.1);
        }

        .button-hover {
            transition: background-color 0.3s ease, transform 0.2s ease;
        }

        .button-hover:hover {
            background-color: #e55b00;
            transform: scale(1.05);
        }
    </style>
</head>
<body class="">
<?php include 'includes/navigation.php'; ?>

    <!-- Main Content -->
    <div class="ml-0 min-[870px]:ml-60 min-h-screen flex flex-col">
        <!-- Navbar -->
        <header class="bg-[#1E3E62] p-4 flex justify-between z-10 flex-col">
            <h1 class="text-2xl font-bold text-white mx-10 my-2">Edit Sale</h1>
                    </header>
        <main class="flex-1 overflow-y-auto space-y-10 p-10">
            <!-- Error message -->
            <?php if (isset($error_message)) : ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                    <strong class="font-bold">Error!</strong>
                    <span class="block sm:inline"><?php echo $error_message; ?></span>
                    <button class="absolute top-0 bottom-0 right-0 px-4 py-3" onclick="this.parentElement.style.display='none'">
                        <svg class="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
                    </button>
                </div>
            <?php endif; ?>

            <!-- Sale Edit Form -->
            <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                <div class="px-6 py-5 border-b border-gray-200 sm:px-8" style="background: linear-gradient(to right, #1E3E62, #0B192C);">
                    <h3 class="text-lg leading-6 font-medium text-white">
                        Edit Sale Information
                    </h3>
                    <p class="mt-1 max-w-2xl text-sm text-white">
                        Update the sale's details
                    </p>
                </div>
                <div class="px-4 py-5 sm:p-6">
                    <form id="sale-form" class="space-y-4" method="POST" action="edit_sale.php?id=<?php echo $sale_id; ?>">
                        <input type="hidden" name="action" value="update_sale">
                        <div class="grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-2">
                            <div>
                                <label for="customer" class="block text-sm font-medium text-gray-700">Customer</label>
                                <?php
                                // Get the customer name for the current sale
                                $customer_name = "";
                                $contact_id = $sale['contact_id'];
                                $customer_query = "SELECT CONCAT(first_name, ' ', last_name) as full_name FROM contacts WHERE id = ?";
                                $customer_stmt = mysqli_prepare($conn, $customer_query);
                                mysqli_stmt_bind_param($customer_stmt, "i", $contact_id);
                                mysqli_stmt_execute($customer_stmt);
                                $customer_result = mysqli_stmt_get_result($customer_stmt);
                                if ($customer_row = mysqli_fetch_assoc($customer_result)) {
                                    $customer_name = $customer_row['full_name'];
                                }
                                ?>
                                <input type="text" id="customer_name" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-gray-100 rounded-md shadow-sm focus:outline-none sm:text-sm" value="<?php echo htmlspecialchars($customer_name); ?>" readonly>
                                <input type="hidden" name="contact_id" value="<?php echo $contact_id; ?>">
                            </div>
                            <div>
                                <label for="product" class="block text-sm font-medium text-gray-700">Product</label>
                                <?php
                                // Get the product name and price for the current sale
                                $product_name = "";
                                $product_price = 0;
                                $product_id = $sale['product_id'];
                                $product_query = "SELECT name, price FROM products WHERE id = ?";
                                $product_stmt = mysqli_prepare($conn, $product_query);
                                mysqli_stmt_bind_param($product_stmt, "i", $product_id);
                                mysqli_stmt_execute($product_stmt);
                                $product_result = mysqli_stmt_get_result($product_stmt);
                                if ($product_row = mysqli_fetch_assoc($product_result)) {
                                    $product_name = $product_row['name'];
                                    $product_price = $product_row['price'];
                                }
                                ?>
                                <input type="text" id="product_name" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-gray-100 rounded-md shadow-sm focus:outline-none sm:text-sm" value="<?php echo htmlspecialchars($product_name); ?> (<?php echo format_currency($product_price); ?>)" readonly>
                                <input type="hidden" name="product_id" value="<?php echo $product_id; ?>">
                            </div>
                            <div>
                                <label for="quantity" class="block text-sm font-medium text-gray-700">Quantity</label>
                                <input type="number" name="quantity" id="quantity" value="<?php echo htmlspecialchars($sale['quantity'] ?? 1); ?>" min="1" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md p-2 border input-focus" required>
                            </div>
                            <div>
                                <label for="total_amount" class="block text-sm font-medium text-gray-700">Total Amount</label>
                                <input type="text" id="total_amount_display" value="<?php echo format_currency($sale['total_amount']); ?>" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-gray-100 rounded-md shadow-sm focus:outline-none sm:text-sm" readonly>
                            </div>
                            <div>
                                <label for="sale-date" class="block text-sm font-medium text-gray-700">Sale Date</label>
                                <input type="date" name="sale_date" id="sale-date" value="<?php echo htmlspecialchars($sale['sale_date']); ?>" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md p-2 border input-focus" required>
                            </div>
                            <div>
                                <label for="payment-method" class="block text-sm font-medium text-gray-700">Payment Method</label>
                                <select id="payment-method" name="payment_method" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                                    <option <?php echo ($sale['payment_method'] == 'Cash') ? 'selected' : ''; ?>>Cash</option>
                                    <option <?php echo ($sale['payment_method'] == 'Credit Card') ? 'selected' : ''; ?>>Credit Card</option>
                                    <option <?php echo ($sale['payment_method'] == 'Debit Card') ? 'selected' : ''; ?>>Debit Card</option>
                                    <option <?php echo ($sale['payment_method'] == 'Bank Transfer') ? 'selected' : ''; ?>>Bank Transfer</option>
                                    <option <?php echo ($sale['payment_method'] == 'UPI') ? 'selected' : ''; ?>>UPI</option>
                                    <option <?php echo ($sale['payment_method'] == 'Other') ? 'selected' : ''; ?>>Other</option>
                                </select>
                            </div>
                            <div class="sm:col-span-2">
                                <label for="notes" class="block text-sm font-medium text-gray-700">Notes</label>
                                <textarea id="notes" name="notes" rows="3" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md p-2 border input-focus"><?php echo htmlspecialchars($sale['notes']); ?></textarea>
                            </div>
                        </div>
                        <div class="pt-5">
                            <div class="flex justify-end">
                                <a href="sales.php" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                    Cancel
                                </a>
                                <button type="submit" class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-[#FF6500] button-hover focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF6500]" title="Save Changes">
                                    <i class="fas fa-save"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </main>
    </div>

    <script>
    // Calculate total amount when quantity changes
    function calculateTotal() {
        const quantity = parseFloat(document.getElementById('quantity').value) || 0;
        const productPrice = <?php echo $product_price; ?>;
        const totalAmount = quantity * productPrice;

        // Display the exact price without rounding
        document.getElementById('total_amount_display').value = '₹' + totalAmount.toLocaleString('en-IN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }

    // Add event listener
    document.getElementById('quantity').addEventListener('input', calculateTotal);

    // Calculate initial total
    calculateTotal();
    </script>
</body>
</html>
