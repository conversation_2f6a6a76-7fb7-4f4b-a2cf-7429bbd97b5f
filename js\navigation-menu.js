// Navigation menu toggle script
document.addEventListener('DOMContentLoaded', function() {
    initMenu();
});

// Also initialize on window load to ensure all elements are loaded
window.addEventListener('load', function() {
    initMenu();
});

function initMenu() {
    console.log("Initializing menu...");
    
    // Get the toggle button and sidebar
    const toggleBtn = document.getElementById('menu-toggle');
    const sidebar = document.getElementById('sidebar');
    const closeButtons = document.querySelectorAll('.close-menu-btn');
    
    // Check if elements exist
    if (!toggleBtn || !sidebar) {
        console.error('Menu elements not found');
        return;
    }
    
    console.log("Toggle button:", toggleBtn);
    console.log("Sidebar:", sidebar);
    console.log("Close buttons:", closeButtons.length);
    
    // Toggle menu function
    function toggleMenu() {
        console.log("Toggling menu");
        
        if (sidebar.classList.contains('-translate-x-full')) {
            // Open the sidebar
            sidebar.classList.remove('-translate-x-full');
            // Hide the hamburger menu when sidebar is open
            toggleBtn.classList.add('hidden');
            console.log("Sidebar opened, hamburger hidden");
        } else {
            // Close the sidebar
            sidebar.classList.add('-translate-x-full');
            // Show the hamburger menu when sidebar is closed
            if (window.innerWidth < 870) {
                toggleBtn.classList.remove('hidden');
                console.log("Sidebar closed, hamburger shown");
            }
        }
    }
    
    // Function to open sidebar
    function openSidebar() {
        console.log("Opening sidebar");
        sidebar.classList.remove('-translate-x-full');
        toggleBtn.classList.add('hidden');
    }
    
    // Function to close sidebar
    function closeSidebar() {
        console.log("Closing sidebar");
        sidebar.classList.add('-translate-x-full');
        if (window.innerWidth < 870) {
            toggleBtn.classList.remove('hidden');
        }
    }
    
    // Add click event to toggle button
    toggleBtn.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        openSidebar();
    });
    
    // Add click events to all close buttons
    closeButtons.forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            closeSidebar();
        });
    });
    
    // Set initial state based on screen size
    function setInitialState() {
        console.log("Setting initial state, window width:", window.innerWidth);
        
        if (window.innerWidth >= 870) {
            // On larger screens
            sidebar.classList.remove('-translate-x-full');
            toggleBtn.classList.add('hidden'); // Always hide toggle on large screens
            console.log("Large screen: sidebar visible, toggle hidden");
        } else {
            // On smaller screens
            sidebar.classList.add('-translate-x-full');
            toggleBtn.classList.remove('hidden'); // Show toggle when sidebar is closed
            console.log("Small screen: sidebar hidden, toggle visible");
        }
    }
    
    // Set initial state
    setInitialState();
    
    // Update on window resize
    window.addEventListener('resize', setInitialState);
    
    // Check if sidebar is already open on small screens
    if (window.innerWidth < 870 && !sidebar.classList.contains('-translate-x-full')) {
        // If sidebar is open on small screen, hide the toggle button
        toggleBtn.classList.add('hidden');
        console.log("Initial check: Sidebar is open on small screen, hiding toggle");
    }
}
