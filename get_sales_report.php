<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include database configuration
$conn = require_once 'config/database.php';
require_once 'includes/functions.php';

// Log request parameters
$log_file = fopen("report_debug.log", "a");
fwrite($log_file, "Report request at " . date('Y-m-d H:i:s') . "\n");
fwrite($log_file, "GET params: " . print_r($_GET, true) . "\n");

// Ensure currency is formatted in rupees
function format_report_currency($amount) {
    return '₹' . number_format($amount, 2);
}

// Ensure user is logged in
ensure_session_started();
require_login();

// Set content type to JSON
header('Content-Type: application/json');

// Get parameters
$from_date = isset($_GET['from_date']) ? $_GET['from_date'] : date('Y-m-d', strtotime('-30 days'));
$to_date = isset($_GET['to_date']) ? $_GET['to_date'] : date('Y-m-d');
$report_type = isset($_GET['type']) ? $_GET['type'] : 'monthly';

// Debug date parameters
fwrite($log_file, "Original date parameters: from_date=" . $from_date . ", to_date=" . $to_date . "\n");

// Ensure dates are in the correct format (YYYY-MM-DD)
if ($from_date) {
    $from_date_obj = new DateTime($from_date);
    $from_date = $from_date_obj->format('Y-m-d');
}

if ($to_date) {
    $to_date_obj = new DateTime($to_date);
    $to_date = $to_date_obj->format('Y-m-d');
}

// Debug formatted dates
fwrite($log_file, "Formatted date parameters: from_date=" . $from_date . ", to_date=" . $to_date . "\n");

// Removed the hardcoded date range override to allow dynamic date selection
fwrite($log_file, "Using user-selected date range: " . $from_date . " to " . $to_date . "\n");

// Check if sales table exists
$check_table_query = "SHOW TABLES LIKE 'sales'";
$check_table_result = mysqli_query($conn, $check_table_query);
if (mysqli_num_rows($check_table_result) == 0) {
    fwrite($log_file, "Error: Sales table does not exist\n");
    echo json_encode([
        'success' => false,
        'message' => 'Sales table does not exist'
    ]);
    exit;
}

// Check if there's any data in the sales table
$check_data_query = "SELECT COUNT(*) as count FROM sales";
$check_data_result = mysqli_query($conn, $check_data_query);
$check_data_row = mysqli_fetch_assoc($check_data_result);
fwrite($log_file, "Sales table record count: " . $check_data_row['count'] . "\n");

// Check if there's any data in the date range
$check_range_query = "SELECT COUNT(*) as count FROM sales WHERE DATE(sale_date) BETWEEN ? AND ?";
$check_range_stmt = mysqli_prepare($conn, $check_range_query);
mysqli_stmt_bind_param($check_range_stmt, 'ss', $from_date, $to_date);
mysqli_stmt_execute($check_range_stmt);
$check_range_result = mysqli_stmt_get_result($check_range_stmt);
$check_range_row = mysqli_fetch_assoc($check_range_result);
fwrite($log_file, "Sales in date range (" . $from_date . " to " . $to_date . "): " . $check_range_row['count'] . "\n");

// Get all sales data for debugging
$all_sales_query = "SELECT id, contact_id, product_id, total_amount, sale_date FROM sales";
$all_sales_result = mysqli_query($conn, $all_sales_query);
fwrite($log_file, "All sales data:\n");
while ($row = mysqli_fetch_assoc($all_sales_result)) {
    fwrite($log_file, "ID: " . $row['id'] . ", Date: " . $row['sale_date'] . ", Amount: " . $row['total_amount'] . "\n");
}
$check_range_stmt = mysqli_prepare($conn, $check_range_query);
mysqli_stmt_bind_param($check_range_stmt, 'ss', $from_date, $to_date);
mysqli_stmt_execute($check_range_stmt);
$check_range_result = mysqli_stmt_get_result($check_range_stmt);
$check_range_row = mysqli_fetch_assoc($check_range_result);
fwrite($log_file, "Sales in date range (" . $from_date . " to " . $to_date . "): " . $check_range_row['count'] . "\n");

// Validate dates
if (!$from_date || !$to_date) {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid date range'
    ]);
    exit;
}

// Initialize response
$response = [
    'success' => true,
    'data' => [],
    'labels' => []
];

// Add a direct query to check for any sales data regardless of date
$direct_query = "SELECT id, contact_id, product_id, total_amount, sale_date FROM sales LIMIT 10";
$direct_result = mysqli_query($conn, $direct_query);
$direct_data = [];
while ($row = mysqli_fetch_assoc($direct_result)) {
    $direct_data[] = $row;
}
$response['debug_data'] = $direct_data;
fwrite($log_file, "Direct query results: " . json_encode($direct_data) . "\n");

// Debug: Log that we're using the overridden date range
fwrite($log_file, "Using overridden date range: " . $from_date . " to " . $to_date . "\n");

// Generate report based on type
switch ($report_type) {
    case 'monthly':
        // Monthly sales report
        // Try a simpler query first to see if we can get any data
        $query = "SELECT
                    sale_date,
                    total_amount
                  FROM sales
                  ORDER BY sale_date ASC";
        
        $stmt = mysqli_prepare($conn, $query);
        
        if (!$stmt) {
            fwrite($log_file, "MySQL prepare error: " . mysqli_error($conn) . "\n");
            $response['success'] = false;
            $response['message'] = "Database error: " . mysqli_error($conn);
            echo json_encode($response);
            exit;
        }
        
        if (!mysqli_stmt_execute($stmt)) {
            fwrite($log_file, "MySQL execute error: " . mysqli_stmt_error($stmt) . "\n");
            $response['success'] = false;
            $response['message'] = "Database error: " . mysqli_stmt_error($stmt);
            echo json_encode($response);
            exit;
        }
        
        $result = mysqli_stmt_get_result($stmt);
        $all_sales = [];
        
        while ($row = mysqli_fetch_assoc($result)) {
            $all_sales[] = $row;
        }
        
        $response['all_sales'] = $all_sales;
        fwrite($log_file, "All sales: " . json_encode($all_sales) . "\n");
        
        // Now try the original query
        $query = "SELECT
                    DATE_FORMAT(sale_date, '%Y-%m') as month,
                    COUNT(*) as count,
                    SUM(total_amount) as total
                  FROM sales
                  WHERE DATE(sale_date) BETWEEN ? AND ?
                  GROUP BY DATE_FORMAT(sale_date, '%Y-%m')
                  ORDER BY month ASC";
        
        fwrite($log_file, "Monthly query: " . $query . "\n");
        fwrite($log_file, "Params: from_date=" . $from_date . ", to_date=" . $to_date . "\n");
        
        $stmt = mysqli_prepare($conn, $query);
        if (!$stmt) {
            fwrite($log_file, "MySQL prepare error: " . mysqli_error($conn) . "\n");
            $response['success'] = false;
            $response['message'] = "Database error: " . mysqli_error($conn);
            echo json_encode($response);
            exit;
        }
        
        mysqli_stmt_bind_param($stmt, 'ss', $from_date, $to_date);
        
        if (!mysqli_stmt_execute($stmt)) {
            fwrite($log_file, "MySQL execute error: " . mysqli_stmt_error($stmt) . "\n");
            $response['success'] = false;
            $response['message'] = "Database error: " . mysqli_stmt_error($stmt);
            echo json_encode($response);
            exit;
        }
        
        $result = mysqli_stmt_get_result($stmt);
        
        while ($row = mysqli_fetch_assoc($result)) {
            $month_name = date('M Y', strtotime($row['month'] . '-01'));
            $response['labels'][] = $month_name;
            $response['data'][] = floatval($row['total']);
        }
        break;
        
    case 'product':
        // Product sales report
        $query = "SELECT
                    p.name as product_name,
                    COUNT(s.id) as count,
                    SUM(s.total_amount) as total
                  FROM sales s
                  JOIN products p ON s.product_id = p.id
                  WHERE DATE(s.sale_date) BETWEEN ? AND ?
                  GROUP BY p.id
                  ORDER BY total DESC
                  LIMIT 5";
        
        $stmt = mysqli_prepare($conn, $query);
        mysqli_stmt_bind_param($stmt, 'ss', $from_date, $to_date);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        
        while ($row = mysqli_fetch_assoc($result)) {
            $response['labels'][] = $row['product_name'];
            $response['data'][] = floatval($row['total']);
        }
        break;
        
    case 'summary':
        // Sales summary
        $total_query = "SELECT
                          COUNT(*) as total_count,
                          SUM(total_amount) as total_revenue,
                          AVG(total_amount) as average_sale
                        FROM sales
                        WHERE DATE(sale_date) BETWEEN ? AND ?";

        $stmt = mysqli_prepare($conn, $total_query);
        mysqli_stmt_bind_param($stmt, 'ss', $from_date, $to_date);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $summary = mysqli_fetch_assoc($result);

        // Debug: Log the summary data
        fwrite($log_file, "Summary query result: " . json_encode($summary) . "\n");
        fwrite($log_file, "Date range: $from_date to $to_date\n");

        // Also check if there are any sales at all (without date filter)
        $all_sales_query = "SELECT COUNT(*) as total_count, SUM(total_amount) as total_revenue FROM sales";
        $all_sales_result = mysqli_query($conn, $all_sales_query);
        $all_sales_data = mysqli_fetch_assoc($all_sales_result);
        fwrite($log_file, "All sales (no date filter): " . json_encode($all_sales_data) . "\n");
        
        // Get conversion rate (contacts who made purchases)
        $conversion_query = "SELECT
                              COUNT(DISTINCT c.id) as total_contacts,
                              COUNT(DISTINCT s.contact_id) as converted_contacts
                            FROM contacts c
                            LEFT JOIN sales s ON c.id = s.contact_id
                            WHERE DATE(c.created_at) BETWEEN ? AND ?";

        $stmt = mysqli_prepare($conn, $conversion_query);
        mysqli_stmt_bind_param($stmt, 'ss', $from_date, $to_date);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        $conversion = mysqli_fetch_assoc($result);

        $conversion_rate = 0;
        if ($conversion['total_contacts'] > 0) {
            $conversion_rate = ($conversion['converted_contacts'] / $conversion['total_contacts']) * 100;
        }

        // Debug: Log the conversion data
        fwrite($log_file, "Conversion query result: " . json_encode($conversion) . "\n");
        fwrite($log_file, "Calculated conversion rate: $conversion_rate\n");

        // Ensure we have valid data
        $total_count = isset($summary['total_count']) ? intval($summary['total_count']) : 0;
        $total_revenue = isset($summary['total_revenue']) && $summary['total_revenue'] !== null ? floatval($summary['total_revenue']) : 0;
        $average_sale = isset($summary['average_sale']) && $summary['average_sale'] !== null ? floatval($summary['average_sale']) : 0;

        $response['summary'] = [
            'total_sales' => $total_count,
            'total_revenue' => $total_revenue,
            'average_sale' => $average_sale,
            'conversion_rate' => round($conversion_rate, 1)
        ];
        break;
        
    case 'customers':
        // Top customers
        $query = "SELECT
                    c.full_name as customer_name,
                    COUNT(s.id) as sales_count,
                    SUM(s.total_amount) as total_revenue
                  FROM sales s
                  JOIN contacts c ON s.contact_id = c.id
                  WHERE DATE(s.sale_date) BETWEEN ? AND ?
                  GROUP BY s.contact_id
                  ORDER BY total_revenue DESC
                  LIMIT 5";
        
        $stmt = mysqli_prepare($conn, $query);
        mysqli_stmt_bind_param($stmt, 'ss', $from_date, $to_date);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        
        $customers = [];
        while ($row = mysqli_fetch_assoc($result)) {
            // Get the top product for this customer with more details
            $top_product_query = "SELECT
                                    p.name as product_name,
                                    COUNT(s.id) as purchase_count,
                                    SUM(s.total_amount) as product_revenue,
                                    MAX(s.sale_date) as last_purchase_date,
                                    p.category as product_category
                                  FROM sales s
                                  JOIN products p ON s.product_id = p.id
                                  JOIN contacts c ON s.contact_id = c.id
                                  WHERE s.contact_id = (SELECT id FROM contacts WHERE full_name = ?)
                                    AND DATE(s.sale_date) BETWEEN ? AND ?
                                  GROUP BY s.product_id
                                  ORDER BY product_revenue DESC
                                  LIMIT 1";
            
            $product_stmt = mysqli_prepare($conn, $top_product_query);
            mysqli_stmt_bind_param($product_stmt, 'sss', $row['customer_name'], $from_date, $to_date);
            mysqli_stmt_execute($product_stmt);
            $product_result = mysqli_stmt_get_result($product_stmt);
            $top_product = mysqli_fetch_assoc($product_result);
            
            // Get total number of unique products purchased
            $unique_products_query = "SELECT
                                        COUNT(DISTINCT s.product_id) as unique_products
                                      FROM sales s
                                      WHERE s.contact_id = (SELECT id FROM contacts WHERE full_name = ?)
                                        AND DATE(s.sale_date) BETWEEN ? AND ?";

            $unique_stmt = mysqli_prepare($conn, $unique_products_query);
            mysqli_stmt_bind_param($unique_stmt, 'sss', $row['customer_name'], $from_date, $to_date);
            mysqli_stmt_execute($unique_stmt);
            $unique_result = mysqli_stmt_get_result($unique_stmt);
            $unique_products = mysqli_fetch_assoc($unique_result);

            // Get customer contact information
            $customer_info_query = "SELECT
                                      email,
                                      phone
                                    FROM contacts
                                    WHERE full_name = ?";
            
            $info_stmt = mysqli_prepare($conn, $customer_info_query);
            mysqli_stmt_bind_param($info_stmt, 's', $row['customer_name']);
            mysqli_stmt_execute($info_stmt);
            $info_result = mysqli_stmt_get_result($info_stmt);
            $customer_info = mysqli_fetch_assoc($info_result);
            
            $customers[] = [
                'name' => $row['customer_name'],
                'sales' => intval($row['sales_count']),
                'revenue' => floatval($row['total_revenue']),
                'top_product' => $top_product ? $top_product['product_name'] : 'N/A',
                'top_product_count' => $top_product ? intval($top_product['purchase_count']) : 0,
                'top_product_revenue' => $top_product ? floatval($top_product['product_revenue']) : 0,
                'top_product_category' => $top_product ? $top_product['product_category'] : 'N/A',
                'last_purchase_date' => $top_product ? $top_product['last_purchase_date'] : 'N/A',
                'unique_products' => $unique_products ? intval($unique_products['unique_products']) : 0,
                'email' => $customer_info ? $customer_info['email'] : 'N/A',
                'phone' => $customer_info ? $customer_info['phone'] : 'N/A'
            ];
        }
        
        $response['customers'] = $customers;
        break;
        
    case 'executives':
        // Top sales representatives
        $query = "SELECT
                    CONCAT(u.first_name, ' ', u.last_name) as rep_name,
                    COUNT(s.id) as sales_count,
                    SUM(s.total_amount) as total_revenue
                  FROM sales s
                  JOIN employees u ON s.sales_rep_id = u.id
                  WHERE DATE(s.sale_date) BETWEEN ? AND ? AND s.sales_rep_id IS NOT NULL
                  GROUP BY s.sales_rep_id
                  ORDER BY total_revenue DESC
                  LIMIT 5";
        
        $stmt = mysqli_prepare($conn, $query);
        mysqli_stmt_bind_param($stmt, 'ss', $from_date, $to_date);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        
        $executives = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $executives[] = [
                'name' => $row['rep_name'],
                'sales' => intval($row['sales_count']),
                'revenue' => floatval($row['total_revenue'])
            ];
        }
        
        $response['executives'] = $executives;
        break;
        
    default:
        $response['success'] = false;
        $response['message'] = 'Invalid report type';
}

// Log the response
fwrite($log_file, "Response: " . json_encode($response) . "\n");
fwrite($log_file, "----------------------------------------\n");
fclose($log_file);

// Return the JSON response
echo json_encode($response);
?>
