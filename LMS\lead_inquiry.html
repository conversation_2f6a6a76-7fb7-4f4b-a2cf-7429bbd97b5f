<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>LMS - Lead Inquiry</title>
    <link rel="icon" type="image/png" href="/images/logo.title.png">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet" />
    <link
        href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Open+Sans:wght@400;500;600&display=swap"
        rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" />
    <link href="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.css" rel="stylesheet">
    <style>
        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        .animate-fadeInUp { animation: fadeInUp 0.6s ease-out forwards; }
        .animate-float { animation: float 6s ease-in-out infinite; }
        .glass-effect {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .card-hover { transition: all 0.3s ease; }
        .card-hover:hover { transform: translateY(-10px); box-shadow: 0 25px 50px -12px rgba(0,0,0,0.25); }

           /* Hamburger to X animation for mobile menu button */
           #mobileMenuBtn.open span:nth-child(1) {
          transform: rotate(45deg) translateY(8px);
        }
        #mobileMenuBtn.open span:nth-child(2) {
          opacity: 0;
        }
        #mobileMenuBtn.open span:nth-child(3) {
          transform: rotate(-45deg) translateY(-8px);
        }
    </style>
</head>

<body class="overflow-x-hidden bg-gray-50 text-gray-800">
 
  <header class="fixed w-full top-0 z-50">
    <div class="lg:max-w-full mx-auto px-4 sm:px-4 md:px-6 lg:px-8 py-2 sm:py-3 md:py-4">
      <div
        class=" bg-white backdrop-blur-lg rounded-lg sm:rounded-xl lg:rounded-2xl shadow-lg border border-white/20 mx-1 sm:mx-2 md:mx-3 lg:mx-4">
        <div class=" flex justify-between items-center h-12 sm:h-14 md:h-16 px-2 sm:px-4 md:px-6">

          <div>
            <h1
              class="text-lg sm:text-xl md:text-2xl bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent relative">
              <img src="images/logo.png" alt="LMS Logo" class="lg:w-32  w-20 object-contain">
            </h1>
          </div>

          <!-- Desktop Nav -->
          <nav class="hidden lg:flex justify-between items-center">
            <!-- logo -->

            
            <div class="flex  ">
              <a href="index.html"
                class="flex nav-link px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-all duration-300">
                Home
              </a>
              <a href="About.html"
                class="flex px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-all duration-300">
                About
              </a>
              <a href="products.html"
                class=" flex px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-all duration-300">
                Products
              </a>

              <!-- Features Dropdown -->
              <div class="relative">
                <button id="featuresDropdownBtn"
                  class="px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-all duration-300 flex items-center">
                  Features
                  <i class="ri-arrow-down-s-line ml-1 transition-transform duration-200"></i>
                </button>
                <div id="featuresDropdownMenu" class="absolute top-full left-0 mt-2 w-64 bg-white rounded-xl shadow-2xl border border-gray-100 py-2 z-50 hidden">
                  <a href="leadmanagement.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-brain-line text-blue-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Lead Management</div>
                      <div class="text-xs text-gray-500">Smart lead prioritization</div>
                    </div>
                  </a>
                  <a href="salemanagement.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-settings-3-line text-green-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Sales Management</div>
                      <div class="text-xs text-gray-500">Streamline your process</div>
                    </div>
                  </a>
                  <a href="analytics.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-pie-chart-line text-purple-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Advanced Analytics</div>
                      <div class="text-xs text-gray-500">Detailed insights & reports</div>
                    </div>
                  </a>
                  <a href="taskmanager.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-smartphone-line text-orange-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Task Manager</div>
                      <div class="text-xs text-gray-500">Capture from anywhere</div>
                    </div>
                  </a>
                  <a href="customersupport.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-team-line text-indigo-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Customer Support</div>
                      <div class="text-xs text-gray-500">Work together effectively</div>
                    </div>
                  </a>
                  <a href="integration.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-teal-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-shield-check-line text-teal-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Integration</div>
                      <div class="text-xs text-gray-500">Connect seamlessly with tools</div>
                    </div>
                  </a>
                </div>
              </div>

            </div>
            <!-- login -->
            <div class="flex ">
              <a href="contact.html"
                class=" flex items-center font-bold px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black  transition-all duration-300">
                Contact
              </a>

              <a href="/LEADManagement/auth/login.php"
                class=" flex items-center bg-orange-500 rounded-3xl font-bold px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-white  transition-all duration-300">
                Login
              </a>
            </div>

          </nav>

          <!-- Mobile Menu Button -->
          <div class="lg:hidden z-50">
            <button id="mobileMenuBtn" class="focus:outline-none relative w-6 sm:w-7 md:w-8 h-6 sm:h-7 md:h-8">
              <span class="absolute top-1 left-0 w-6 sm:w-7 md:w-8 h-0.5 bg-black transition-transform duration-300"></span>
              <span class="absolute top-3 left-0 w-6 sm:w-7 md:w-8 h-0.5 bg-black transition-opacity duration-300"></span>
              <span class="absolute top-5 left-0 w-6 sm:w-7 md:w-8 h-0.5 bg-black transition-transform duration-300"></span>
            </button>
          </div>
        </div>

        <!-- Mobile Nav Menu -->
        <div id="mobileMenu" class="lg:hidden px-3 sm:px-4 md:px-6 pb-4 sm:pb-5 md:pb-6 pt-2 hidden">
          <div class="flex flex-col space-y-2 sm:space-y-3 md:space-y-4">
            <a href="index.html"
              class="text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200">
              Home
            </a>
            <a href="About.html"
              class="text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200">
              About
            </a>
            <a href="products.html"
              class="text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200">
              Products
            </a>
            <a href="contact.html"
              class="text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200">
              Contact
            </a>

            <!-- Mobile Features Section -->
            <div>
              <button id="mobileFeaturesBtn"
                class="w-full text-left text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200 flex items-center justify-between">
                <span>Features</span>
                <i class="ri-arrow-down-s-line transition-transform duration-200"></i>
              </button>

              <div id="mobileFeaturesMenu" class="ml-4 mt-2 space-y-2 hidden">
                <a href="leadmanagement.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-brain-line mr-2"></i>Lead Management
                </a>
                <a href="salemanagement.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-settings-3-line mr-2"></i>Email
                  Marketing
                </a>
                <a href="analytics.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-pie-chart-line mr-2"></i>Advanced Analytics
                </a>
                <a href="taskmanager.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-smartphone-line mr-2"></i>Task Manager
                </a>
                <a href="customersupport.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-team-line mr-2"></i>Customer Support
                </a>
                <a href="integration.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-shield-check-line mr-2"></i>Integration
                </a>
              </div>
            </div>

            <a href="/LEADManagement/auth/login.php"
              class="bg-[#FF6500] text-white px-4 sm:px-5 md:px-6 py-1.5 sm:py-2 md:py-2.5 text-sm sm:text-base md:text-lg rounded-full font-bold text-center mt-2 hover:bg-[#e55a00] transition-colors duration-200">
              Login
            </a>
          </div>
        </div>
      </div>
    </div>
  </header>

  <!-- Hero Section -->
  <section class="px-4 sm:px-6 md:px-10 py-16 sm:py-20 mt-16 relative overflow-hidden bg-gradient-to-br from-orange-50 via-blue-50 to-white">
    <div class="text-center w-auto mx-auto space-y-10">
      <div class="animate-fadeInUp">
        <h1 class="text-5xl sm:text-6xl md:text-7xl font-extrabold font-serif bg-gradient-to-r from-orange-500 to-blue-600 bg-clip-text text-transparent mb-4">Submit Your Inquiry</h1>
        <p class="text-lg sm:text-2xl md:text-3xl text-gray-700 font-medium mt-6 px-2 max-w-3xl mx-auto">Interested in our products or services? Fill out our lead form and we'll get back to you quickly.</p>
      </div>
    </div>
  </section>

  <!-- Lead Form Section -->
  <section class="py-16 px-4 sm:px-6 md:px-10 bg-white">
    <div class="max-w-4xl mx-auto">
      <div class="text-center mb-12">
        <h2 class="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">Tell Us About Your Needs</h2>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">We'll connect you with the right solutions and team members to help you succeed.</p>
      </div>
      
      <div class="bg-white rounded-2xl shadow-xl p-8 border border-gray-100">
        <div class="mb-6 text-center">
          <a href="/LEADManagement/lead_form.php" class="inline-block bg-[#FF6500] text-white font-bold py-4 px-10 rounded-lg hover:bg-[#e55a00] transition-colors duration-300 text-xl">
            Go to Lead Form
          </a>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
          <div class="text-center">
            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <i class="ri-user-follow-line text-2xl text-blue-600"></i>
            </div>
            <h3 class="text-xl font-bold mb-2">Personal Attention</h3>
            <p class="text-gray-600">Get personalized service from our dedicated team of experts.</p>
          </div>
          
          <div class="text-center">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <i class="ri-time-line text-2xl text-green-600"></i>
            </div>
            <h3 class="text-xl font-bold mb-2">Quick Response</h3>
            <p class="text-gray-600">We respond to all inquiries within 24 hours or less.</p>
          </div>
          
          <div class="text-center">
            <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <i class="ri-shield-check-line text-2xl text-purple-600"></i>
            </div>
            <h3 class="text-xl font-bold mb-2">Secure Process</h3>
            <p class="text-gray-600">Your information is kept confidential and secure.</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-[#1E3E62] text-white py-12 px-4 sm:px-6 md:px-10">
    <div class="max-w-6xl mx-auto">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <div>
          <img src="images/logo.png" alt="LMS Logo" class="h-12 mb-4">
          <p class="text-gray-300 text-sm">Empowering businesses with intelligent lead management solutions since 2020.</p>
        </div>
        
        <div>
          <h3 class="text-lg font-bold mb-4">Quick Links</h3>
          <ul class="space-y-2">
            <li><a href="index.html" class="text-gray-300 hover:text-white transition-colors">Home</a></li>
            <li><a href="About.html" class="text-gray-300 hover:text-white transition-colors">About</a></li>
            <li><a href="products.html" class="text-gray-300 hover:text-white transition-colors">Products</a></li>
            <li><a href="contact.html" class="text-gray-300 hover:text-white transition-colors">Contact</a></li>
          </ul>
        </div>
        
        <div>
          <h3 class="text-lg font-bold mb-4">Features</h3>
          <ul class="space-y-2">
            <li><a href="leadmanagement.html" class="text-gray-300 hover:text-white transition-colors">Lead Management</a></li>
            <li><a href="salemanagement.html" class="text-gray-300 hover:text-white transition-colors">Sales Management</a></li>
            <li><a href="analytics.html" class="text-gray-300 hover:text-white transition-colors">Analytics</a></li>
            <li><a href="integration.html" class="text-gray-300 hover:text-white transition-colors">Integration</a></li>
          </ul>
        </div>
        
        <div>
          <h3 class="text-lg font-bold mb-4">Contact Us</h3>
          <ul class="space-y-2">
            <li class="flex items-center"><i class="ri-map-pin-line mr-2"></i> 123 Business Ave, Suite 100, City, Country</li>
            <li class="flex items-center"><i class="ri-mail-line mr-2"></i> <EMAIL></li>
            <li class="flex items-center"><i class="ri-phone-line mr-2"></i> +****************</li>
          </ul>
        </div>
      </div>
      
      <div class="border-t border-gray-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
        <p class="text-gray-400 text-sm">&copy; 2023 Lead Management System. All rights reserved.</p>
        <div class="flex space-x-4 mt-4 md:mt-0">
          <a href="#" class="text-gray-400 hover:text-white transition-colors"><i class="ri-facebook-fill text-xl"></i></a>
          <a href="#" class="text-gray-400 hover:text-white transition-colors"><i class="ri-twitter-fill text-xl"></i></a>
          <a href="#" class="text-gray-400 hover:text-white transition-colors"><i class="ri-linkedin-fill text-xl"></i></a>
          <a href="#" class="text-gray-400 hover:text-white transition-colors"><i class="ri-instagram-fill text-xl"></i></a>
        </div>
      </div>
    </div>
  </footer>

  <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
  <script>
    // Initialize AOS
    AOS.init({
      duration: 800,
      easing: 'ease-in-out',
      once: true
    });

    // Desktop Features Dropdown
    const featuresBtn = document.getElementById('featuresDropdownBtn');
    const featuresMenu = document.getElementById('featuresDropdownMenu');

    featuresBtn.addEventListener('click', function(e) {
      e.preventDefault();
      featuresMenu.classList.toggle('hidden');
      featuresBtn.querySelector('i').classList.toggle('rotate-180');
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
      if (!featuresBtn.contains(e.target) && !featuresMenu.contains(e.target)) {
        featuresMenu.classList.add('hidden');
        featuresBtn.querySelector('i').classList.remove('rotate-180');
      }
    });

    // Mobile Menu Toggle
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    const mobileMenu = document.getElementById('mobileMenu');

    mobileMenuBtn.addEventListener('click', function() {
      mobileMenu.classList.toggle('hidden');
      mobileMenuBtn.classList.toggle('open');
    });

    // Mobile Features Toggle
    const mobileFeaturesBtn = document.getElementById('mobileFeaturesBtn');
    const mobileFeaturesMenu = document.getElementById('mobileFeaturesMenu');

    mobileFeaturesBtn.addEventListener('click', function() {
      mobileFeaturesMenu.classList.toggle('hidden');
      mobileFeaturesBtn.querySelector('i').classList.toggle('rotate-180');
    });
  </script>
</body>

</html>