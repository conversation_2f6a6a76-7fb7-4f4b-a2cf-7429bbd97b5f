<?php
// Include database configuration
$conn = require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if the user is logged in
ensure_session_started();
require_login();

// Check if user is a sales rep
if (!has_role("sales_rep")) {
    header("Location: admin.php");
    exit;
}

// Get the sales rep ID
$sales_rep_id = $_SESSION['user_id'];

// Get assigned contacts (leads and customers)
$assigned_contacts_query = "SELECT id, first_name, last_name, email, phone, customer_interest, created_at FROM contacts WHERE assigned_to = ? ORDER BY created_at DESC";
$assigned_contacts_stmt = mysqli_prepare($conn, $assigned_contacts_query);
mysqli_stmt_bind_param($assigned_contacts_stmt, "i", $sales_rep_id);
mysqli_stmt_execute($assigned_contacts_stmt);
$assigned_contacts_result = mysqli_stmt_get_result($assigned_contacts_stmt);

// Get completed leads (customers with sales records)
$completed_leads_query = "SELECT DISTINCT c.id, c.first_name, c.last_name, c.email, c.phone, c.customer_interest, c.created_at FROM contacts c
                         INNER JOIN sales s ON c.id = s.contact_id
                         WHERE c.assigned_to = ?
                         ORDER BY c.created_at DESC";
$completed_leads_stmt = mysqli_prepare($conn, $completed_leads_query);
mysqli_stmt_bind_param($completed_leads_stmt, "i", $sales_rep_id);
mysqli_stmt_execute($completed_leads_stmt);
$completed_leads_result = mysqli_stmt_get_result($completed_leads_stmt);

// Get ongoing leads (contacts without sales records)
$ongoing_leads_query = "SELECT c.id, c.first_name, c.last_name, c.email, c.phone, c.customer_interest, c.created_at FROM contacts c
                       LEFT JOIN sales s ON c.id = s.contact_id
                       WHERE c.assigned_to = ? AND s.contact_id IS NULL
                       ORDER BY c.created_at DESC";
$ongoing_leads_stmt = mysqli_prepare($conn, $ongoing_leads_query);
mysqli_stmt_bind_param($ongoing_leads_stmt, "i", $sales_rep_id);
mysqli_stmt_execute($ongoing_leads_stmt);
$ongoing_leads_result = mysqli_stmt_get_result($ongoing_leads_stmt);

// Get contact statistics
$total_assigned_query = "SELECT COUNT(*) as total FROM contacts WHERE assigned_to = ?";
$total_assigned_stmt = mysqli_prepare($conn, $total_assigned_query);
mysqli_stmt_bind_param($total_assigned_stmt, "i", $sales_rep_id);
mysqli_stmt_execute($total_assigned_stmt);
$total_assigned_result = mysqli_stmt_get_result($total_assigned_stmt);
$total_assigned_row = mysqli_fetch_assoc($total_assigned_result);
$total_assigned = $total_assigned_row['total'];

$total_converted_query = "SELECT COUNT(DISTINCT c.id) as total FROM contacts c
                         INNER JOIN sales s ON c.id = s.contact_id
                         WHERE c.assigned_to = ?";
$total_converted_stmt = mysqli_prepare($conn, $total_converted_query);
mysqli_stmt_bind_param($total_converted_stmt, "i", $sales_rep_id);
mysqli_stmt_execute($total_converted_stmt);
$total_converted_result = mysqli_stmt_get_result($total_converted_stmt);
$total_converted_row = mysqli_fetch_assoc($total_converted_result);
$total_converted = $total_converted_row['total'];

$conversion_rate = calculate_conversion_rate($total_assigned, $total_converted);

// Get recent sales
$recent_sales_query = "SELECT s.*, CONCAT(c.first_name, ' ', c.last_name) as customer_name, p.name as product_name
                      FROM sales s
                      LEFT JOIN contacts c ON s.contact_id = c.id
                      LEFT JOIN products p ON s.product_id = p.id
                      WHERE s.sales_rep_id = ?
                      ORDER BY s.sale_date DESC LIMIT 5";
$recent_sales_stmt = mysqli_prepare($conn, $recent_sales_query);
mysqli_stmt_bind_param($recent_sales_stmt, "i", $sales_rep_id);
mysqli_stmt_execute($recent_sales_stmt);
$recent_sales_result = mysqli_stmt_get_result($recent_sales_stmt);

// Get total sales amount
$total_sales_amount_query = "SELECT SUM(total_amount) as total FROM sales WHERE sales_rep_id = ?";
$total_sales_amount_stmt = mysqli_prepare($conn, $total_sales_amount_query);
mysqli_stmt_bind_param($total_sales_amount_stmt, "i", $sales_rep_id);
mysqli_stmt_execute($total_sales_amount_stmt);
$total_sales_amount_result = mysqli_stmt_get_result($total_sales_amount_stmt);
$total_sales_amount_row = mysqli_fetch_assoc($total_sales_amount_result);
$total_sales_amount = $total_sales_amount_row['total'] ? $total_sales_amount_row['total'] : 0;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="img/minilogo.jpg" type="image/x-icon">
    <title>Sales Representative Dashboard - CRM System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .text-color{
        color: #FF6500;
        }
        .input-focus {
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        .input-focus:focus {
            border-color: #FF6500;
            box-shadow: 0 0 0 3px rgba(255, 101, 0, 0.1);
        }

        .button-hover {
            transition: background-color 0.3s ease, transform 0.2s ease;
        }

        .button-hover:hover {
            background-color: #e55b00;
            transform: scale(1.05);
        }
    </style>
</head>
<body class="">
<?php include 'includes/navigation.php'; ?>

    <!-- Main Content -->
    <div class="ml-0 min-[870px]:ml-60 min-h-screen flex flex-col">
        <!-- Navbar -->
        <header class="bg-[#1E3E62] p-4 flex justify-between z-10 flex-col">
            <h1 class="text-2xl font-bold text-white mx-10 my-2">Sales Representative Dashboard</h1>
                    </header>
        <main class="flex-1 overflow-y-auto space-y-10">
            <!-- Analytics Cards -->
            <section class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-10 px-10" style="background: linear-gradient(to bottom, #1E3E62 60%, white 50%);">
                <div class="bg-white p-4 rounded-2xl shadow-xl h-[150px]">
                    <img src="img/totalsales.png" alt="" class="w-10">
                    <p class="text-sm text-gray-500 ">Assigned Contacts</p>
                    <h2 class="text-xl font-bold mt-2 text-color"><?php echo $total_assigned; ?></h2>
                </div>
                <div class="bg-white p-4 rounded-2xl shadow-xl h-[150px]">
                    <img src="img/revenue.png" alt="" class="w-10">
                    <p class="text-sm text-gray-500">Conversion Rate</p>
                    <h2 class="text-xl font-bold mt-2 text-color"><?php echo $conversion_rate; ?></h2>
                </div>
                <div class="bg-white p-4 rounded-2xl shadow-xl h-[150px]">
                    <img src="img/revenue.png" alt="" class="w-10">
                    <p class="text-sm text-gray-500">Total Sales</p>
                    <h2 class="text-xl font-bold mt-2 text-color"><?php echo format_currency($total_sales_amount); ?></h2>
                </div>
            </section>

            <!-- Ongoing Leads Section -->
            <section class="px-10">
                <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                    <div class="px-6 py-5 border-b border-gray-200 sm:px-8" style="background: linear-gradient(to right, #1E3E62, #0B192C);">
                        <div class="flex justify-between items-center">
                            <div>
                                <h3 class="text-lg leading-6 font-medium text-white">
                                    Active contacts
                                </h3>
                                <p class="mt-1 max-w-2xl text-sm text-white">
                                    Contacts that need your attention
                                </p>
                            </div>
                            <div>
                                <a href="my_contacts.php" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-[#FF6500] button-hover">
                                    View All
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <?php if (mysqli_num_rows($ongoing_leads_result) > 0) : ?>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">S.No.</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                            <!-- <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th> -->
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Interest</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                                            <!-- <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th> -->
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <?php 
                                        $count = 0;
                                        while ($row = mysqli_fetch_assoc($ongoing_leads_result)) {
                                            if ($count >= 5) break; // Show only 5 leads
                                            $count++;
                                            
                                            echo '<tr>';
                                            echo '<td class="px-6 py-4 whitespace-nowrap">';
                                            echo '<div class="text-sm font-medium text-gray-900">' . $count . '</div>';
                                            echo '</td>';
                                            echo '<td class="px-6 py-4 whitespace-nowrap">';
                                            echo '<div class="text-sm font-medium text-gray-900">' . htmlspecialchars($row['first_name'] . ' ' . $row['last_name']) . '</div>';
                                            echo '</td>';
                                            // echo '<td class="px-6 py-4 whitespace-nowrap">';
                                            // echo '<div class="text-sm text-gray-900">' . htmlspecialchars($row['email']) . '</div>';
                                            // echo '</td>';
                                            echo '<td class="px-6 py-4 whitespace-nowrap">';
                                            echo '<div class="text-sm text-gray-500">' . htmlspecialchars($row['phone']) . '</div>';
                                            echo '</td>';
                                            echo '<td class="px-6 py-4 whitespace-nowrap">';
                                            echo '<div class="text-sm text-gray-900">' . htmlspecialchars($row['customer_interest'] ?? '') . '</div>';
                                            echo '</td>';
                                            echo '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">';
                                            echo date('M d, Y', strtotime($row['created_at']));
                                            echo '</td>';
                                            // echo '<td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">';
                                            // echo '<a href="view_lead.php?id=' . $row['id'] . '" class="text-blue-600 hover:text-blue-900 mx-2" title="View Lead"><i class="fas fa-eye"></i></a>';
                                            // echo '</td>';
                                            echo '</tr>';
                                        }
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else : ?>
                            <p class="text-gray-500">No active leads assigned to you.</p>
                        <?php endif; ?>
                    </div>
                </div>
            </section>

            <!-- Recent Sales Section -->
            <section class="px-10 mb-20">
                <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                    <div class="px-6 py-5 border-b border-gray-200 sm:px-8" style="background: linear-gradient(to right, #1E3E62, #0B192C);">
                        <div class="flex justify-between items-center">
                            <div>
                                <h3 class="text-lg leading-6 font-medium text-white">
                                    Recent Sales
                                </h3>
                                <p class="mt-1 max-w-2xl text-sm text-white">
                                    Your most recent sales transactions
                                </p>
                            </div>
                            <div>
                                <a href="my_sales.php" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-[#FF6500] button-hover">
                                    View All
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <?php if (mysqli_num_rows($recent_sales_result) > 0) : ?>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                                            <!-- <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Serial Number</th> -->
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment Method</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <?php while ($row = mysqli_fetch_assoc($recent_sales_result)) : ?>
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($row['customer_name']); ?></div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm text-gray-900"><?php echo htmlspecialchars($row['product_name']); ?></div>
                                                </td>
                                                <!-- <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm text-gray-900"><?php echo htmlspecialchars($row['serial_number']); ?></div>
                                                </td> -->
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm text-gray-900"><?php echo format_currency($row['total_amount']); ?></div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="text-sm text-gray-900"><?php echo date('M d, Y', strtotime($row['sale_date'])); ?></div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                        <?php echo htmlspecialchars($row['payment_method']); ?>
                                                    </span>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else : ?>
                            <p class="text-gray-500">No sales recorded yet.</p>
                        <?php endif; ?>
                    </div>
                </div>
            </section>
        </main>
    </div>
</body>
</html>
