<?php
// Get database connection using reliable method
$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';

// Start session if not already started
ensure_session_started();

// Check if database is set up first
require_once 'check_database_setup.php';
redirect_to_setup_if_needed();

// Check if the user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: auth/login.php");
    exit;
}

// Get sale ID from URL
$sale_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($sale_id <= 0) {
    header("Location: sales.php");
    exit;
}

// Get sale details
$sql = "SELECT s.*, c.first_name, c.last_name, c.email, c.phone, c.address,
               p.name as product_name, p.price,
               e.first_name as sales_rep_first_name, e.last_name as sales_rep_last_name
        FROM sales s
        LEFT JOIN contacts c ON s.contact_id = c.id
        LEFT JOIN products p ON s.product_id = p.id
        LEFT JOIN employees e ON s.sales_rep_id = e.id
        WHERE s.id = ?";

$stmt = mysqli_prepare($conn, $sql);
mysqli_stmt_bind_param($stmt, "i", $sale_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) == 0) {
    header("Location: sales.php");
    exit;
}

$sale = mysqli_fetch_assoc($result);

// Initialize variables
$sale_items = [];
$invoice_number = $sale['invoice_number'];

if ($invoice_number) {
    // Get all sales with the same invoice number
    $items_sql = "SELECT s.*, p.name as product_name
                 FROM sales s
                 JOIN products p ON s.product_id = p.id
                 WHERE s.invoice_number = ?
                 ORDER BY s.id";
    $items_stmt = mysqli_prepare($conn, $items_sql);
    mysqli_stmt_bind_param($items_stmt, "s", $invoice_number);
    mysqli_stmt_execute($items_stmt);
    $items_result = mysqli_stmt_get_result($items_stmt);

    while ($item = mysqli_fetch_assoc($items_result)) {
        $sale_items[] = $item;
    }
} else {
    // If no invoice number, just use this single sale
    $sale_items[] = array_merge($sale, ['product_name' => $sale['product_name']]);
}

// Auto-generate invoice and redirect if accessed via GET (button click from sales page)
if ($_SERVER["REQUEST_METHOD"] === "GET" && $sale_id > 0) {
    $document_type = 'invoice';
    $payment_status = $sale['invoice_status'] ?? 'pending';
    $document_number = $sale['invoice_number'];
    if (!$document_number) {
        $document_number = 'INV-' . date('Ymd') . '-' . $sale_id;
        $sql = "UPDATE sales SET invoice_number = ?, invoice_status = ?, invoice_date = CURDATE() WHERE id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "ssi", $document_number, $payment_status, $sale_id);
        mysqli_stmt_execute($stmt);
    }
    $doc_status = ucfirst($payment_status);
    $doc_date = date('d M, Y');
    header("Location: print_document.php?type=invoice&number=" . urlencode($document_number) . "&sale_id=" . $sale_id . "&status=" . urlencode($doc_status) . "&date=" . urlencode($doc_date));
    exit;
}

// Process form submission for generating document
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['action'])) {
    if ($_POST['action'] == 'generate_pdf') {
        $document_type = 'invoice'; // Only invoice generation
        $payment_status = isset($_POST['payment_status']) ? $_POST['payment_status'] : 'pending';
        $payment_date = isset($_POST['payment_date']) ? $_POST['payment_date'] : date('Y-m-d');
        
        // Generate document number
        $document_number = 'INV-' . date('Ymd') . '-' . $sale_id;

        // Update sales records with invoice information
        $sql = "UPDATE sales SET
                invoice_number = ?,
                invoice_status = ?,
                invoice_date = CURDATE()
                WHERE id = ?";

        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "ssi", $document_number, $payment_status, $sale_id);
        mysqli_stmt_execute($stmt);

        // Since we're using unified sales table, no separate invoice tables needed
        // The invoice_status and payment information is stored in the sales table
        
        // Set variables for the document (invoice only)
        $doc_title = 'Invoice';
        $doc_number = $document_number;
        $doc_date = date('d M, Y');
        $doc_status = ucfirst($payment_status);
        $customer_label = 'Bill To:';
        $total_label = 'Total';
        $thank_you_message = 'Thank you for your business!';

        // Redirect to print page
        header("Location: print_document.php?type=invoice&number=" . urlencode($document_number) . "&sale_id=" . $sale_id . "&status=" . urlencode($doc_status) . "&date=" . urlencode($doc_date));
        exit;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="img/minilogo.jpg" type="image/x-icon">
    <title>Generate Invoice - CRM System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .text-color{ color: #FF6500; }
        .input-focus {
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        .input-focus:focus {
            border-color: #FF6500;
            box-shadow: 0 0 0 3px rgba(255, 101, 0, 0.1);
        }

        .button-hover {
            transition: background-color 0.3s ease, transform 0.2s ease;
        }

        .button-hover:hover {
            transform: translateY(-1px);
        }

        @media print {
            .no-print {
                display: none !important;
            }
            
            body {
                margin: 0;
                padding: 0;
            }
            
            .print-container {
                width: 100%;
                max-width: none;
                margin: 0;
                padding: 20px;
                box-shadow: none;
            }
        }
    </style>
</head>
<body class="">

<?php include 'includes/navigation.php'; ?>

<!-- Main Content -->
<div class="ml-0 min-[870px]:ml-60 min-h-screen flex flex-col">
    <!-- Navbar -->
    <header class="bg-[#1E3E62] p-4 flex justify-between z-10 flex-col no-print">
        <h1 class="text-2xl font-bold text-white mx-10 my-2">Generate Invoice</h1>
            </header>
    
    <main class="flex-1 p-10">
        <?php if (isset($success_message)): ?>
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
            <i class="fas fa-check-circle mr-2"></i><?php echo $success_message; ?>
        </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <i class="fas fa-exclamation-circle mr-2"></i><?php echo $error_message; ?>
        </div>
        <?php endif; ?>

        <!-- Sale Information -->
        <div class="bg-white shadow rounded-lg mb-8">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Sale Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Customer</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo htmlspecialchars($sale['first_name'] . ' ' . $sale['last_name']); ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Product</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo htmlspecialchars($sale['product_name']); ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Amount</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo format_currency($sale['total_amount']); ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Sale Date</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo date('d M, Y', strtotime($sale['sale_date'])); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Generate Document Form -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Generate Invoice</h3>
                
                <div class="px-6 py-5 sm:p-6">
                    <form method="POST" class="space-y-6">
                        <input type="hidden" name="action" value="generate_pdf">
                        
                        <input type="hidden" name="document_type" value="invoice">
                        
                        <div id="invoice-options" css="space-y-4">
                            <div>
                                <label for="payment_status" class="block text-sm font-medium text-gray-700">Invoice Status</label>
                                <select id="payment_status" name="payment_status" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md p-2 border input-focus">
                                    <option value="pending">Pending</option>
                                    <option value="paid">Paid</option>
                                    <option value="cancelled">Cancelled</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="flex justify-end space-x-4">
                            <a href="view_sale.php?id=<?php echo $sale_id; ?>" class="px-6 py-2 bg-[#0b192c] text-white rounded-sm hover:bg-[#1e3e62] button-hover">
                                <i class="fas fa-arrow-left mr-2"></i>Back to Sale
                            </a>
                            <button type="submit" class="px-6 py-2 bg-[#0b192c] text-white rounded-sm hover:bg-[#1e3e62] button-hover">
                                <i class="fas fa-file-pdf mr-2"></i>Generate Invoice
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    
    </main>
</div>

</body>
</html>
