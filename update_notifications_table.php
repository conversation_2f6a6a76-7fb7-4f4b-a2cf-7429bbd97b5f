<?php
// Include database connection
$conn = require_once 'get_db_connection.php';

// Check if the columns already exist
$check_type_column = mysqli_query($conn, "SHOW COLUMNS FROM notifications LIKE 'type'");
$check_title_column = mysqli_query($conn, "SHOW COLUMNS FROM notifications LIKE 'title'");
$check_related_id_column = mysqli_query($conn, "SHOW COLUMNS FROM notifications LIKE 'related_id'");

// Add type column if it doesn't exist
if (mysqli_num_rows($check_type_column) == 0) {
    $add_type_sql = "ALTER TABLE notifications ADD COLUMN type VARCHAR(50) AFTER message";
    if (mysqli_query($conn, $add_type_sql)) {
        echo "Added 'type' column to notifications table.<br>";
    } else {
        echo "Error adding 'type' column: " . mysqli_error($conn) . "<br>";
    }
}

// Add title column if it doesn't exist
if (mysqli_num_rows($check_title_column) == 0) {
    $add_title_sql = "ALTER TABLE notifications ADD COLUMN title VARCHAR(255) AFTER message";
    if (mysqli_query($conn, $add_title_sql)) {
        echo "Added 'title' column to notifications table.<br>";
    } else {
        echo "Error adding 'title' column: " . mysqli_error($conn) . "<br>";
    }
}

// Add related_id column if it doesn't exist
if (mysqli_num_rows($check_related_id_column) == 0) {
    $add_related_id_sql = "ALTER TABLE notifications ADD COLUMN related_id INT AFTER link";
    if (mysqli_query($conn, $add_related_id_sql)) {
        echo "Added 'related_id' column to notifications table.<br>";
    } else {
        echo "Error adding 'related_id' column: " . mysqli_error($conn) . "<br>";
    }
}

// Update existing notifications to set type and related_id based on link
$update_sql = "UPDATE notifications SET 
               type = 'lead_created',
               related_id = SUBSTRING_INDEX(SUBSTRING_INDEX(link, 'id=', -1), '&', 1)
               WHERE link LIKE '%view_contact.php?id=%' OR link LIKE '%view_lead.php?id=%'";

if (mysqli_query($conn, $update_sql)) {
    echo "Updated existing notifications with type and related_id.<br>";
} else {
    echo "Error updating existing notifications: " . mysqli_error($conn) . "<br>";
}

echo "<p>Notifications table update completed. <a href='notifications.php'>Go back to notifications</a></p>";

// Close connection
mysqli_close($conn);
?>