# Disable directory browsing
Options -Indexes

# Protect the .htaccess file
<Files .htaccess>
    Order Allow,<PERSON><PERSON> from all
</Files>

# Protect config files
<FilesMatch "^(database\.php|init_db\.php)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Redirect to index.php if trying to access a PHP file directly in includes or config
RewriteEngine On
RewriteRule ^(includes|config)/.*\.php$ - [F,L]

# Protect against XSS attacks
<IfModule mod_headers.c>
    Header set X-XSS-Protection "1; mode=block"
    Header set X-Content-Type-Options "nosniff"
    Header set X-Frame-Options "SAMEORIGIN"
    Header set Content-Security-Policy "default-src 'self' https://cdn.tailwindcss.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; img-src 'self' data:; font-src 'self' https://cdnjs.cloudflare.com;"
</IfModule>

# Enable display errors for development
php_flag display_errors on
php_flag log_errors on
php_value error_log "error_log.txt"