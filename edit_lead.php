<?php
// Get database connection using reliable method
$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';
require_once 'includes/validation.php';

// Check if database is set up first
require_once 'check_database_setup.php';
redirect_to_setup_if_needed();

// Check if the user is logged in
ensure_session_started();
require_login();

// Get lead ID from URL
$lead_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($lead_id <= 0) {
    header("Location: leads.php");
    exit;
}

$error_message = '';
$success_message = '';

// Check if user has permission to edit this lead
$is_sales_rep = isset($_SESSION['role']) && $_SESSION['role'] === 'sales_rep';
$sales_rep_id = $is_sales_rep ? $_SESSION['user_id'] : 0;

// Get lead data
$sql = "SELECT * FROM leads WHERE id = ?";
if ($is_sales_rep) {
    $sql .= " AND assigned_to = ?";
}

$stmt = mysqli_prepare($conn, $sql);
if ($is_sales_rep) {
    mysqli_stmt_bind_param($stmt, "ii", $lead_id, $sales_rep_id);
} else {
    mysqli_stmt_bind_param($stmt, "i", $lead_id);
}
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) == 0) {
    header("Location: leads.php?error=Lead not found or access denied");
    exit;
}

$lead = mysqli_fetch_assoc($result);

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $first_name = sanitize_input($_POST['first_name']);
    $last_name = sanitize_input($_POST['last_name']);
    $email = sanitize_input($_POST['email']);
    $phone = sanitize_input($_POST['phone']);
    $country_code = sanitize_input($_POST['country_code']);
    $company = sanitize_input($_POST['company']);
    $customer_interest = sanitize_input($_POST['customer_interest']);
    $source = sanitize_input($_POST['source']);
    $status = sanitize_input($_POST['status']);
    $notes = sanitize_input($_POST['notes']);
    $assigned_to = !empty($_POST['assigned_to']) ? intval($_POST['assigned_to']) : null;
    
    // Validation
    $errors = [];
    
    if (empty($first_name)) {
        $errors[] = "First name is required.";
    }
    
    if (empty($last_name)) {
        $errors[] = "Last name is required.";
    }
    
    if (empty($email)) {
        $errors[] = "Email is required.";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Invalid email format.";
    } else {
        // Check if email exists for other leads
        $email_check_sql = "SELECT id FROM leads WHERE email = ? AND id != ?";
        $email_stmt = mysqli_prepare($conn, $email_check_sql);
        mysqli_stmt_bind_param($email_stmt, "si", $email, $lead_id);
        mysqli_stmt_execute($email_stmt);
        $email_result = mysqli_stmt_get_result($email_stmt);
        
        if (mysqli_num_rows($email_result) > 0) {
            $errors[] = "Email already exists for another lead.";
        }
    }
    
    if (!empty($phone) && !preg_match("/^[0-9]{10}$/", $phone)) {
        $errors[] = "Phone number must be 10 digits.";
    }
    
    if (empty($errors)) {
        // Update lead
        $update_sql = "UPDATE leads SET 
                      first_name = ?, last_name = ?, email = ?, phone = ?, 
                      country_code = ?, company = ?, customer_interest = ?, 
                      source = ?, status = ?, notes = ?, assigned_to = ?,
                      updated_at = NOW()
                      WHERE id = ?";
        
        $update_stmt = mysqli_prepare($conn, $update_sql);
        mysqli_stmt_bind_param($update_stmt, "ssssssssssii", 
            $first_name, $last_name, $email, $phone, $country_code, 
            $company, $customer_interest, $source, $status, $notes, 
            $assigned_to, $lead_id);
        
        if (mysqli_stmt_execute($update_stmt)) {
            $success_message = "Lead updated successfully!";
            
            // Refresh lead data
            $stmt = mysqli_prepare($conn, "SELECT * FROM leads WHERE id = ?");
            mysqli_stmt_bind_param($stmt, "i", $lead_id);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            $lead = mysqli_fetch_assoc($result);
        } else {
            $error_message = "Error updating lead: " . mysqli_error($conn);
        }
    } else {
        $error_message = implode("<br>", $errors);
    }
}

// Get all employees for assignment dropdown
$employees_sql = "SELECT id, CONCAT(first_name, ' ', last_name) as name FROM employees WHERE role IN ('sales_rep', 'manager') ORDER BY first_name, last_name";
$employees_result = mysqli_query($conn, $employees_sql);

// Get all products for interest dropdown
$products_sql = "SELECT id, name, price, category FROM products ORDER BY category, name";
$products_result = mysqli_query($conn, $products_sql);

$page_title = "Edit Lead";
include __DIR__ . '/includes/header.php';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="img/minilogo.jpg" type="image/x-icon">
    <title>Edit Lead - CRM System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">

<?php include 'includes/navigation.php'; ?>

<!-- Main Content -->
<div class="ml-0 min-[870px]:ml-60 min-h-screen flex flex-col">
    <header class="bg-[#1E3E62] p-4 flex justify-between items-center z-10">
        <h1 class="text-2xl font-bold text-white mx-10 my-2">Edit Lead</h1>
        <div class="mx-10">
            <?php include 'includes/top_notification.php'; ?>
        </div>
    </header>

    <main class="flex-1 p-10">
        <!-- Back Button -->
        <div class="mb-6">
            <a href="leads.php" class="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Leads
            </a>
        </div>

        <!-- Success/Error Messages -->
        <?php if (!empty($success_message)): ?>
            <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                <?php echo $success_message; ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($error_message)): ?>
            <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                <?php echo $error_message; ?>
            </div>
        <?php endif; ?>

        <!-- Edit Form -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <form method="POST" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- First Name -->
                    <div>
                        <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2">First Name *</label>
                        <input type="text" id="first_name" name="first_name" value="<?php echo htmlspecialchars($lead['first_name']); ?>" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                    </div>

                    <!-- Last Name -->
                    <div>
                        <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2">Last Name *</label>
                        <input type="text" id="last_name" name="last_name" value="<?php echo htmlspecialchars($lead['last_name']); ?>" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                    </div>

                    <!-- Email -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email *</label>
                        <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($lead['email']); ?>" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                    </div>

                    <!-- Phone -->
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                        <div class="flex">
                            <select name="country_code" class="px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="+91" <?php echo ($lead['country_code'] == '+91') ? 'selected' : ''; ?>>+91</option>
                                <option value="+1" <?php echo ($lead['country_code'] == '+1') ? 'selected' : ''; ?>>+1</option>
                                <option value="+44" <?php echo ($lead['country_code'] == '+44') ? 'selected' : ''; ?>>+44</option>
                            </select>
                            <input type="text" id="phone" name="phone" value="<?php echo htmlspecialchars($lead['phone']); ?>" 
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-r-md focus:outline-none focus:ring-2 focus:ring-blue-500" 
                                   pattern="[0-9]{10}" title="Please enter a 10-digit phone number">
                        </div>
                    </div>
                </div>

                <!-- Company -->
                <div>
                    <label for="company" class="block text-sm font-medium text-gray-700 mb-2">Company</label>
                    <input type="text" id="company" name="company" value="<?php echo htmlspecialchars($lead['company']); ?>" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Interest -->
                    <div>
                        <label for="customer_interest" class="block text-sm font-medium text-gray-700 mb-2">Interest</label>
                        <select id="customer_interest" name="customer_interest" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">Select interest</option>
                            <?php
                            if ($products_result && mysqli_num_rows($products_result) > 0) {
                                $current_category = '';
                                while ($product = mysqli_fetch_assoc($products_result)) {
                                    if ($current_category != $product['category']) {
                                        if ($current_category != '') echo '</optgroup>';
                                        echo '<optgroup label="' . htmlspecialchars($product['category']) . '">';
                                        $current_category = $product['category'];
                                    }
                                    $selected = ($lead['customer_interest'] == $product['name']) ? 'selected' : '';
                                    echo '<option value="' . htmlspecialchars($product['name']) . '" ' . $selected . '>' . 
                                         htmlspecialchars($product['name']) . ' - ₹' . number_format($product['price'], 2) . '</option>';
                                }
                                if ($current_category != '') echo '</optgroup>';
                            }
                            ?>
                            <option value="General Inquiry" <?php echo ($lead['customer_interest'] == 'General Inquiry') ? 'selected' : ''; ?>>General Inquiry</option>
                        </select>
                    </div>

                    <!-- Status -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <select id="status" name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="new" <?php echo ($lead['status'] == 'new') ? 'selected' : ''; ?>>New</option>
                            <option value="contacted" <?php echo ($lead['status'] == 'contacted') ? 'selected' : ''; ?>>Contacted</option>
                            <option value="qualified" <?php echo ($lead['status'] == 'qualified') ? 'selected' : ''; ?>>Qualified</option>
                            <option value="lost" <?php echo ($lead['status'] == 'lost') ? 'selected' : ''; ?>>Lost</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Source -->
                    <div>
                        <label for="source" class="block text-sm font-medium text-gray-700 mb-2">Source</label>
                        <input type="text" id="source" name="source" value="<?php echo htmlspecialchars($lead['source']); ?>" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <!-- Assigned To -->
                    <?php if (!$is_sales_rep): ?>
                    <div>
                        <label for="assigned_to" class="block text-sm font-medium text-gray-700 mb-2">Assigned To</label>
                        <select id="assigned_to" name="assigned_to" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">Unassigned</option>
                            <?php while ($employee = mysqli_fetch_assoc($employees_result)): ?>
                                <option value="<?php echo $employee['id']; ?>" <?php echo ($lead['assigned_to'] == $employee['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($employee['name']); ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Notes -->
                <div>
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                    <textarea id="notes" name="notes" rows="4" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo htmlspecialchars($lead['notes']); ?></textarea>
                </div>

                <!-- Submit Buttons -->
                <div class="flex justify-end space-x-4">
                    <a href="leads.php" class="px-6 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors">
                        Cancel
                    </a>
                    <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                        Update Lead
                    </button>
                </div>
            </form>
        </div>
    </main>
</div>

</body>
</html>
