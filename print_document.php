<?php

$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';

// Check if database is set up first
require_once 'check_database_setup.php';
redirect_to_setup_if_needed();

// Check if the user is logged in
ensure_session_started();
require_login();

// Check if required parameters are provided
if (!isset($_GET['type']) || !isset($_GET['number']) || !isset($_GET['sale_id'])) {
    // Redirect based on user role
    if (has_role('sales_rep')) {
        header("Location: my_sales.php");
    } else {
        header("Location: sales.php");
    }
    exit;
}

$document_type = 'invoice'; // Only invoice generation
$document_number = $_GET['number'];
$sale_id = intval($_GET['sale_id']);
$document_status = isset($_GET['status']) ? $_GET['status'] : 'Pending';
$document_date = isset($_GET['date']) ? $_GET['date'] : date('d M, Y');

// Get sale details with unified contacts table
$sql = "SELECT s.*, CONCAT(c.first_name, ' ', c.last_name) as customer_name, c.email as customer_email, c.phone as customer_phone,
        c.address as customer_address,
        p.name as product_name, p.category as product_category, CONCAT(u.first_name, ' ', u.last_name) as sales_rep_name
        FROM sales s
        LEFT JOIN contacts c ON s.contact_id = c.id
        LEFT JOIN products p ON s.product_id = p.id
        LEFT JOIN employees u ON s.sales_rep_id = u.id
        WHERE s.id = ?";
$stmt = mysqli_prepare($conn, $sql);
mysqli_stmt_bind_param($stmt, "i", $sale_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) == 0) {
    // Redirect based on user role
    if (has_role('sales_rep')) {
        header("Location: my_sales.php");
    } else {
        header("Location: sales.php");
    }
    exit;
}

$sale = mysqli_fetch_assoc($result);

// Set document-specific variables (invoice only)
$doc_title = 'INVOICE';
$customer_label = 'Bill To:';
$details_title = 'Invoice Details';
$total_label = 'Total';
$thank_you_message = 'Thank you for your business!';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="img/minilogo.jpg" type="image/x-icon">
    <title><?php echo ucfirst($document_type); ?> #<?php echo $document_number; ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Add html2pdf.js library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
    <style>
        @media print {
            .no-print {
                display: none !important;
            }
            body {
                padding: 0;
                margin: 0;
            }
            .container {
                max-width: 100%;
                width: 100%;
                padding: 20px;
            }
            button, .print-button, .back-button, .download-button {
                display: none !important;
            }
        }
        
        @page {
            size: A4;
            margin: 10mm;
        }
    </style>
</head>
<body class="bg-gray-100">
    
    <div class="bg-[#1E3E62] p-4 flex justify-between items-center no-print">
        <h1 class="text-xl font-bold text-white"><?php echo ucfirst($document_type); ?> #<?php echo $document_number; ?></h1>
        <div class="flex space-x-3 no-print">
            <button onclick="downloadPDF()" class="download-button inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 no-print">
                <i class="fas fa-download mr-2"></i> Download PDF
            </button>
            <button onclick="window.print()" class="print-button inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-sm shadow-sm text-white bg-[#0b192c] hover:bg-[#1e3e62] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0b192c] no-print">
                <i class="fas fa-print mr-2"></i> Print Document
            </button>
            <button onclick="window.location.href='<?php echo has_role('sales_rep') ? 'my_sales.php' : 'sales.php'; ?>'" class="back-button inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-sm shadow-sm text-white bg-[#0b192c] hover:bg-[#1e3e62] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0b192c] no-print">
                <i class="fas fa-arrow-left mr-2"></i> Back to Sales
            </button>
        </div>
    </div>
    
    <!-- Document Content -->
    <div id="document-container" class="container mx-auto my-8 bg-white shadow-lg rounded-lg overflow-hidden p-8">
        <!-- Document Header -->
        <div class="flex justify-between items-start mb-8">
            <div>
                <img src="img/logo.png" alt="Company Logo" class="h-16 mb-2">
                <p class="text-gray-600">Your Company Name</p>
                <p class="text-gray-600">123 Business Street</p>
                <p class="text-gray-600">City, State, ZIP</p>
                <p class="text-gray-600">Phone: (*************</p>
                <p class="text-gray-600">Email: <EMAIL></p>
            </div>
            <div class="text-right">
                <h1 class="text-2xl font-bold text-gray-800"><?php echo $doc_title; ?></h1>
                <p class="text-gray-600"><?php echo ucfirst($document_type); ?> #: <?php echo $document_number; ?></p>
                <p class="text-gray-600">Date: <?php echo $document_date; ?></p>
                <?php if ($document_type == 'invoice'): ?>
                <p class="text-gray-600">Status: <?php echo $document_status; ?></p>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Customer Information -->
        <div class="mb-8">
            <h2 class="text-lg font-semibold text-gray-800 mb-2"><?php echo $customer_label; ?></h2>
            <p class="text-gray-700"><?php echo htmlspecialchars($sale['customer_name']); ?></p>
            <?php if (!empty($sale['customer_company'])): ?>
            <p class="text-gray-600"><?php echo htmlspecialchars($sale['customer_company']); ?></p>
            <?php endif; ?>
            <?php if (!empty($sale['customer_email'])): ?>
            <p class="text-gray-600">Email: <?php echo htmlspecialchars($sale['customer_email']); ?></p>
            <?php endif; ?>
            <?php if (!empty($sale['customer_phone'])): ?>
            <p class="text-gray-600">Phone: <?php echo htmlspecialchars($sale['customer_phone']); ?></p>
            <?php endif; ?>
            <?php if (!empty($sale['customer_address'])): ?>
            <p class="text-gray-600">Address: <?php echo htmlspecialchars($sale['customer_address']); ?></p>
            <?php endif; ?>
        </div>
        
        <!-- Document Details -->
        <div class="mb-8">
            <h2 class="text-lg font-semibold text-gray-800 mb-4"><?php echo $details_title; ?></h2>
            <table class="min-w-full divide-y divide-gray-200">
                <thead>
                    <tr class="bg-gray-50">
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sale Date</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment Method</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo htmlspecialchars($sale['product_name']); ?></td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><?php echo date('d M, Y', strtotime($sale['sale_date'])); ?></td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><?php echo htmlspecialchars($sale['payment_method']); ?></td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right"><?php echo format_currency($sale['total_amount']); ?></td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr class="bg-gray-50">
                        <td colspan="3" class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-right"><?php echo $total_label; ?></td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 text-right"><?php echo format_currency($sale['total_amount']); ?></td>
                    </tr>
                </tfoot>
            </table>
        </div>
        
        
        <!-- Terms and Conditions for Invoice -->
        <?php if ($document_type == 'invoice'): ?>
        <div class="mb-8">
            <h2 class="text-lg font-semibold text-gray-800 mb-2">Terms and Conditions:</h2>
            <ol class="list-decimal pl-5 text-gray-600 text-sm">
                <li class="mb-1">Payment is due within 30 days of invoice date.</li>
                <li class="mb-1">Please make checks payable to Your Company Name.</li>
                <li class="mb-1">For any questions regarding this invoice, please contact our finance department.</li>
            </ol>
        </div>
        <?php endif; ?>
        
        <!-- Thank You Note -->
        <div class="text-center text-gray-600 mt-12 mb-6">
            <p class="text-lg font-semibold"><?php echo $thank_you_message; ?></p>

        </div>
    </div>
    
    <script>
        // Variables to track UI state
        let printDialogOpened = false;
        
        // Function to restore the page to its normal state
        function restorePageState() {
            // Show the header section
            document.querySelectorAll('.no-print').forEach(function(element) {
                element.style.display = '';
            });
            
            // Make sure the document content is visible
            const documentContainer = document.getElementById('document-container');
            documentContainer.style.display = 'block';
            
            // Reset the print dialog state
            printDialogOpened = false;
        }
        
        // Function to prepare the page for printing
        function prepareForPrinting() {
            // Hide elements that shouldn't be printed
            document.querySelectorAll('.no-print').forEach(function(element) {
                element.style.display = 'none';
            });
            
            // Set the print dialog state
            printDialogOpened = true;
        }
        
        // Auto-print when the page loads
        window.onload = function() {
            // Add a small delay to ensure the page is fully rendered
            setTimeout(function() {
                // Prepare for printing
                prepareForPrinting();
                
                // Trigger the print dialog
                window.print();
            }, 500);
        };
        
        // Make sure the page is restored after printing or if print is canceled
        window.onafterprint = function() {
            restorePageState();
        };
        
        // Backup to ensure page is restored if print dialog is closed without printing
        document.addEventListener('mousemove', function() {
            if (printDialogOpened) {
                // If the print dialog was opened, restore the page state
                setTimeout(function() {
                    restorePageState();
                }, 500);
            }
        });
        
        // Function to download the document as PDF
        function downloadPDF() {
            // Show a loading indicator
            const downloadBtn = document.querySelector('.download-button');
            const originalBtnText = downloadBtn.innerHTML;
            downloadBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Preparing PDF...';
            downloadBtn.disabled = true;
            
            // Get the original document container
            const documentContainer = document.getElementById('document-container');
            
            // Create a direct PDF from the visible document container
            const opt = {
                margin: 10,
                filename: '<?php echo strtolower($document_type) . "_" . $document_number; ?>.pdf',
                image: { type: 'jpeg', quality: 0.98 },
                html2canvas: { 
                    scale: 2,
                    useCORS: true,
                    // Capture only the document container
                    windowWidth: documentContainer.offsetWidth,
                    windowHeight: documentContainer.offsetHeight
                },
                jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
            };
            
            // Generate and download the PDF directly from the visible container
            html2pdf()
                .from(documentContainer)
                .set(opt)
                .save()
                .then(() => {
                    // Restore the button text
                    downloadBtn.innerHTML = originalBtnText;
                    downloadBtn.disabled = false;
                })
                .catch((error) => {
                    console.error('PDF generation error:', error);
                    
                    // Restore the button text
                    downloadBtn.innerHTML = originalBtnText;
                    downloadBtn.disabled = false;
                    
                    // Show error message
                    alert('There was an error generating the PDF. Please try again.');
                });
        }
        
        // Add a print button event listener
        document.querySelector('.print-button').addEventListener('click', function() {
            prepareForPrinting();
            window.print();
        });
    </script>
</body>
</html>
