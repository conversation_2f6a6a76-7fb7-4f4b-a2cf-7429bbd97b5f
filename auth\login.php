<?php
// Get database connection using reliable method
$conn = require_once '../get_db_connection.php';
require_once '../includes/functions.php';

// Check if the employees table exists first
$table_check_sql = "SHOW TABLES LIKE 'employees'";
$table_result = mysqli_query($conn, $table_check_sql);

if (mysqli_num_rows($table_result) == 0) {
    // Employees table doesn't exist - redirect to setup
    header("Location: ../setup.php");
    exit;
}

// Check if the remember_token column exists and add it if it doesn't
$check_sql = "SHOW COLUMNS FROM employees LIKE 'remember_token'";
$result = mysqli_query($conn, $check_sql);

if (mysqli_num_rows($result) == 0) {
    // Add remember_token column
    $alter_sql = "ALTER TABLE employees ADD COLUMN remember_token VARCHAR(64) DEFAULT NULL";
    mysqli_query($conn, $alter_sql);
}

// Check if the token_expiry column exists and add it if it doesn't
$check_sql = "SHOW COLUMNS FROM employees LIKE 'token_expiry'";
$result = mysqli_query($conn, $check_sql);

if (mysqli_num_rows($result) == 0) {
    // Add token_expiry column
    $alter_sql = "ALTER TABLE employees ADD COLUMN token_expiry DATETIME DEFAULT NULL";
    mysqli_query($conn, $alter_sql);
}

// Check for remember me cookie
ensure_session_started();
if (!isset($_SESSION['user_id']) && isset($_COOKIE['remember_user']) && isset($_COOKIE['remember_token'])) {
    $user_id = $_COOKIE['remember_user'];
    $token = $_COOKIE['remember_token'];
    
    // Verify the token and check if it's not expired
    if (is_valid_remember_token($user_id, $token, $conn)) {
        // Get user information
        $sql = "SELECT id, username, role, first_name, last_name FROM employees WHERE id = ?";
        $stmt = mysqli_prepare($conn, $sql);
        mysqli_stmt_bind_param($stmt, "i", $user_id);
        mysqli_stmt_execute($stmt);
        $result = mysqli_stmt_get_result($stmt);
        
        if ($row = mysqli_fetch_assoc($result)) {
            // Token is valid and not expired, log the user in
            $_SESSION['user_id'] = $row['id'];
            $_SESSION['username'] = $row['username'];
            $_SESSION['role'] = $row['role'];
            $_SESSION['first_name'] = $row['first_name'];
            $_SESSION['last_name'] = $row['last_name'];
            
            // Refresh the token and extend its expiry (30 days from now)
            refresh_remember_token($user_id, $conn, 30);
            
            // Redirect to appropriate page
            switch ($row['role']) {
                case 'admin':
                    header("location: ../admin.php");
                    break;
                case 'sales_rep':
                    header("location: ../sales_rep_dashboard.php");
                    break;
                case 'manager':
                    header("location: ../admin.php");
                    break;
                default:
                    header("location: ../index.php");
            }
            exit;
        }
    } else {
        // Invalid or expired token, clear cookies
        clear_remember_cookie(null, $conn);
    }
}

// Initialize variables
$username = $password = "";
$username_err = $password_err = $login_err = "";

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Check if username is empty
    if (empty(trim($_POST["username"]))) {
        $username_err = "Please enter username.";
    } else {
        $username = trim($_POST["username"]);
    }
    
    // Check if password is empty
    if (empty(trim($_POST["password"]))) {
        $password_err = "Please enter your password.";
    } else {
        $password = trim($_POST["password"]);
    }
    
    // Validate credentials
    if (empty($username_err) && empty($password_err)) {
        // Prepare a select statement
        $sql = "SELECT id, username, password, role, first_name, last_name FROM employees WHERE username = ?";
        
        if ($stmt = mysqli_prepare($conn, $sql)) {
            // Bind variables to the prepared statement as parameters
            mysqli_stmt_bind_param($stmt, "s", $param_username);
            
            // Set parameters
            $param_username = $username;
            
            // Attempt to execute the prepared statement
            if (mysqli_stmt_execute($stmt)) {
                // Store result
                mysqli_stmt_store_result($stmt);
                
                // Check if username exists, if yes then verify password
                if (mysqli_stmt_num_rows($stmt) == 1) {                    
                    // Bind result variables
                    mysqli_stmt_bind_result($stmt, $id, $username, $hashed_password, $role, $first_name, $last_name);
                    if (mysqli_stmt_fetch($stmt)) {
                        if (password_verify($password, $hashed_password)) {
                            // Password is correct, start a new session
                            ensure_session_started();
                            
                            // Store data in session variables
                            $_SESSION["user_id"] = $id;
                            $_SESSION["username"] = $username;
                            $_SESSION["role"] = $role;
                            $_SESSION["first_name"] = $first_name;
                            $_SESSION["last_name"] = $last_name;
                            
                            // Handle "Remember Me" functionality
                            if (isset($_POST["remember-me"])) {
                                // Generate a secure token
                                $remember_token = generate_remember_token();
                                
                                // Set expiry days
                                $expiry_days = 30; // Default to 30 days
                                
                                // Set cookies and update database in one function call
                                if (set_remember_cookie($id, $remember_token, $expiry_days, $conn)) {
                                    // Cookie set successfully
                                    // No additional action needed as set_remember_cookie handles both
                                    // cookie setting and database update
                                }
                            } else {
                                // Clear any existing remember me cookies
                                clear_remember_cookie($id, $conn);
                            }
                            
                            // Redirect user to appropriate page based on role
                            switch ($role) {
                                case 'admin':
                                    header("location: ../admin.php");
                                    break;
                                case 'sales_rep':
                                    header("location: ../sales_rep_dashboard.php");
                                    break;
                                case 'manager':
                                    header("location: ../admin.php");
                                    break;
                                default:
                                    header("location: ../index.php");
                            }
                        } else {
                            // Password is not valid
                            $login_err = "Invalid username or password.";
                        }
                    }
                } else {
                    // Username doesn't exist
                    $login_err = "Invalid username or password.";
                }
            } else {
                $login_err = "Oops! Something went wrong. Please try again later.";
            }

            // Close statement
            mysqli_stmt_close($stmt);
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LMS - Login</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: { primary: "#4A77A8", secondary: "#F7A041" },
            borderRadius: {
              none: "0px",
              sm: "4px",
              DEFAULT: "8px",
              md: "12px",
              lg: "16px",
              xl: "20px",
              "2xl": "24px",
              "3xl": "32px",
              full: "9999px",
              button: "8px",
            },
          },
        },
      };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Open+Sans:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <link href="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.css" rel="stylesheet">
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <style>
      :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
          font-family: 'Inter', sans-serif;
          background-color: #fafafa;
      }
      input:focus {
          outline: none;
      }
      input:-webkit-autofill,
      input:-webkit-autofill:hover,
      input:-webkit-autofill:focus {
          -webkit-box-shadow: 0 0 0px 1000px rgb(201, 180, 180) inset;
          transition: background-color 5000s ease-in-out 0s;
      }
      input[type="password"] {
          letter-spacing: 0.2em;
      }
      .login-container {
          background-size: cover;
          background-position: center;
      }
      .illustration {
          background-image: url('../img/login.jpg');
          background-size: cover;
          background-position: center;
          border-radius: 0 0 0 120px;
      }
    </style>
</head>
<body class="bg-white text-black font-sans">
    <!-- Back to Home Button (Right-aligned) -->
    <div class="flex justify-end px-4 sm:px-6 md:px-8 pt-3 pb-0">
        <a href="/LEADManagement/LMS/index.html" class="inline-flex items-center justify-center bg-[#1E3E62] text-white py-2 px-4 sm:px-6 md:px-8 text-sm sm:text-base md:text-lg rounded-sm font-medium hover:bg-[#0B192C] transition-colors duration-200 shadow-md">
            <i class="ri-arrow-left-line mr-2"></i> Back to Home
        </a>
    </div>
    
    <div class="w-full flex items-center justify-center p-4 login-container" data-aos="fade-right" data-aos-duration="1000">
        <div class="w-full max-w-5xl bg-white rounded-xl shadow-2xl overflow-hidden flex flex-col md:flex-row px-0 border border-gray-200">
            <!-- Left side - Login form -->
            <div class="w-full md:w-1/2 p-4 md:p-6 flex flex-col justify-center">
                <div class="mb-3">
                    <div class="w-3 h-3 bg-primary rounded-full mb-6"></div>
                    <h2 class="text-[#1E3E62]-400 text-2xl font-bold mb-1">Welcome to</h2>
                    <h1 class="text-4xl font-bold mb-8"> 
                        <span class="text-">Lead</span>
                        <span class="text-[#FF6500]">Management</span></br>
                        System
                    </h1>
                </div>

                <?php if (!empty($login_err)) : ?>
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                        <span class="block sm:inline"><?php echo $login_err; ?></span>
                    </div>
                <?php endif; ?>

                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post" class="space-y-6">
                    <div class="space-y-2">
                        <label for="username" class="block text-sm text-[#1E3E62]-500 font-medium">Username <span class="text-red-500">*</span></label>
                        <input 
                            type="text" 
                            name="username" 
                            id="username" 
                            class="w-full px-3 py-3 border-b border-gray-200 text-gray-700 focus:border-primary transition-colors <?php echo (!empty($username_err)) ? 'border-red-500' : ''; ?>" 
                            placeholder="Enter your username"
                            value="<?php echo $username; ?>"
                            required
                        >
                        <span class="text-red-500 text-xs"><?php echo $username_err; ?></span>
                    </div>

                    <div class="space-y-2">
                        <label for="password" class="block text-sm text-[#1E3E62]-500 font-medium">Password <span class="text-red-500">*</span></label>
                        <input 
                            type="password" 
                            name="password" 
                            id="password" 
                            class="w-full px-3 py-3 border-b border-gray-200 text-gray-700 focus:border-primary transition-colors <?php echo (!empty($password_err)) ? 'border-red-500' : ''; ?>" 
                            placeholder="••••••••"
                            required
                        >
                        <span class="text-red-500 text-xs"><?php echo $password_err; ?></span>
                    </div>

                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <input id="remember-me" name="remember-me" type="checkbox" class="h-4 w-4 text-[#FF6500] focus:ring-[#FF6500] border-gray-300 rounded">
                            <label for="remember-me" class="ml-2 block text-sm text-gray-700">Remember me</label>
                        </div>
                        <a href="forgot_password.php" class="text-sm text-[#FF6500] hover:underline">Forgot password?</a>
                    </div>

                    <button type="submit" class="w-full md:w-32 bg-[#0b192c] text-white py-3 px-6 rounded-sm font-medium hover:bg-[#1e3e62] transition-colors whitespace-nowrap">
                        LOGIN
                    </button>
                </form>
            </div>

            <!-- Right side - Illustration -->
            <div class="w-full md:w-1/2 h-64 md:h-auto illustration"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js"></script>
    <script>
        AOS.init();
    </script>
</body>
</html>
