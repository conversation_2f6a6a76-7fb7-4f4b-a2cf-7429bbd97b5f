# LEAD Management System

A comprehensive CRM system for managing leads, sales, customers, and products.

## System Requirements

- PHP 7.4 or higher
- MySQL 5.7 or higher
- Apache web server
- mysqli PHP extension

## Installation

1. Clone or download this repository to your web server's document root (e.g., `htdocs` for XAMPP).
2. Make sure your web server (Apache) and MySQL are running.
3. Navigate to `http://localhost/LEADManagement/auth/login.php` in your browser to initialize the database.
4. After setup, you'll be redirected to the login page.

## Default Login Credentials

- **Username:** admin
- **Password:** Admin@123

## Features

- **Lead Management:** Track and manage leads from various sources
- **Sales Management:** Record sales and generate invoices/receipts
- **Customer Management:** Manage customer profiles and nominee information
- **Product Management:** Manage product catalog and track sales performance
- **User Management:** Add, edit, and delete system users with different roles

## User Roles

- **Admin:** Full access to all system features
- **Manager:** Access to dashboard and management features
- **Sales Rep:** Access to leads, sales, customers, and products

## Directory Structure

- `/auth`: Authentication-related files (login, logout, register)
- `/config`: Database configuration and initialization
- `/includes`: Common functions and utilities
- `/api`: API endpoints for AJAX requests
- `/img`: Images and icons

## Troubleshooting

If you encounter any issues:

1. Make sure your web server and MySQL are running.
2. Check that the mysqli PHP extension is enabled.
3. Ensure the database connection details in `config/database.php` are correct.
4. Check the Apache error logs for any PHP errors.

## License

This project is licensed under the MIT License - see the LICENSE file for details.