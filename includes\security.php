<?php
/**
 * Security functions for the application
 * 
 * This file contains functions to help with security, including
 * CSRF protection, input validation, and output encoding.
 */

/**
 * Generate a CSRF token
 * 
 * @return string CSRF token
 */
function generate_csrf_token() {
    // Start session if not already started
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    // Generate a new token if one doesn't exist
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 * 
 * @param string $token Token to verify
 * @return bool True if token is valid, false otherwise
 */
function verify_csrf_token($token) {
    // Start session if not already started
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    // Check if token exists and matches
    if (isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token)) {
        return true;
    }
    
    return false;
}

/**
 * Output CSRF token field for forms
 * 
 * @return string HTML input field with CSRF token
 */
function csrf_token_field() {
    $token = generate_csrf_token();
    return '<input type="hidden" name="csrf_token" value="' . $token . '">';
}

/**
 * Validate CSRF token from POST request
 * 
 * @return bool True if token is valid, false otherwise
 */
function validate_csrf_token() {
    if (isset($_POST['csrf_token'])) {
        return verify_csrf_token($_POST['csrf_token']);
    }
    
    return false;
}

/**
 * Set secure headers for the application
 */
function set_secure_headers() {
    // Content Security Policy
    header("Content-Security-Policy: default-src 'self'; script-src 'self' https://cdn.tailwindcss.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com 'unsafe-inline'; style-src 'self' https://fonts.googleapis.com https://cdn.jsdelivr.net https://cdnjs.cloudflare.com 'unsafe-inline'; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data:; connect-src 'self'");
    
    // X-Content-Type-Options
    header("X-Content-Type-Options: nosniff");
    
    // X-Frame-Options
    header("X-Frame-Options: SAMEORIGIN");
    
    // X-XSS-Protection
    header("X-XSS-Protection: 1; mode=block");
    
    // Referrer-Policy
    header("Referrer-Policy: strict-origin-when-cross-origin");
    
    // Strict-Transport-Security (only in production)
    if ($_SERVER['SERVER_NAME'] !== 'localhost' && 
        $_SERVER['SERVER_NAME'] !== '127.0.0.1' && 
        !strpos($_SERVER['SERVER_NAME'], '.local')) {
        header("Strict-Transport-Security: max-age=31536000; includeSubDomains; preload");
    }
}

/**
 * Sanitize and validate input based on type
 * 
 * @param mixed $input Input to sanitize and validate
 * @param string $type Type of input (email, url, int, float, etc.)
 * @param array $options Additional options for validation
 * @return array Array with sanitized value and validation status
 */
function sanitize_and_validate_input($input, $type = 'text', $options = []) {
    $result = [
        'value' => $input,
        'valid' => true,
        'error' => ''
    ];
    
    // Sanitize input
    $input = trim($input);
    
    // Validate based on type
    switch ($type) {
        case 'email':
            $input = filter_var($input, FILTER_SANITIZE_EMAIL);
            if (!filter_var($input, FILTER_VALIDATE_EMAIL)) {
                $result['valid'] = false;
                $result['error'] = 'Invalid email address';
            }
            break;
            
        case 'url':
            $input = filter_var($input, FILTER_SANITIZE_URL);
            if (!filter_var($input, FILTER_VALIDATE_URL)) {
                $result['valid'] = false;
                $result['error'] = 'Invalid URL';
            }
            break;
            
        case 'int':
            if (filter_var($input, FILTER_VALIDATE_INT) === false) {
                $result['valid'] = false;
                $result['error'] = 'Invalid integer';
            } else {
                $input = (int) $input;
                
                // Check min/max if provided
                if (isset($options['min']) && $input < $options['min']) {
                    $result['valid'] = false;
                    $result['error'] = 'Value must be at least ' . $options['min'];
                }
                
                if (isset($options['max']) && $input > $options['max']) {
                    $result['valid'] = false;
                    $result['error'] = 'Value must be at most ' . $options['max'];
                }
            }
            break;
            
        case 'float':
            if (filter_var($input, FILTER_VALIDATE_FLOAT) === false) {
                $result['valid'] = false;
                $result['error'] = 'Invalid number';
            } else {
                $input = (float) $input;
                
                // Check min/max if provided
                if (isset($options['min']) && $input < $options['min']) {
                    $result['valid'] = false;
                    $result['error'] = 'Value must be at least ' . $options['min'];
                }
                
                if (isset($options['max']) && $input > $options['max']) {
                    $result['valid'] = false;
                    $result['error'] = 'Value must be at most ' . $options['max'];
                }
            }
            break;
            
        case 'date':
            $date = date_create($input);
            if (!$date) {
                $result['valid'] = false;
                $result['error'] = 'Invalid date';
            } else {
                $input = date_format($date, 'Y-m-d');
            }
            break;
            
        case 'text':
        default:
            // Remove potentially dangerous characters
            $input = htmlspecialchars($input, ENT_QUOTES | ENT_HTML5, 'UTF-8');
            
            // Check length if provided
            if (isset($options['min_length']) && strlen($input) < $options['min_length']) {
                $result['valid'] = false;
                $result['error'] = 'Text must be at least ' . $options['min_length'] . ' characters';
            }
            
            if (isset($options['max_length']) && strlen($input) > $options['max_length']) {
                $result['valid'] = false;
                $result['error'] = 'Text must be at most ' . $options['max_length'] . ' characters';
            }
            break;
    }
    
    $result['value'] = $input;
    return $result;
}

/**
 * Check if a request is an AJAX request
 * 
 * @return bool True if request is AJAX, false otherwise
 */
function is_ajax_request() {
    return (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest');
}

/**
 * Log security events
 * 
 * @param string $event Event description
 * @param string $level Log level (info, warning, error)
 * @param array $data Additional data to log
 */
function log_security_event($event, $level = 'info', $data = []) {
    $log_file = __DIR__ . '/../logs/security.log';
    $log_dir = dirname($log_file);
    
    // Create log directory if it doesn't exist
    if (!file_exists($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    // Format log entry
    $log_entry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'level' => $level,
        'event' => $event,
        'ip' => $_SERVER['REMOTE_ADDR'],
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
        'user_id' => $_SESSION['user_id'] ?? 'Not logged in',
        'data' => $data
    ];
    
    // Write to log file
    file_put_contents(
        $log_file, 
        json_encode($log_entry) . PHP_EOL, 
        FILE_APPEND
    );
}