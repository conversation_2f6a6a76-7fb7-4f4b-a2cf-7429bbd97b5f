<?php
// Include database configuration
$conn = require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if the user is logged in and has admin or manager role
ensure_session_started();
require_any_role(["admin", "manager"]);

$success_message = '';
$error_message = '';
$import_results = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['contact_csv'])) {
    $file = $_FILES['contact_csv'];

    // Check if confirmation checkbox was checked
    if (!isset($_POST['confirm_import']) || $_POST['confirm_import'] !== 'on') {
        $error_message = "Please confirm that you have verified the data before uploading.";
    }
    // Check for upload errors
    else if ($file['error'] !== UPLOAD_ERR_OK) {
        $error_message = "File upload error: " . $file['error'];
    } else {
        // Validate file type
        $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if ($file_extension !== 'csv') {
            $error_message = "Please upload a CSV file only.";
        } else {
            // Process the CSV file
            $csv_file = fopen($file['tmp_name'], 'r');
            
            if ($csv_file === false) {
                $error_message = "Could not open the CSV file.";
            } else {
                // Read the header row
                $header = fgetcsv($csv_file);
                
                if ($header === false) {
                    $error_message = "Could not read the CSV header.";
                } else {
                    // Expected columns
                    $required_columns = ['first_name', 'last_name', 'email', 'phone', 'source'];
                    $optional_columns = ['country_code', 'address', 'customer_interest', 'category', 'assigned_to'];
                    
                    // Normalize header (trim and lowercase)
                    $header = array_map(function($col) {
                        return strtolower(trim($col));
                    }, $header);
                    
                    // Check if all required columns are present
                    $missing_columns = array_diff($required_columns, $header);

                    if (!empty($missing_columns)) {
                        $error_message = "Missing required columns: " . implode(', ', $missing_columns) .
                                       ". Found columns: " . implode(', ', $header);
                    } else {
                        // Create column mapping
                        $column_map = array_flip($header);
                        
                        $imported_count = 0;
                        $skipped_count = 0;
                        $error_count = 0;
                        $errors = [];
                        $row_number = 1; // Start from 1 (header is row 0)
                        
                        // Process each data row
                        while (($data = fgetcsv($csv_file)) !== false) {
                            $row_number++;

                            // Skip empty rows
                            if (empty(array_filter($data))) {
                                $skipped_count++;
                                continue;
                            }

                            // Ensure data array has enough elements to match header
                            if (count($data) < count($header)) {
                                $data = array_pad($data, count($header), '');
                            }
                            
                            // Extract data using column mapping with safe access
                            $first_name = isset($column_map['first_name']) && isset($data[$column_map['first_name']]) ? trim($data[$column_map['first_name']]) : '';
                            $last_name = isset($column_map['last_name']) && isset($data[$column_map['last_name']]) ? trim($data[$column_map['last_name']]) : '';
                            $email = isset($column_map['email']) && isset($data[$column_map['email']]) ? trim($data[$column_map['email']]) : '';
                            $phone = isset($column_map['phone']) && isset($data[$column_map['phone']]) ? trim($data[$column_map['phone']]) : '';
                            $source = isset($column_map['source']) && isset($data[$column_map['source']]) ? trim($data[$column_map['source']]) : '';

                            // Handle optional columns safely
                            $country_code = isset($column_map['country_code']) && isset($data[$column_map['country_code']]) ? trim($data[$column_map['country_code']]) : '+91';
                            $address = isset($column_map['address']) && isset($data[$column_map['address']]) ? trim($data[$column_map['address']]) : '';
                            $customer_interest = isset($column_map['customer_interest']) && isset($data[$column_map['customer_interest']]) ? trim($data[$column_map['customer_interest']]) : '';

                            $category = isset($column_map['category']) && isset($data[$column_map['category']]) ? trim($data[$column_map['category']]) : '';
                            $assigned_to = isset($column_map['assigned_to']) && isset($data[$column_map['assigned_to']]) ? trim($data[$column_map['assigned_to']]) : '';
                            
                            // Validate required fields
                            if (empty($first_name) || empty($last_name) || empty($email)) {
                                $errors[] = "Row $row_number: Missing required fields (first_name, last_name, or email)";
                                $error_count++;
                                continue;
                            }
                            
                            // Validate email format
                            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                                $errors[] = "Row $row_number: Invalid email format ($email)";
                                $error_count++;
                                continue;
                            }
                            
                            // Check if email already exists
                            $check_email_sql = "SELECT id FROM contacts WHERE email = ?";
                            $check_stmt = mysqli_prepare($conn, $check_email_sql);
                            mysqli_stmt_bind_param($check_stmt, "s", $email);
                            mysqli_stmt_execute($check_stmt);
                            $check_result = mysqli_stmt_get_result($check_stmt);
                            
                            if (mysqli_num_rows($check_result) > 0) {
                                $errors[] = "Row $row_number: Email already exists ($email)";
                                $error_count++;
                                continue;
                            }
                            
                            // Status field is no longer used - removed from system
                            
                            // Handle assigned_to (can be user ID or username)
                            $assigned_to_id = null;
                            if (!empty($assigned_to)) {
                                if (is_numeric($assigned_to)) {
                                    // It's a user ID
                                    $assigned_to_id = intval($assigned_to);
                                } else {
                                    // It's a username, look up the ID
                                    $user_sql = "SELECT id FROM employees WHERE username = ?";
                                    $user_stmt = mysqli_prepare($conn, $user_sql);
                                    mysqli_stmt_bind_param($user_stmt, "s", $assigned_to);
                                    mysqli_stmt_execute($user_stmt);
                                    $user_result = mysqli_stmt_get_result($user_stmt);
                                    
                                    if ($user_row = mysqli_fetch_assoc($user_result)) {
                                        $assigned_to_id = $user_row['id'];
                                    }
                                }
                            }
                            
                            // Insert the contact
                            $insert_sql = "INSERT INTO contacts (first_name, last_name, email, phone, country_code, source, address, customer_interest, category, assigned_to, created_by)
                                          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                            $insert_stmt = mysqli_prepare($conn, $insert_sql);
                            $created_by = $_SESSION['user_id'];

                            mysqli_stmt_bind_param($insert_stmt, "sssssssssii",
                                $first_name, $last_name, $email, $phone, $country_code,
                                $source, $address, $customer_interest, $category,
                                $assigned_to_id, $created_by);
                            
                            if (mysqli_stmt_execute($insert_stmt)) {
                                $imported_count++;
                            } else {
                                $errors[] = "Row $row_number: Database error - " . mysqli_error($conn);
                                $error_count++;
                            }
                        }
                        
                        fclose($csv_file);
                        
                        // Prepare results
                        $import_results = [
                            'imported' => $imported_count,
                            'skipped' => $skipped_count,
                            'errors' => $error_count,
                            'error_details' => $errors
                        ];
                        
                        if ($imported_count > 0) {
                            $success_message = "Successfully imported $imported_count contact(s).";
                            if ($skipped_count > 0) {
                                $success_message .= " Skipped $skipped_count empty row(s).";
                            }
                            if ($error_count > 0) {
                                $success_message .= " $error_count row(s) had errors.";
                            }
                        } else {
                            $error_message = "No contacts were imported. Please check your CSV file.";
                        }
                    }
                }
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="img/minilogo.jpg" type="image/x-icon">
    <title>Import Contacts - CRM System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .text-color { color: #FF6500; }
        .bg-color { background-color: #FF6500; }
    </style>
</head>
<body class="bg-gray-100">

<?php include 'includes/navigation.php'; ?>

<!-- Main Content -->
<div class="ml-0 min-[870px]:ml-60 min-h-screen flex flex-col">
    <header class="bg-[#1E3E62] p-4 flex justify-between z-10 flex-col">
        <h1 class="text-2xl font-bold text-white mx-10 my-2">Contact Import Results</h1>
            </header>
    
    <main class="flex-1 p-10">
        <!-- Back Button -->
        <div class="mb-6">
            <a href="admin.php" class="text-blue-600 hover:text-blue-800">
                <i class="fas fa-arrow-left mr-2"></i>Back to Admin Dashboard
            </a>
        </div>

        <!-- Results -->
        <div class="max-w-4xl mx-auto">
            <?php if (!empty($success_message)): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <?php echo $success_message; ?>
                </div>
            </div>
            <?php endif; ?>

            <?php if (!empty($error_message)): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    <?php echo $error_message; ?>
                </div>
            </div>
            <?php endif; ?>

            <?php if (!empty($import_results)): ?>
            <!-- Import Summary -->
            <div class="bg-white rounded-lg shadow p-6 mb-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Import Summary</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="bg-green-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-check text-green-600 text-2xl mr-3"></i>
                            <div>
                                <p class="text-sm text-green-600">Successfully Imported</p>
                                <p class="text-2xl font-bold text-green-800"><?php echo $import_results['imported']; ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-yellow-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-triangle text-yellow-600 text-2xl mr-3"></i>
                            <div>
                                <p class="text-sm text-yellow-600">Skipped/Empty Rows</p>
                                <p class="text-2xl font-bold text-yellow-800"><?php echo $import_results['skipped']; ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-red-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-times text-red-600 text-2xl mr-3"></i>
                            <div>
                                <p class="text-sm text-red-600">Errors</p>
                                <p class="text-2xl font-bold text-red-800"><?php echo $import_results['errors']; ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <?php if (!empty($import_results['error_details'])): ?>
            <!-- Error Details -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Error Details</h2>
                <div class="bg-red-50 p-4 rounded-lg max-h-64 overflow-y-auto">
                    <ul class="text-sm text-red-700 space-y-1">
                        <?php foreach ($import_results['error_details'] as $error): ?>
                        <li><i class="fas fa-exclamation-circle mr-2"></i><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </div>
            <?php endif; ?>
            <?php endif; ?>

            <!-- Actions -->
            <div class="mt-6 flex space-x-4">
                <a href="contacts.php" class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    <i class="fas fa-users mr-2"></i>View Contacts
                </a>
                <a href="admin.php" class="px-6 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Admin
                </a>
            </div>
        </div>
    </main>
</div>

</body>
</html>
