<?php

$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';

// Check if database is set up first
require_once 'check_database_setup.php';
redirect_to_setup_if_needed();

// Check if the user is logged in
ensure_session_started();
require_login();

$is_sales_rep = isset($_SESSION['role']) && $_SESSION['role'] === 'sales_rep';
$sales_rep_id = $is_sales_rep ? $_SESSION['user_id'] : 0;

// Get filter parameters
$filter = isset($_GET['filter']) ? $_GET['filter'] : '';
$search = isset($_GET['search']) ? $_GET['search'] : '';

// Pagination parameters
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$records_per_page = 10;
$offset = ($page - 1) * $records_per_page;

// Build the base SQL query
$where_conditions = ["contacts.status LIKE 'lead_%'"]; // Only show leads, not customers
$params = [];
$param_types = '';

if ($is_sales_rep) {
    $where_conditions[] = "contacts.assigned_to = ?";
    $params[] = $sales_rep_id;
    $param_types .= 'i';
}

if (!empty($filter)) {
    // Handle different filter types
    switch ($filter) {
        case 'new':
            $where_conditions[] = "contacts.status = 'lead_new'";
            break;
        case 'contacted':
            $where_conditions[] = "contacts.status = 'lead_contacted'";
            break;
        case 'qualified':
            $where_conditions[] = "contacts.status = 'lead_qualified'";
            break;
        case 'lost':
            $where_conditions[] = "contacts.status = 'lead_lost'";
            break;
        case 'assigned':
            $where_conditions[] = "contacts.assigned_to IS NOT NULL";
            break;
        case 'unassigned':
            $where_conditions[] = "contacts.assigned_to IS NULL";
            break;
    }
}

if (!empty($search)) {
    $where_conditions[] = "(contacts.first_name LIKE ? OR contacts.last_name LIKE ? OR contacts.email LIKE ? OR contacts.phone LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $param_types .= 'ssss';
}

$where_clause = !empty($where_conditions) ? " WHERE " . implode(" AND ", $where_conditions) : "";

// Get total count for pagination
$count_sql = "SELECT COUNT(*) as total FROM contacts" . $where_clause;

// Debug output
echo "<h1>Debug Information</h1>";
echo "<h2>Count SQL Query:</h2>";
echo "<pre>" . htmlspecialchars($count_sql) . "</pre>";

echo "<h2>Parameters:</h2>";
echo "<pre>";
print_r($params);
echo "</pre>";

echo "<h2>Parameter Types:</h2>";
echo "<pre>" . htmlspecialchars($param_types) . "</pre>";

// Try running a simple query to check if the database connection is working
$test_sql = "SHOW TABLES";
$test_result = mysqli_query($conn, $test_sql);
echo "<h2>Database Tables:</h2>";
echo "<pre>";
while ($row = mysqli_fetch_row($test_result)) {
    echo htmlspecialchars($row[0]) . "\n";
}
echo "</pre>";

// Try running a simple query on the contacts table
$test_sql = "DESCRIBE contacts";
$test_result = mysqli_query($conn, $test_sql);
echo "<h2>Contacts Table Structure:</h2>";
echo "<pre>";
while ($row = mysqli_fetch_assoc($test_result)) {
    echo htmlspecialchars($row['Field']) . " - " . htmlspecialchars($row['Type']) . "\n";
}
echo "</pre>";

// Try running a simple count query
$test_sql = "SELECT COUNT(*) as total FROM contacts";
$test_result = mysqli_query($conn, $test_sql);
$test_count = mysqli_fetch_assoc($test_result);
echo "<h2>Total Contacts:</h2>";
echo "<pre>" . $test_count['total'] . "</pre>";

// Try running a simple query with a WHERE clause
$test_sql = "SELECT COUNT(*) as total FROM contacts WHERE contacts.status LIKE 'lead_%'";
echo "<h2>Test Query:</h2>";
echo "<pre>" . htmlspecialchars($test_sql) . "</pre>";

try {
    $test_result = mysqli_query($conn, $test_sql);
    if ($test_result) {
        $test_count = mysqli_fetch_assoc($test_result);
        echo "<h2>Total Leads:</h2>";
        echo "<pre>" . $test_count['total'] . "</pre>";
    } else {
        echo "<h2>Error:</h2>";
        echo "<pre>" . mysqli_error($conn) . "</pre>";
    }
} catch (Exception $e) {
    echo "<h2>Exception:</h2>";
    echo "<pre>" . $e->getMessage() . "</pre>";
}

// Exit to prevent the rest of the page from loading
exit;