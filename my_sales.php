<?php
// Get database connection using reliable method
$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';

// Check if database is set up first
require_once 'check_database_setup.php';
redirect_to_setup_if_needed();

// Check if the user is logged in
ensure_session_started();
require_login();

// Check if user is a sales rep
if (!has_role("sales_rep")) {
    header("Location: admin.php");
    exit;
}

// Get the sales rep ID
$sales_rep_id = $_SESSION['user_id'];

// Get sales for this sales rep with updated table structure
$sales_query = "SELECT s.*, CONCAT(c.first_name, ' ', c.last_name) as customer_name, p.name as product_name
                FROM sales s
                LEFT JOIN contacts c ON s.contact_id = c.id
                LEFT JOIN products p ON s.product_id = p.id
                WHERE s.sales_rep_id = ?
                ORDER BY s.sale_date DESC";
$sales_stmt = mysqli_prepare($conn, $sales_query);
mysqli_stmt_bind_param($sales_stmt, "i", $sales_rep_id);
mysqli_stmt_execute($sales_stmt);
$sales_result = mysqli_stmt_get_result($sales_stmt);

// Get sales statistics
$total_sales_query = "SELECT COUNT(*) as count, SUM(total_amount) as total FROM sales WHERE sales_rep_id = ?";
$total_sales_stmt = mysqli_prepare($conn, $total_sales_query);
mysqli_stmt_bind_param($total_sales_stmt, "i", $sales_rep_id);
mysqli_stmt_execute($total_sales_stmt);
$total_sales_result = mysqli_stmt_get_result($total_sales_stmt);
$total_sales_row = mysqli_fetch_assoc($total_sales_result);
$total_sales_count = $total_sales_row['count'] ? $total_sales_row['count'] : 0;
$total_sales_amount = $total_sales_row['total'] ? $total_sales_row['total'] : 0;

// Get average sale amount
$avg_sale_amount = $total_sales_count > 0 ? $total_sales_amount / $total_sales_count : 0;

// Get sales by month for the current year
$monthly_sales_query = "SELECT MONTH(sale_date) as month, SUM(total_amount) as total
                        FROM sales
                        WHERE sales_rep_id = ? AND YEAR(sale_date) = YEAR(CURRENT_DATE) 
                        GROUP BY MONTH(sale_date) 
                        ORDER BY MONTH(sale_date)";
$monthly_sales_stmt = mysqli_prepare($conn, $monthly_sales_query);
mysqli_stmt_bind_param($monthly_sales_stmt, "i", $sales_rep_id);
mysqli_stmt_execute($monthly_sales_stmt);
$monthly_sales_result = mysqli_stmt_get_result($monthly_sales_stmt);

// Prepare data for chart
$months = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
$monthly_data = array_fill(0, 12, 0);

while ($row = mysqli_fetch_assoc($monthly_sales_result)) {
    $month_index = intval($row['month']) - 1;
    $monthly_data[$month_index] = floatval($row['total']);
}

$chart_labels = json_encode($months);
$chart_data = json_encode($monthly_data);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="img/minilogo.jpg" type="image/x-icon">
    <title>My Sales - CRM System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .text-color{
        color: #FF6500;
        }
        .input-focus {
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        .input-focus:focus {
            border-color: #FF6500;
            box-shadow: 0 0 0 3px rgba(255, 101, 0, 0.1);
        }

        .button-hover {
            transition: background-color 0.3s ease, transform 0.2s ease;
        }

        .button-hover:hover {
            background-color: #e55b00;
            transform: scale(1.05);
        }
    </style>
</head>
<body class="mb-20">
<?php include 'includes/navigation.php'; ?>

    <!-- Main Content -->
    <div class="ml-0 min-[870px]:ml-60 min-h-screen flex flex-col">
        <!-- Navbar -->
        <header class="bg-[#1E3E62] p-4 flex justify-between z-10 flex-col">
            <h1 class="text-2xl font-bold text-white mx-10 my-2">My Sales</h1>
                    </header>
        <main class="flex-1 overflow-y-auto space-y-10">
            <!-- Success message -->
            <?php if (isset($_GET['success']) && $_GET['success'] == 1) : ?>
                <div class="mx-10 mt-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                    <strong class="font-bold">Success!</strong>
                    <span class="block sm:inline"><?php echo htmlspecialchars($_GET['message'] ?? 'Operation completed successfully.'); ?></span>
                    <button class="absolute top-0 bottom-0 right-0 px-4 py-3" onclick="this.parentElement.style.display='none'">
                        <svg class="fill-current h-6 w-6 text-green-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
                    </button>
                </div>
            <?php endif; ?>

            <!-- Error message -->
            <?php if (isset($_GET['error']) && $_GET['error'] == 1) : ?>
                <div class="mx-10 mt-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                    <strong class="font-bold">Error!</strong>
                    <span class="block sm:inline"><?php echo htmlspecialchars($_GET['message'] ?? 'An error occurred.'); ?></span>
                    <button class="absolute top-0 bottom-0 right-0 px-4 py-3" onclick="this.parentElement.style.display='none'">
                        <svg class="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
                    </button>
                </div>
            <?php endif; ?>
            
            <!-- Analytics Cards -->
            <section class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-10 px-10" style="background: linear-gradient(to bottom, #1E3E62 60%, white 50%);">
                <div class="bg-white p-4 rounded-2xl shadow-xl h-[150px]">
                    <img src="img/totalsales.png" alt="" class="w-10">
                    <p class="text-sm text-gray-500 ">Total Sales</p>
                    <h2 class="text-xl font-bold mt-2 text-color"><?php echo $total_sales_count; ?></h2>
                </div>
                <div class="bg-white p-4 rounded-2xl shadow-xl h-[150px]">
                    <img src="img/revenue.png" alt="" class="w-10">
                    <p class="text-sm text-gray-500">Total Revenue</p>
                    <h2 class="text-xl font-bold mt-2 text-color"><?php echo format_currency($total_sales_amount); ?></h2>
                </div>
                <div class="bg-white p-4 rounded-2xl shadow-xl h-[150px]">
                    <img src="img/referrals.png" alt="" class="w-10">
                    <p class="text-sm text-gray-500">Average Sale</p>
                    <h2 class="text-xl font-bold mt-2 text-color"><?php echo format_currency($avg_sale_amount); ?></h2>
                </div>
            </section>

            <!-- Monthly Sales Chart -->
            <section class="px-10">
                <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                    <div class="px-6 py-5 border-b border-gray-200 sm:px-8" style="background: linear-gradient(to right, #1E3E62, #0B192C);">
                        <h3 class="text-lg leading-6 font-medium text-white">
                            Monthly Sales Performance
                        </h3>
                        <p class="mt-1 max-w-2xl text-sm text-white">
                            Your sales performance for the current year
                        </p>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <canvas id="monthlySalesChart" height="100"></canvas>
                    </div>
                </div>
            </section>

            <!-- Sales Table -->
            <section class="px-10 mb-10">
                <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                    <div class="px-6 py-5 border-b border-gray-200 sm:px-8" style="background: linear-gradient(to right, #1E3E62, #0B192C);">
                        <h3 class="text-lg leading-6 font-medium text-white">
                            Sales History
                        </h3>
                        <p class="mt-1 max-w-2xl text-sm text-white">
                            Complete list of your sales transactions
                        </p>
                    </div>
                    <div class="overflow-x-auto">
                        <?php if (mysqli_num_rows($sales_result) > 0) : ?>
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">S.No</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment Method</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <?php
                                    $serial_number = 1;
                                    while ($row = mysqli_fetch_assoc($sales_result)) :
                                    ?>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900"><?php echo $serial_number++; ?></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($row['customer_name']); ?></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900"><?php echo htmlspecialchars($row['product_name']); ?></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900"><?php echo format_currency($row['total_amount']); ?></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900"><?php echo date('d M, Y', strtotime($row['sale_date'])); ?></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                    <?php echo htmlspecialchars($row['payment_method']); ?>
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                <a href="view_sale.php?id=<?php echo $row['id']; ?>" class="text-blue-600 hover:text-blue-900 mr-3" title="View Sale">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="invoice.php?id=<?php echo $row['id']; ?>" class="text-green-600 hover:text-green-900 mr-3" title="Generate Invoice">
                                                    <i class="fas fa-file-invoice"></i>
                                                </a>
                                                <a href="delete_sale.php?id=<?php echo $row['id']; ?>" class="text-red-600 hover:text-red-900" title="Delete Sale" onclick="return confirm('Are you sure you want to delete this sale?')">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        <?php else : ?>
                            <div class="px-6 py-4">
                                <p class="text-gray-500">No sales recorded yet.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <script>
        // Monthly Sales Chart
        const ctx = document.getElementById('monthlySalesChart').getContext('2d');
        const monthlySalesChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: <?php echo $chart_labels; ?>,
                datasets: [{
                    label: 'Monthly Sales (₹)',
                    data: <?php echo $chart_data; ?>,
                    backgroundColor: 'rgba(255, 101, 0, 0.6)',
                    borderColor: 'rgba(255, 101, 0, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '₹' + value.toLocaleString();
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return '₹' + context.raw.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
