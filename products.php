<?php
// Include database configuration
$conn = require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/product_email_notifications.php';
require_once 'includes/notification_helper.php';

// Check if the user is logged in
ensure_session_started();
require_login();

// Process test email functionality
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['action']) && $_POST['action'] == 'test_email') {
    $test_product = [
        'id' => 999,
        'name' => 'Test Product - Email Verification',
        'price' => 1000,
        'category' => 'Test Category'
    ];

    // Send test email to admin only
    $admin_email = $_SESSION['email'] ?? SMTP_FROM_EMAIL;
    $test_recipient = [
        'first_name' => $_SESSION['first_name'] ?? 'Admin',
        'last_name' => $_SESSION['last_name'] ?? 'User',
        'email' => $admin_email,
        'customer_interest' => 'Test Interest'
    ];

    $email_result = send_product_email($test_recipient, $test_product, 'customer');

    if ($email_result['success']) {
        add_success_notification("Test Email Sent", "Test product notification email sent successfully to {$admin_email}");
    } else {
        add_error_notification("Test Email Failed", "Failed to send test email: " . $email_result['message']);
    }

    header("Location: products.php");
    exit;
}

// Process form submission for adding a new product
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['action']) && $_POST['action'] == 'add_product') {
    $name = sanitize_input($_POST['name']);
    $price = intval(sanitize_input($_POST['price']));
    $category = sanitize_input($_POST['category']);
    
    $sql = "INSERT INTO products (name, price, category) 
            VALUES (?, ?, ?)";
    
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, "sds", $name, $price, $category);
    
    if (mysqli_stmt_execute($stmt)) {
        $new_product_id = mysqli_insert_id($conn);

        // Get the newly created product details
        $product_details = [
            'id' => $new_product_id,
            'name' => $name,
            'price' => $price,
            'category' => $category
        ];

        // Check if email notifications are enabled (default: enabled)
        $send_emails = isset($_POST['send_notifications']) ? true : false;

        if ($send_emails) {
            // Send email notifications to all leads and customers
            $email_results = send_new_product_notifications($product_details, $conn);
        } else {
            $email_results = ['total_sent' => 0, 'failed' => 0];
            add_info_notification("Product Added", "Product '{$name}' has been added without sending email notifications.");
        }

        // Log the email results for debugging
        error_log("Product Email Notifications - Total Sent: {$email_results['total_sent']}, Failed: {$email_results['failed']}");

        // Add success notification
        add_success_notification(
            "Product Added Successfully",
            "Product '{$name}' has been added and notifications sent to {$email_results['total_sent']} recipients."
        );

        // Redirect to avoid form resubmission
        header("Location: products.php?success=1");
        exit;
    } else {
        $error_message = "Error: " . mysqli_error($conn);
        add_error_notification("Product Addition Failed", $error_message);
    }
}

// Get all products ordered by most recent first (assuming higher ID means more recent)
$sql = "SELECT * FROM products ORDER BY id DESC";
$products_result = mysqli_query($conn, $sql);

// Get product statistics
$total_products_query = "SELECT COUNT(*) as total FROM products";
$total_products_result = mysqli_query($conn, $total_products_query);
$total_products_row = mysqli_fetch_assoc($total_products_result);
$total_products = $total_products_row['total'];

$avg_price_query = "SELECT AVG(price) as avg_price FROM products";
$avg_price_result = mysqli_query($conn, $avg_price_query);
$avg_price_row = mysqli_fetch_assoc($avg_price_result);
$avg_price = $avg_price_row['avg_price'] ? $avg_price_row['avg_price'] : 0;

// Get total sales amount
$total_sales_query = "SELECT SUM(total_amount) as total_sales FROM sales";
$total_sales_result = mysqli_query($conn, $total_sales_query);
$total_sales_row = mysqli_fetch_assoc($total_sales_result);
$total_sales_amount = $total_sales_row['total_sales'] ? $total_sales_row['total_sales'] : 0;

// Get top selling products
$top_products_query = "SELECT p.name, COUNT(s.id) as sale_count, SUM(s.total_amount) as total_amount
                      FROM products p
                      JOIN sales s ON p.id = s.product_id
                      GROUP BY p.id
                      ORDER BY sale_count DESC
                      LIMIT 5";
$top_products_result = mysqli_query($conn, $top_products_query);

// Get monthly sales data for the last 6 months
$monthly_sales_query = "SELECT
                          DATE_FORMAT(sale_date, '%Y-%m') as month,
                          SUM(total_amount) as total_amount,
                          COUNT(*) as sale_count
                        FROM sales
                        WHERE sale_date >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
                        GROUP BY DATE_FORMAT(sale_date, '%Y-%m')
                        ORDER BY month ASC";
$monthly_sales_result = mysqli_query($conn, $monthly_sales_query);
$monthly_sales_data = [];
$monthly_sales_labels = [];
$monthly_sales_values = [];
$monthly_sales_counts = [];

if (mysqli_num_rows($monthly_sales_result) > 0) {
    while ($row = mysqli_fetch_assoc($monthly_sales_result)) {
        $monthly_sales_data[] = $row;
        $month_year = date('M Y', strtotime($row['month'] . '-01'));
        $monthly_sales_labels[] = $month_year;
        $monthly_sales_values[] = $row['total_amount'];
        $monthly_sales_counts[] = $row['sale_count'];
    }
}

// Get product category distribution
$category_query = "SELECT 
                    p.category, 
                    COUNT(*) as product_count,
                    SUM(CASE WHEN s.id IS NOT NULL THEN 1 ELSE 0 END) as sales_count
                  FROM products p
                  LEFT JOIN sales s ON p.id = s.product_id
                  GROUP BY p.category
                  ORDER BY product_count DESC";
$category_result = mysqli_query($conn, $category_query);
$category_data = [];
$category_labels = [];
$category_values = [];
$category_sales = [];

if (mysqli_num_rows($category_result) > 0) {
    while ($row = mysqli_fetch_assoc($category_result)) {
        $category_data[] = $row;
        $category_labels[] = $row['category'] ? $row['category'] : 'Uncategorized';
        $category_values[] = $row['product_count'];
        $category_sales[] = $row['sales_count'];
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="img/minilogo.jpg" type="image/x-icon">
    <title>Product Management - CRM System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .text-color{ color: #FF6500; }
        .input-focus {
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        .input-focus:focus {
            border-color: #FF6500;
            box-shadow: 0 0 0 3px rgba(255, 101, 0, 0.1);
        }

        .button-hover {
            transition: background-color 0.3s ease, transform 0.2s ease;
        }

        .button-hover:hover {
            background-color: #e55b00;
            transform: scale(1.05);
        }
    </style>
</head>
<body class="">
<!-- Sidebar -->
<?php include 'includes/navigation.php'; ?>

<!-- Main Content -->
    <div class="ml-0 min-[870px]:ml-60 min-h-screen flex flex-col">
        <!-- Navbar -->
        <header class="bg-[#1E3E62] p-4 flex justify-between items-center z-10">
            <h1 class="text-2xl font-bold text-white mx-10 my-2">Product Management</h1>
            <div class="mx-10">
                <?php include 'includes/top_notification.php'; ?>
            </div>
        </header>
        <main class="flex-1 overflow-y-auto space-y-10">
            <!-- Analytics Cards -->
            <section class="grid grid-cols-1 sm:grid-cols-3 lg:grid-cols-4 gap-6 mb-10 px-10" style="background: linear-gradient(to bottom, #1E3E62 60%, white 50%);">
                <div class="bg-white p-4 rounded-2xl shadow-xl h-[150px]">
                    <img src="img/totalsales.png" alt="" class="w-10">
                    <p class="text-sm text-gray-500 ">Total Products</p>
                    <h2 class="text-xl font-bold mt-2 text-color"><?php echo $total_products; ?></h2>
                </div>
                <div class="bg-white p-4 rounded-2xl shadow-xl h-[150px]">
                    <img src="img/revenue.png" alt="" class="w-10">
                    <p class="text-sm text-gray-500">Average Price</p>
                    <h2 class="text-xl font-bold mt-2 text-color"><?php echo '₹' . number_format($avg_price, 2); ?></h2>
                </div>
                <div class="bg-white p-4 rounded-2xl shadow-xl h-[150px]">
                    <img src="img/revenue.png" alt="" class="w-10">
                    <p class="text-sm text-gray-500">Total Sales Revenue</p>
                    <h2 class="text-xl font-bold mt-2 text-color"><?php echo format_currency($total_sales_amount); ?></h2>
                </div>
            </section>
            
            <!-- Success message -->
            <?php if (isset($_GET['success'])) : ?>
                <div class="mx-10 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                    <strong class="font-bold">Success!</strong>
                    <span class="block sm:inline">
                        <?php 
                        if (isset($_GET['message'])) {
                            echo htmlspecialchars($_GET['message']);
                        } else if ($_GET['success'] == 1) {
                            echo "Product added successfully.";
                        }
                        ?>
                    </span>
                    <button class="absolute top-0 bottom-0 right-0 px-4 py-3" onclick="this.parentElement.style.display='none'">
                        <svg class="fill-current h-6 w-6 text-green-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
                    </button>
                </div>
            <?php endif; ?>

            <!-- Error message -->
            <?php if (isset($error_message)) : ?>
                <div class="mx-10 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                    <strong class="font-bold">Error!</strong>
                    <span class="block sm:inline"><?php echo $error_message; ?></span>
                    <button class="absolute top-0 bottom-0 right-0 px-4 py-3" onclick="this.parentElement.style.display='none'">
                        <svg class="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
                    </button>
                </div>
            <?php endif; ?>

            <!-- Email Test Section -->
            <div class="mx-10 mb-6">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-sm font-medium text-blue-800">Email Notification System</h3>
                            <p class="text-xs text-blue-600 mt-1">Test the email notification system before adding products</p>
                        </div>
                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="action" value="test_email">
                            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white text-xs px-3 py-2 rounded-md transition-colors">
                                📧 Send Test Email
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Product Management Tab -->
            <div id="product-management" class="tab-content mx-10">
                <div class="grid grid-cols-1 gap-6 lg:grid-cols-2 mt-10">
                    <!-- Product Entry Form -->
                    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                        <div class="px-6 py-5 border-b border-gray-200 sm:px-8" style="background: linear-gradient(to right, #1E3E62, #0B192C);">
                            <h3 class="text-lg leading-6 font-medium text-white">
                                Add New Product
                            </h3>
                            <p class="mt-1 max-w-2xl text-sm text-white">
                                Add a new product to the system
                            </p>
                        </div>
                        <div class="px-4 py-5 sm:p-6">
                            <form id="product-form" class="space-y-4" method="POST" action="products.php">
                                <input type="hidden" name="action" value="add_product">
                                <div class="grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-2">
                                    <div class="sm:col-span-2">
                                        <label for="name" class="block text-sm font-medium text-gray-700">Product Name <span class="text-red-500">*</span></label>
                                        <input type="text" name="name" id="name" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md p-2 border input-focus" required>
                                    </div>
                                    <div>
                                        <label for="price" class="block text-sm font-medium text-gray-700">Price <span class="text-red-500">*</span></label>
                                        <input type="text" name="price" id="price" pattern="[1-9][0-9]{0,7}" title="Enter a valid price (whole numbers only, no decimals, maximum 8 digits)" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md p-2 border input-focus" required>
                                    </div>
                                    <div>
                                        <label for="category" class="block text-sm font-medium text-gray-700">Category <span class="text-red-500">*</span></label>
                                        <input type="text" name="category" id="category" class="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md p-2 border input-focus" required>
                                    </div>
                                </div>

                                <!-- Email Notification Option -->
                                <div class="pt-4 border-t border-gray-200">
                                    <div class="flex items-center">
                                        <input type="checkbox" id="send_notifications" name="send_notifications" checked
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <label for="send_notifications" class="ml-2 block text-sm text-gray-700">
                                            <span class="font-medium">Send email notifications</span>
                                            <span class="text-gray-500 block text-xs">Automatically notify all leads and customers about this new product</span>
                                        </label>
                                    </div>
                                </div>

                                <div class="pt-2">
                                    <button type="submit" class="inline-flex justify-center py-2 px-6 border border-transparent shadow-sm text-sm font-medium rounded-sm text-white bg-[#0b192c] hover:bg-[#1e3e62] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0b192c]">
                                        Add Product
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Recent Products -->
                    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                        <div class="px-6 py-5 border-b border-gray-200 sm:px-8" style="background: linear-gradient(to right, #1E3E62, #0B192C);">
                            <h3 class="text-lg leading-6 font-medium text-white">
                                Recent Products
                            </h3>
                            <p class="mt-1 max-w-2xl text-sm text-white">
                                Most recently added products!
                            </p>
                        </div>
                        <div class="px-4 py-5 sm:p-6">
                            <div class="space-y-4" id="recent-products-container">
                                <?php 
                                // Display the first 3 products
                                $count = 0;
                                if (mysqli_num_rows($products_result) > 0) {
                                    while ($row = mysqli_fetch_assoc($products_result)) {
                                        if ($count >= 3) break;
                                        
                                        echo '<div class="flex items-center justify-between border-b pb-3">';
                                        echo '<div class="w-full">';
                                        echo '<h4 class="text-sm font-medium text-gray-900">' . htmlspecialchars($row['name']) . '</h4>';
                                        echo '<p class="text-sm text-gray-500">' . format_currency($row['price']) . '</p>';
                                        echo '<p class="text-xs text-gray-400">' . htmlspecialchars($row['category']) . '</p>';
                                        echo '</div>';
                                        echo '</div>';
                                        
                                        $count++;
                                    }
                                } else {
                                    echo '<p class="text-gray-500">No products added yet.</p>';
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- All Products Table -->
                <div class="mt-10 bg-white shadow overflow-hidden sm:rounded-lg">
                    <div class="px-6 py-5 border-b border-gray-200 sm:px-8" style="background: linear-gradient(to right, #1E3E62, #0B192C);">
                        <h3 class="text-lg leading-6 font-medium text-white">
                            All Products
                        </h3>
                        <p class="mt-1 max-w-2xl text-sm text-white">
                            Complete list of all products
                        </p>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">S.No</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product Name</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php 
                                // Reset the result pointer
                                mysqli_data_seek($products_result, 0);
                                
                                if (mysqli_num_rows($products_result) > 0) {
                                    $serial_number = 1;
                                    while ($row = mysqli_fetch_assoc($products_result)) {
                                        echo '<tr>';
                                        echo '<td class="px-6 py-4 whitespace-nowrap">';
                                        echo '<div class="text-sm font-medium text-gray-900">' . $serial_number . '</div>';
                                        echo '</td>';
                                        echo '<td class="px-6 py-4 whitespace-nowrap">';
                                        echo '<div class="text-sm font-medium text-gray-900">' . htmlspecialchars($row['name']) . '</div>';
                                        echo '</td>';
                                        
                                        $serial_number++;
                                        echo '<td class="px-6 py-4 whitespace-nowrap">';
                                        echo '<div class="text-sm text-gray-900">' . format_currency($row['price']) . '</div>';
                                        echo '</td>';
                                        echo '<td class="px-6 py-4 whitespace-nowrap">';
                                        echo '<div class="text-sm text-gray-900">' . htmlspecialchars($row['category']) . '</div>';
                                        echo '</td>';
                                        echo '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">';
                                        echo date('M d, Y', strtotime($row['created_at']));
                                        echo '</td>';
                                        echo '<td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">';
                                        echo '<a href="edit_product.php?id=' . $row['id'] . '" class="text-indigo-600 hover:text-indigo-900 mx-2" title="Edit Product"><i class="fas fa-edit"></i></a>';
                                        echo '<a href="delete_product.php?id=' . $row['id'] . '" class="text-red-600 hover:text-red-900 mx-2" title="Delete Product" onclick="return confirm(\'Are you sure you want to delete this product?\');"><i class="fas fa-trash-alt"></i></a>';
                                        echo '</td>';
                                        echo '</tr>';
                                    }
                                } else {
                                    echo '<tr><td colspan="5" class="px-6 py-4 text-center text-gray-500">No products added yet.</td></tr>';
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Sales Tracking and Analytics Section -->
                <div class="mt-10">
                    <h2 class="text-xl font-bold text-gray-800 mb-6">Sales Analytics & Insights</h2>
                    
                    <!-- Sales Tracking Charts -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                        <!-- Monthly Sales Chart -->
                        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                            <div class="px-6 py-5 border-b border-gray-200 sm:px-8" style="background: linear-gradient(to right, #1E3E62, #0B192C);">
                                <h3 class="text-lg leading-6 font-medium text-white">
                                    Monthly Sales Tracking
                                </h3>
                                <p class="mt-1 max-w-2xl text-sm text-white">
                                    Sales performance over the last 6 months
                                </p>
                            </div>
                            <div class="px-4 py-5 sm:p-6">
                                <canvas id="monthlySalesChart" height="300"></canvas>
                            </div>
                        </div>
                        
                        <!-- Product Category Distribution -->
                        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                            <div class="px-6 py-5 border-b border-gray-200 sm:px-8" style="background: linear-gradient(to right, #1E3E62, #0B192C);">
                                <h3 class="text-lg leading-6 font-medium text-white">
                                    Product Category Distribution
                                </h3>
                                <p class="mt-1 max-w-2xl text-sm text-white">
                                    Products and sales by category
                                </p>
                            </div>
                            <div class="px-4 py-5 sm:p-6">
                                <canvas id="categoryChart" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Product Popularity Insights -->
                    <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-8">
                        <div class="px-6 py-5 border-b border-gray-200 sm:px-8" style="background: linear-gradient(to right, #1E3E62, #0B192C);">
                            <h3 class="text-lg leading-6 font-medium text-white">
                                Product Popularity Insights
                            </h3>
                            <p class="mt-1 max-w-2xl text-sm text-white">
                                Top selling products and performance metrics
                            </p>
                        </div>
                        <div class="px-4 py-5 sm:p-6">
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sales Count</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Revenue</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Popularity</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <?php 
                                        if (mysqli_num_rows($top_products_result) > 0) {
                                            $rank = 1;
                                            while ($row = mysqli_fetch_assoc($top_products_result)) {
                                                echo '<tr>';
                                                echo '<td class="px-6 py-4 whitespace-nowrap">';
                                                echo '<div class="text-sm font-medium text-gray-900">' . htmlspecialchars($row['name']) . '</div>';
                                                echo '</td>';
                                                echo '<td class="px-6 py-4 whitespace-nowrap">';
                                                echo '<div class="text-sm text-gray-900">' . $row['sale_count'] . '</div>';
                                                echo '</td>';
                                                echo '<td class="px-6 py-4 whitespace-nowrap">';
                                                echo '<div class="text-sm text-gray-900">' . format_currency($row['total_amount']) . '</div>';
                                                echo '</td>';
                                                echo '<td class="px-6 py-4 whitespace-nowrap">';
                                                
                                                // Display popularity rating with stars
                                                $stars = 5;
                                                if ($rank == 1) $stars = 5;
                                                else if ($rank == 2) $stars = 4;
                                                else if ($rank == 3) $stars = 4;
                                                else if ($rank == 4) $stars = 3;
                                                else $stars = 3;
                                                
                                                echo '<div class="flex items-center">';
                                                for ($i = 0; $i < $stars; $i++) {
                                                    echo '<svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>';
                                                }
                                                for ($i = 0; $i < (5 - $stars); $i++) {
                                                    echo '<svg class="w-4 h-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>';
                                                }
                                                echo '</div>';
                                                
                                                echo '</td>';
                                                echo '</tr>';
                                                $rank++;
                                            }
                                        } else {
                                            echo '<tr><td colspan="4" class="px-6 py-4 text-center text-gray-500">No sales data available yet.</td></tr>';
                                        }
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- Chart for Product Popularity -->
                            <div class="mt-8">
                                <h4 class="text-base font-medium text-gray-900 mb-4">Product Popularity Visualization</h4>
                                <div class="h-64">
                                    <canvas id="productPopularityChart"></canvas>
                                </div>
                            </div>
                            
                            <!-- JavaScript for Chart -->
                            <script>
                                document.addEventListener('DOMContentLoaded', function() {
                                    // Get data from the table
                                    const productNames = [];
                                    const salesCounts = [];
                                    const revenues = [];
                                    const popularityRatings = [];
                                    
                                    <?php
                                    if (mysqli_num_rows($top_products_result) > 0) {
                                        // Reset the result pointer
                                        mysqli_data_seek($top_products_result, 0);
                                        $rank = 1;
                                        while ($row = mysqli_fetch_assoc($top_products_result)) {
                                            echo "productNames.push('" . addslashes($row['name']) . "');\n";
                                            echo "salesCounts.push(" . $row['sale_count'] . ");\n";
                                            echo "revenues.push(" . $row['total_amount'] . ");\n";
                                            
                                            // Calculate stars based on rank
                                            $stars = 5;
                                            if ($rank == 1) $stars = 5;
                                            else if ($rank == 2) $stars = 4;
                                            else if ($rank == 3) $stars = 4;
                                            else if ($rank == 4) $stars = 3;
                                            else $stars = 3;
                                            
                                            echo "popularityRatings.push(" . $stars . ");\n";
                                            $rank++;
                                        }
                                    }
                                    ?>
                                    
                                    // Create the chart
                                    const ctx = document.getElementById('productPopularityChart').getContext('2d');
                                    const productPopularityChart = new Chart(ctx, {
                                        type: 'bar',
                                        data: {
                                            labels: productNames,
                                            datasets: [
                                                {
                                                    label: 'Sales Count',
                                                    data: salesCounts,
                                                    backgroundColor: '#FF6500',
                                                    borderColor: '#FF6500',
                                                    borderWidth: 1,
                                                    yAxisID: 'y'
                                                },
                                                {
                                                    label: 'Popularity Rating (Stars)',
                                                    data: popularityRatings,
                                                    backgroundColor: '#FFD700',
                                                    borderColor: '#FFD700',
                                                    borderWidth: 1,
                                                    type: 'line',
                                                    yAxisID: 'y1'
                                                }
                                            ]
                                        },
                                        options: {
                                            responsive: true,
                                            maintainAspectRatio: false,
                                            interaction: {
                                                mode: 'index',
                                                intersect: false,
                                            },
                                            scales: {
                                                y: {
                                                    beginAtZero: true,
                                                    title: {
                                                        display: true,
                                                        text: 'Sales Count'
                                                    },
                                                    position: 'left',
                                                },
                                                y1: {
                                                    beginAtZero: true,
                                                    max: 5,
                                                    title: {
                                                        display: true,
                                                        text: 'Popularity Rating'
                                                    },
                                                    position: 'right',
                                                    grid: {
                                                        drawOnChartArea: false,
                                                    },
                                                }
                                            },
                                            plugins: {
                                                tooltip: {
                                                    callbacks: {
                                                        label: function(context) {
                                                            let label = context.dataset.label || '';
                                                            if (label) {
                                                                label += ': ';
                                                            }
                                                            if (context.dataset.label === 'Popularity Rating (Stars)') {
                                                                label += context.parsed.y + ' stars';
                                                            } else {
                                                                label += context.parsed.y;
                                                            }
                                                            return label;
                                                        }
                                                    }
                                                },
                                                legend: {
                                                    position: 'top',
                                                },
                                                title: {
                                                    display: true,
                                                    text: 'Product Popularity Analysis'
                                                }
                                            }
                                        }
                                    });
                                });
                            </script>
                        </div>
                    </div>
                    

                </div>
            </div>
        </main>
    </div>
    
    <script>
    // Initialize charts when the DOM is fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        // Monthly Sales Chart
        const monthlySalesCtx = document.getElementById('monthlySalesChart').getContext('2d');
        const monthlySalesChart = new Chart(monthlySalesCtx, {
            type: 'line',
            data: {
                labels: <?php echo json_encode($monthly_sales_labels); ?>,
                datasets: [
                    {
                        label: 'Sales Amount',
                        data: <?php echo json_encode($monthly_sales_values); ?>,
                        backgroundColor: 'rgba(255, 101, 0, 0.2)',
                        borderColor: 'rgba(255, 101, 0, 1)',
                        borderWidth: 2,
                        tension: 0.3,
                        yAxisID: 'y'
                    },
                    {
                        label: 'Number of Sales',
                        data: <?php echo json_encode($monthly_sales_counts); ?>,
                        backgroundColor: 'rgba(30, 62, 98, 0.2)',
                        borderColor: 'rgba(30, 62, 98, 1)',
                        borderWidth: 2,
                        tension: 0.3,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Sales Amount'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Number of Sales'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        });
        
        // Category Distribution Chart
        const categoryCtx = document.getElementById('categoryChart').getContext('2d');
        const categoryChart = new Chart(categoryCtx, {
            type: 'bar',
            data: {
                labels: <?php echo json_encode($category_labels); ?>,
                datasets: [
                    {
                        label: 'Number of Products',
                        data: <?php echo json_encode($category_values); ?>,
                        backgroundColor: 'rgba(30, 62, 98, 0.7)',
                        borderColor: 'rgba(30, 62, 98, 1)',
                        borderWidth: 1
                    },
                    {
                        label: 'Number of Sales',
                        data: <?php echo json_encode($category_sales); ?>,
                        backgroundColor: 'rgba(255, 101, 0, 0.7)',
                        borderColor: 'rgba(255, 101, 0, 1)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Count'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Category'
                        }
                    }
                }
            }
        });
    });
    </script>
    
    <script>
    // Validation for price field
    document.addEventListener('DOMContentLoaded', function() {
        const priceInput = document.getElementById('price');
        const productForm = document.getElementById('product-form');
        
        productForm.addEventListener('submit', function(event) {
            const priceValue = priceInput.value;
            
            // Check if price is a valid whole number (no decimals)
            if (!/^[1-9][0-9]*$/.test(priceValue)) {
                alert('Please enter a valid price (whole numbers only, no decimals)!');
                event.preventDefault();
                return;
            }
            
            // Check if price is not larger than 8 digits
            if (priceValue.length > 8) {
                alert('Price cannot be larger than 8 digits!');
                event.preventDefault();
                return;
            }
        });
    });
    </script>
</body>
</html>
