<?php
session_start();
?>
<!DOCTYPE html>
<html>
<head>
    <title>Debug Session</title>
</head>
<body>
    <h1>Session Debug</h1>
    
    <h2>Session Data:</h2>
    <pre><?php print_r($_SESSION); ?></pre>
    
    <h2>POST Data:</h2>
    <pre><?php print_r($_POST); ?></pre>
    
    <h2>GET Data:</h2>
    <pre><?php print_r($_GET); ?></pre>
    
    <h2>Test Form:</h2>
    <form method="POST" action="convert_lead.php">
        <input type="hidden" name="lead_id" value="5">
        <button type="submit">Test Convert Lead 5</button>
    </form>
    
    <h2>Test JavaScript Form:</h2>
    <button onclick="testJSForm()">Test JS Form Submission</button>
    
    <script>
    function testJSForm() {
        console.log('Creating test form...');
        
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'convert_lead.php';
        
        const leadIdInput = document.createElement('input');
        leadIdInput.type = 'hidden';
        leadIdInput.name = 'lead_id';
        leadIdInput.value = '5';
        
        form.appendChild(leadIdInput);
        document.body.appendChild(form);
        
        console.log('Form created:', form);
        console.log('Form HTML:', form.outerHTML);
        
        form.submit();
    }
    </script>
</body>
</html>
