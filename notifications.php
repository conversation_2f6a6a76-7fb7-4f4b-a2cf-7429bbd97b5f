<?php
// Get database connection using reliable method
$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';

// Check if database is set up first
require_once 'check_database_setup.php';
redirect_to_setup_if_needed();

// Check if the user is logged in
ensure_session_started();
require_login();

// Get user ID
$user_id = $_SESSION['user_id'];

// Handle marking all as read
if (isset($_GET['mark_all_read']) && $_GET['mark_all_read'] == 1) {
    $mark_read_sql = "UPDATE notifications SET is_read = 1 WHERE user_id = ? AND is_read = 0";
    $mark_read_stmt = mysqli_prepare($conn, $mark_read_sql);
    mysqli_stmt_bind_param($mark_read_stmt, "i", $user_id);
    
    if (mysqli_stmt_execute($mark_read_stmt)) {
        $success_message = "All notifications marked as read.";
    } else {
        $error_message = "Error marking notifications as read.";
    }
}

// Handle deleting notifications
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $notification_id = intval($_GET['delete']);
    
    $delete_sql = "DELETE FROM notifications WHERE id = ? AND user_id = ?";
    $delete_stmt = mysqli_prepare($conn, $delete_sql);
    mysqli_stmt_bind_param($delete_stmt, "ii", $notification_id, $user_id);
    
    if (mysqli_stmt_execute($delete_stmt)) {
        $success_message = "Notification deleted.";
    } else {
        $error_message = "Error deleting notification.";
    }
}

// Handle clearing all notifications
if (isset($_GET['clear_all']) && $_GET['clear_all'] == 1) {
    $clear_sql = "DELETE FROM notifications WHERE user_id = ?";
    $clear_stmt = mysqli_prepare($conn, $clear_sql);
    mysqli_stmt_bind_param($clear_stmt, "i", $user_id);
    
    if (mysqli_stmt_execute($clear_stmt)) {
        $success_message = "All notifications cleared.";
    } else {
        $error_message = "Error clearing notifications.";
    }
}

// Pagination parameters
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$records_per_page = 20;
$offset = ($page - 1) * $records_per_page;

// Get total count for pagination
$count_sql = "SELECT COUNT(*) as total FROM notifications WHERE user_id = ?";
$count_stmt = mysqli_prepare($conn, $count_sql);
mysqli_stmt_bind_param($count_stmt, "i", $user_id);
mysqli_stmt_execute($count_stmt);
$count_result = mysqli_stmt_get_result($count_stmt);
$total_records = mysqli_fetch_assoc($count_result)['total'];
$total_pages = ceil($total_records / $records_per_page);

// Get notifications with pagination
$sql = "SELECT * FROM notifications WHERE user_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?";
$stmt = mysqli_prepare($conn, $sql);
mysqli_stmt_bind_param($stmt, "iii", $user_id, $records_per_page, $offset);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

// Get unread count
$unread_sql = "SELECT COUNT(*) as unread FROM notifications WHERE user_id = ? AND is_read = 0";
$unread_stmt = mysqli_prepare($conn, $unread_sql);
mysqli_stmt_bind_param($unread_stmt, "i", $user_id);
mysqli_stmt_execute($unread_stmt);
$unread_result = mysqli_stmt_get_result($unread_stmt);
$unread_count = mysqli_fetch_assoc($unread_result)['unread'];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="img/minilogo.jpg" type="image/x-icon">
    <title>Notifications - CRM System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .text-color { color: #FF6500; }
        .bg-color { background-color: #FF6500; }
        
        .notification-item {
            transition: background-color 0.2s ease;
        }
        
        .notification-item:hover {
            background-color: #f9fafb;
        }
        
        .notification-unread {
            background-color: #eff6ff;
        }
    </style>
</head>
<body class="">

<?php include 'includes/navigation.php'; ?>

<!-- Main Content -->
<div class="ml-0 min-[870px]:ml-60 min-h-screen flex flex-col">
    <header class="bg-[#1E3E62] p-4 flex justify-between z-10 flex-col">
        <h1 class="text-2xl font-bold text-white mx-10 my-2">Notifications</h1>
    </header>

    <main class="flex-1 overflow-y-auto space-y-10">
        <!-- Notifications Section -->
        <section class="px-10 mt-10">
            <!-- Success/Error Messages -->
            <?php if (isset($success_message)): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
                    <strong class="font-bold">Success!</strong>
                    <span class="block sm:inline"><?php echo $success_message; ?></span>
                    <button class="absolute top-0 bottom-0 right-0 px-4 py-3" onclick="this.parentElement.style.display='none'">
                        <svg class="fill-current h-6 w-6 text-green-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
                    </button>
                </div>
            <?php endif; ?>
            
            <?php if (isset($error_message)): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
                    <strong class="font-bold">Error!</strong>
                    <span class="block sm:inline"><?php echo $error_message; ?></span>
                    <button class="absolute top-0 bottom-0 right-0 px-4 py-3" onclick="this.parentElement.style.display='none'">
                        <svg class="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
                    </button>
                </div>
            <?php endif; ?>
            
            <div class="bg-white p-6 rounded-2xl shadow-xl mb-6">
                <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
                    <div>
                        <h3 class="text-lg font-semibold text-color">All Notifications</h3>
                        <?php if ($unread_count > 0): ?>
                            <p class="text-sm text-gray-500">You have <?php echo $unread_count; ?> unread notification<?php echo $unread_count > 1 ? 's' : ''; ?></p>
                        <?php else: ?>
                            <p class="text-sm text-gray-500">You have no unread notifications</p>
                        <?php endif; ?>
                    </div>
                    <div class="flex space-x-2 mt-4 md:mt-0">
                        <?php if ($unread_count > 0): ?>
                            <a href="?mark_all_read=1" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors">
                                <i class="fas fa-check-double mr-1"></i> Mark All as Read
                            </a>
                        <?php endif; ?>
                        <?php if ($total_records > 0): ?>
                            <a href="?clear_all=1" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 transition-colors" onclick="return confirm('Are you sure you want to clear all notifications?');">
                                <i class="fas fa-trash mr-1"></i> Clear All
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Notifications List -->
                <div class="overflow-hidden rounded-lg border border-gray-200">
                    <?php if (mysqli_num_rows($result) > 0): ?>
                        <ul class="divide-y divide-gray-200">
                            <?php while ($row = mysqli_fetch_assoc($result)): ?>
                                <li class="notification-item <?php echo $row['is_read'] ? '' : 'notification-unread'; ?>">
                                    <div class="flex items-center justify-between p-4">
                                        <div class="flex-1">
                                            <div class="flex items-center">
                                                <?php
                                                // Set default icon class
                                                $icon_class = 'fas fa-bell';
                                                
                                                // Check if type exists before using it
                                                if (isset($row['type'])) {
                                                    switch ($row['type']) {
                                                        case 'lead_created':
                                                            $icon_class = 'fas fa-user-plus';
                                                            break;
                                                        case 'lead_assigned':
                                                            $icon_class = 'fas fa-user-check';
                                                            break;
                                                        case 'lead_converted':
                                                            $icon_class = 'fas fa-exchange-alt';
                                                            break;
                                                        case 'lead_followup':
                                                            $icon_class = 'fas fa-envelope';
                                                            break;
                                                    }
                                                }
                                                ?>
                                                <div class="flex-shrink-0 mr-3">
                                                    <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                                                        <i class="<?php echo $icon_class; ?> text-blue-500"></i>
                                                    </div>
                                                </div>
                                                <div class="flex-1 min-w-0">
                                                    <p class="text-sm font-medium text-gray-900 truncate">
                                                        <?php 
                                                        // Check if title exists, otherwise use a default or first part of message
                                                        if (isset($row['title'])) {
                                                            echo htmlspecialchars($row['title']);
                                                        } else {
                                                            // Use the first part of the message as a title
                                                            $title = substr($row['message'], 0, strpos($row['message'] . ':', ':'));
                                                            echo htmlspecialchars($title);
                                                        }
                                                        ?>
                                                    </p>
                                                    <p class="text-sm text-gray-500">
                                                        <?php echo htmlspecialchars($row['message']); ?>
                                                    </p>
                                                    <p class="text-xs text-gray-400 mt-1">
                                                        <?php echo date('M d, Y h:i A', strtotime($row['created_at'])); ?>
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex-shrink-0 ml-4 flex space-x-2">
                                            <?php if (!$row['is_read']): ?>
                                                <a href="?mark_read=<?php echo $row['id']; ?>" class="text-blue-500 hover:text-blue-700" title="Mark as Read">
                                                    <i class="fas fa-check"></i>
                                                </a>
                                            <?php endif; ?>
                                            <?php if (isset($row['related_id']) && $row['related_id']): ?>
                                                <a href="view_contact.php?id=<?php echo $row['related_id']; ?>" class="text-green-500 hover:text-green-700" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            <?php elseif (isset($row['link']) && $row['link']): ?>
                                                <a href="<?php echo $row['link']; ?>" class="text-green-500 hover:text-green-700" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            <?php endif; ?>
                                            <a href="?delete=<?php echo $row['id']; ?>" class="text-red-500 hover:text-red-700" title="Delete" onclick="return confirm('Are you sure you want to delete this notification?');">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </div>
                                </li>
                            <?php endwhile; ?>
                        </ul>
                    <?php else: ?>
                        <div class="p-8 text-center text-gray-500">
                            <i class="fas fa-bell-slash text-4xl mb-4 text-gray-300"></i>
                            <p>You have no notifications</p>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <div class="mt-6 flex justify-center">
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <?php if ($page > 1): ?>
                                <a href="?page=<?php echo $page - 1; ?>" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">Previous</span>
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            <?php endif; ?>
                            
                            <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                <a href="?page=<?php echo $i; ?>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium <?php echo $i === $page ? 'text-blue-600 bg-blue-50' : 'text-gray-700 hover:bg-gray-50'; ?>">
                                    <?php echo $i; ?>
                                </a>
                            <?php endfor; ?>
                            
                            <?php if ($page < $total_pages): ?>
                                <a href="?page=<?php echo $page + 1; ?>" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">Next</span>
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            <?php endif; ?>
                        </nav>
                    </div>
                <?php endif; ?>
            </div>
        </section>
    </main>
</div>

</body>
</html>