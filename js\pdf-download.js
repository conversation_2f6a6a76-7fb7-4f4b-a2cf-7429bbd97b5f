/**
 * PDF Download Functionality
 * 
 * This script handles the PDF download functionality for the sales report.
 */

// Wait for both DOM and external scripts to load
window.addEventListener('load', function() {
    console.log('PDF Download script loaded');
    
    // Ensure jsPDF is properly loaded
    if (typeof window.jspdf === 'undefined') {
        console.warn('jsPDF not loaded yet, loading it directly');
        // Create a script element to load jsPDF
        const jsPdfScript = document.createElement('script');
        jsPdfScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
        jsPdfScript.onload = function() {
            console.log('jsPDF loaded successfully');
            initializePdfDownload();
        };
        jsPdfScript.onerror = function() {
            console.error('Failed to load jsPDF');
            alert('Failed to load PDF generation library. Please check your internet connection and try again.');
        };
        document.head.appendChild(jsPdfScript);
    } else {
        initializePdfDownload();
    }
    
    function initializePdfDownload() {
        // Attach event listener to the download button
        const downloadBtn = document.getElementById('download-report-btn');
        if (downloadBtn) {
            downloadBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Download PDF button clicked');
                downloadReportAsPDF();
            });
        } else {
            console.error('Download PDF button not found in the DOM');
        }
    }
    
    // Function to download the report as PDF
    function downloadReportAsPDF() {
        console.log('Starting PDF generation...');
        
        try {
            // Check if jspdf is properly loaded
            if (typeof window.jspdf === 'undefined') {
                console.error('jsPDF library not loaded properly');
                alert('PDF generation library not loaded. Please refresh the page and try again.');
                return;
            }
            
            // Create a new PDF document
            const doc = new window.jspdf.jsPDF('p', 'mm', 'a4');
            
            // Get date information
            const fromDate = document.getElementById('report-date-from').value;
            const toDate = document.getElementById('report-date-to').value;
            const fromDateFormatted = new Date(fromDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
            const toDateFormatted = new Date(toDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
            const reportId = document.getElementById('report-id').textContent;
            const currentDate = new Date().toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' });
            
            // Add header
            doc.setFillColor(30, 62, 98); // #1E3E62
            doc.rect(0, 0, 210, 30, 'F');
            
            doc.setTextColor(255, 255, 255);
            doc.setFontSize(22);
            doc.setFont('helvetica', 'bold');
            doc.text('SALES REPORT', 15, 15);
            
            doc.setFontSize(10);
            doc.setFont('helvetica', 'normal');
            doc.text(`Report #: ${reportId}`, 15, 22);
            
            doc.setFontSize(10);
            doc.text(`Date Range: ${fromDateFormatted} to ${toDateFormatted}`, 120, 15);
            doc.text(`Generated: ${currentDate}`, 120, 22);
            
            // Add summary section
            doc.setTextColor(0, 0, 0);
            doc.setFontSize(16);
            doc.setFont('helvetica', 'bold');
            doc.text('SUMMARY', 15, 40);
            
            doc.setDrawColor(200, 200, 200);
            doc.line(15, 42, 195, 42);
            
            // Summary data
            doc.setFontSize(10);
            doc.setFont('helvetica', 'normal');
            doc.text('Total Sales:', 15, 50);
            doc.text('Total Revenue:', 65, 50);
            doc.text('Average Sale:', 115, 50);
            doc.text('Conversion Rate:', 165, 50);
            
            doc.setFont('helvetica', 'bold');
            doc.text(document.getElementById('report-total-sales').textContent, 15, 58);
            doc.text(document.getElementById('report-total-revenue').textContent, 65, 58);
            doc.text(document.getElementById('report-average-sale').textContent, 115, 58);
            doc.text(document.getElementById('report-conversion-rate').textContent, 165, 58);
            
            // Add charts
            doc.setFontSize(14);
            doc.setFont('helvetica', 'bold');
            doc.text('MONTHLY SALES TREND', 15, 75);
            
            doc.setDrawColor(200, 200, 200);
            doc.line(15, 77, 195, 77);
            
            // Use html2canvas to convert the charts to images
            html2canvas(document.getElementById('monthly-sales-chart')).then(canvas => {
                const imgData = canvas.toDataURL('image/png');
                doc.addImage(imgData, 'PNG', 15, 80, 180, 60);
                
                doc.setFontSize(14);
                doc.setFont('helvetica', 'bold');
                doc.text('TOP PRODUCTS', 15, 150);
                
                doc.setDrawColor(200, 200, 200);
                doc.line(15, 152, 195, 152);
                
                // Add product sales chart
                html2canvas(document.getElementById('product-sales-chart')).then(canvas => {
                    const imgData = canvas.toDataURL('image/png');
                    doc.addImage(imgData, 'PNG', 15, 155, 180, 60);
                    
                    // Add top customers table on new page
                    doc.addPage();
                    
                    // Add header to new page
                    doc.setFillColor(30, 62, 98);
                    doc.rect(0, 0, 210, 30, 'F');
                    
                    doc.setTextColor(255, 255, 255);
                    doc.setFontSize(22);
                    doc.setFont('helvetica', 'bold');
                    doc.text('SALES REPORT', 15, 15);
                    
                    doc.setFontSize(10);
                    doc.setFont('helvetica', 'normal');
                    doc.text(`Report #: ${reportId}`, 15, 22);
                    doc.text(`Page 2 of 2`, 180, 22);
                    
                    // Top customers table
                    doc.setTextColor(0, 0, 0);
                    doc.setFontSize(16);
                    doc.setFont('helvetica', 'bold');
                    doc.text('TOP CUSTOMERS', 15, 40);
                    
                    doc.setDrawColor(200, 200, 200);
                    doc.line(15, 42, 195, 42);
                    
                    // Table header
                    doc.setFillColor(240, 240, 240);
                    doc.rect(15, 45, 180, 8, 'F');
                    
                    doc.setFontSize(9);
                    doc.setTextColor(80, 80, 80);
                    doc.text('CUSTOMER', 17, 50);
                    doc.text('SALES', 65, 50);
                    doc.text('REVENUE', 90, 50);
                    doc.text('TOP PRODUCT', 125, 50);
                    doc.text('QTY', 165, 50);
                    doc.text('DETAILS', 180, 50);
                    
                    // Table rows
                    doc.setTextColor(0, 0, 0);
                    doc.setFont('helvetica', 'normal');
                    const customers = document.getElementById('top-customers-body').getElementsByTagName('tr');
                    let y = 58;
                    
                    for (let i = 0; i < customers.length; i++) {
                        const cells = customers[i].getElementsByTagName('td');
                        if (cells.length >= 4) {
                            if (i % 2 === 1) {
                                doc.setFillColor(248, 248, 248);
                                doc.rect(15, y - 4, 180, 8, 'F');
                            }
                            
                            // Customer name
                            let customerName = cells[0].textContent;
                            if (customerName.length > 20) {
                                customerName = customerName.substring(0, 17) + '...';
                            }
                            doc.text(customerName, 17, y);
                            
                            // Sales count
                            doc.text(cells[1].textContent, 65, y);
                            
                            // Revenue
                            doc.text(cells[2].textContent, 90, y);
                            
                            // Top product
                            let productName = cells[3].textContent;
                            if (productName.length > 15) {
                                productName = productName.substring(0, 12) + '...';
                            }
                            doc.text(productName, 125, y);
                            
                            // Quantity
                            if (cells.length >= 5) {
                                doc.text(cells[4].textContent, 165, y);
                            }
                            
                            // Details - extract category from the details cell
                            if (cells.length >= 6) {
                                const detailsText = cells[5].textContent;
                                const categoryMatch = detailsText.match(/Category:\s*([^\n]+)/);
                                if (categoryMatch && categoryMatch[1]) {
                                    let category = categoryMatch[1].trim();
                                    if (category.length > 10) {
                                        category = category.substring(0, 7) + '...';
                                    }
                                    doc.text(category, 180, y);
                                }
                            }
                            
                            y += 8;
                        }
                    }
                    
                    // Top executives table
                    doc.setFontSize(16);
                    doc.setFont('helvetica', 'bold');
                    doc.text('TOP EXECUTIVES', 15, y + 15);
                    
                    doc.setDrawColor(200, 200, 200);
                    doc.line(15, y + 17, 195, y + 17);
                    
                    // Table header
                    doc.setFillColor(240, 240, 240);
                    doc.rect(15, y + 20, 180, 8, 'F');
                    
                    doc.setFontSize(9);
                    doc.setTextColor(80, 80, 80);
                    doc.text('SALES REP', 17, y + 25);
                    doc.text('SALES', 90, y + 25);
                    doc.text('REVENUE', 150, y + 25);
                    
                    // Table rows
                    doc.setTextColor(0, 0, 0);
                    doc.setFont('helvetica', 'normal');
                    const executives = document.getElementById('top-executives-body').getElementsByTagName('tr');
                    let execY = y + 33;
                    
                    for (let i = 0; i < executives.length; i++) {
                        const cells = executives[i].getElementsByTagName('td');
                        if (cells.length >= 3) {
                            if (i % 2 === 1) {
                                doc.setFillColor(248, 248, 248);
                                doc.rect(15, execY - 4, 180, 8, 'F');
                            }
                            
                            doc.text(cells[0].textContent, 17, execY);
                            doc.text(cells[1].textContent, 90, execY);
                            doc.text(cells[2].textContent, 150, execY);
                            
                            execY += 8;
                        }
                    }
                    
                    // Add footer
                    doc.setFontSize(8);
                    doc.setTextColor(150, 150, 150);
                    doc.text('© LEAD Management System', 15, 280);
                    doc.text('Generated on: ' + new Date().toLocaleString(), 120, 280);
                    
                    // Save the PDF
                    doc.save('sales_report.pdf');
                    console.log('PDF generated and downloaded successfully');
                }).catch(err => {
                    console.error('Error generating product sales chart:', err);
                    alert('Error generating PDF: ' + err.message);
                });
            }).catch(err => {
                console.error('Error generating monthly sales chart:', err);
                alert('Error generating PDF: ' + err.message);
            });
        } catch (err) {
            console.error('Error in PDF generation:', err);
            alert('Error generating PDF: ' + err.message);
        }
    }
});
