<?php
// Get database connection using reliable method
$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';

// Check if database is set up first
require_once 'check_database_setup.php';
redirect_to_setup_if_needed();

// Check if the user is logged in
ensure_session_started();
require_login();

// Get the sale ID
$sale_id = intval($_GET['id']);

// Check if the sale exists and get owner information
$check_sale_sql = "SELECT sales_rep_id FROM sales WHERE id = ?";
$check_sale_stmt = mysqli_prepare($conn, $check_sale_sql);
mysqli_stmt_bind_param($check_sale_stmt, "i", $sale_id);
mysqli_stmt_execute($check_sale_stmt);
$check_sale_result = mysqli_stmt_get_result($check_sale_stmt);

if (mysqli_num_rows($check_sale_result) == 0) {
    header("Location: " . (has_role("sales_rep") ? "my_sales.php" : "sales.php") . "?error=1&message=Sale not found");
    exit;
}

$sale_data = mysqli_fetch_assoc($check_sale_result);
$sales_rep_id = $sale_data['sales_rep_id'];

// Check if user has permission
// Admin and manager can delete any sale
// Sales rep can only delete their own sales
if (has_role("sales_rep") && $sales_rep_id != $_SESSION['user_id']) {
    header("Location: my_sales.php?error=1&message=You do not have permission to delete this sale");
    exit;
}

// Check if sale ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: " . (has_role("sales_rep") ? "my_sales.php" : "sales.php") . "?error=1&message=No sale ID provided");
    exit;
}

// Check if confirmation is provided
if (!isset($_GET['confirm']) || $_GET['confirm'] != '1') {
    // Show confirmation page
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Confirm Delete Sale - CRM System</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <style>
            .text-color{ color: #FF6500; }
            .button-hover {
                transition: background-color 0.3s ease, transform 0.2s ease;
            }
            .button-hover:hover {
                background-color: #e55b00;
                transform: scale(1.05);
            }
        </style>
    </head>
    <body class="bg-gray-100 min-h-screen flex items-center justify-center">
        <div class="bg-white rounded-lg shadow-lg p-8 max-w-md w-full">
            <div class="text-center mb-6">
                <i class="fas fa-exclamation-triangle text-red-500 text-5xl mb-4"></i>
                <h1 class="text-2xl font-bold text-gray-800">Confirm Delete</h1>
                <p class="text-gray-600 mt-2">Are you sure you want to delete this sale? This action cannot be undone.</p>
            </div>
            
            <?php
            // Get sale details for confirmation
            $sql = "SELECT s.*, c.name as customer_name, p.name as product_name 
                    FROM sales s
                    LEFT JOIN customers c ON s.customer_id = c.id
                    LEFT JOIN products p ON s.product_id = p.id
                    WHERE s.id = ?";
            $stmt = mysqli_prepare($conn, $sql);
            mysqli_stmt_bind_param($stmt, "i", $sale_id);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            
            if (mysqli_num_rows($result) > 0) {
                $sale = mysqli_fetch_assoc($result);
                ?>
                <div class="bg-gray-50 p-4 rounded-lg mb-6">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm text-gray-500">Customer:</p>
                            <p class="font-medium"><?php echo htmlspecialchars($sale['customer_name']); ?></p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Product:</p>
                            <p class="font-medium"><?php echo htmlspecialchars($sale['product_name']); ?></p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Amount:</p>
                            <p class="font-medium"><?php echo format_currency($sale['amount']); ?></p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Date:</p>
                            <p class="font-medium"><?php echo date('d M, Y', strtotime($sale['sale_date'])); ?></p>
                        </div>
                    </div>
                </div>
                <?php
            } else {
                echo '<div class="bg-red-100 text-red-700 p-4 rounded-lg mb-6">Sale not found.</div>';
            }
            ?>
            
            <div class="flex justify-between">
                <a href="sales.php" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                    Cancel
                </a>
                <a href="delete_sale.php?id=<?php echo $sale_id; ?>&confirm=1" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                    Delete Sale
                </a>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// Process deletion
try {
    // Start transaction
    mysqli_begin_transaction($conn);
    
    // Delete the sale record (consolidated table structure - no need to delete from separate tables)
    $delete_sale_sql = "DELETE FROM sales WHERE id = ?";
    $delete_sale_stmt = mysqli_prepare($conn, $delete_sale_sql);
    mysqli_stmt_bind_param($delete_sale_stmt, "i", $sale_id);
    $result = mysqli_stmt_execute($delete_sale_stmt);
    
    // Commit transaction
    mysqli_commit($conn);
    
    // Redirect with success message
    header("Location: " . (has_role("sales_rep") ? "my_sales.php" : "sales.php") . "?success=1&message=Sale deleted successfully");
    exit;
    
} catch (Exception $e) {
    // Rollback transaction on error
    mysqli_rollback($conn);
    
    // Redirect with error message
    header("Location: " . (has_role("sales_rep") ? "my_sales.php" : "sales.php") . "?error=1&message=" . urlencode("Error deleting sale: " . $e->getMessage()));
    exit;
}
?>
