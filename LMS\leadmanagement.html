<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LMS-Lead Management</title>
    <link rel="icon" type="image/png" href="/images/logo.title.png">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet" />
    <link
        href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Open+Sans:wght@400;500;600&display=swap"
        rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css" />
    <!-- AOS CSS -->
    <link href="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.css" rel="stylesheet">
</head>

<body class="bg-gray-100 text-white overflow-x-hidden">


  <header class="fixed w-full top-0 z-50" x-data="{ isOpen: false, featuresOpen: false }">
    <div class="lg:max-w-full mx-auto px-4 sm:px-4 md:px-6 lg:px-8 py-2 sm:py-3 md:py-4">
      <div
        class=" bg-white backdrop-blur-lg rounded-lg sm:rounded-xl lg:rounded-2xl shadow-lg border border-white/20 mx-1 sm:mx-2 md:mx-3 lg:mx-4">
        <div class=" flex justify-between items-center h-12 sm:h-14 md:h-16 px-2 sm:px-4 md:px-6">

          <div>
            <h1
              class="text-lg sm:text-xl md:text-2xl bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent relative">
              <img src="images/logo.png" alt="LMS Logo" class="lg:w-32  w-20 object-contain">
            </h1>
          </div>

          <!-- Desktop Nav -->
          <nav class="hidden lg:flex justify-between items-center">
            <!-- logo -->

            
            <div class="flex  ">
              <a href="index.html"
                class="flex nav-link px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-all duration-300">
                Home
              </a>
              <a href="About.html"
                class="flex px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-all duration-300">
                About
              </a>
              <a href="products.html"
                class=" flex px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-all duration-300">
                Products
              </a>

              <!-- Features Dropdown -->
              <div class="relative" x-data="{ open: false }" @mouseenter="open = true" @mouseleave="open = false">
                <button
                  class="px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-all duration-300 flex items-center">
                  Features
                  <i class="ri-arrow-down-s-line ml-1 transition-transform duration-200"
                    :class="open ? 'rotate-180' : ''"></i>
                </button>

                <!-- Dropdown Menu -->
                <div x-show="open" x-transition:enter="transition ease-out duration-200"
                  x-transition:enter-start="opacity-0 transform scale-95"
                  x-transition:enter-end="opacity-100 transform scale-100"
                  x-transition:leave="transition ease-in duration-150"
                  x-transition:leave-start="opacity-100 transform scale-100"
                  x-transition:leave-end="opacity-0 transform scale-95"
                  class="absolute top-full left-0 mt-2 w-64 bg-white rounded-xl shadow-2xl border border-gray-100 py-2 z-50">

                  <a href="leadmanagement.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-brain-line text-blue-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Lead Management</div>
                      <div class="text-xs text-gray-500">Smart lead prioritization</div>
                    </div>
                  </a>

                  <a href="salemanagement.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-settings-3-line text-green-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Sales Management</div>
                      <div class="text-xs text-gray-500">Streamline your process</div>
                    </div>
                  </a>

                  <a href="analytics.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-pie-chart-line text-purple-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Advanced Analytics</div>
                      <div class="text-xs text-gray-500">Detailed insights & reports</div>
                    </div>
                  </a>

                  <a href="taskmanager.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-smartphone-line text-orange-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Task Manager</div>
                      <div class="text-xs text-gray-500">Capture from anywhere</div>
                    </div>
                  </a>

                  <a href="customersupport.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-team-line text-indigo-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Customer Support</div>
                      <div class="text-xs text-gray-500">Work together effectively</div>
                    </div>
                  </a>

                  <a href="integration.html"
                    class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 hover:text-[#1E3E62] transition-colors duration-200">
                    <div class="w-8 h-8 bg-teal-100 rounded-lg flex items-center justify-center mr-3">
                      <i class="ri-shield-check-line text-teal-600"></i>
                    </div>
                    <div>
                      <div class="font-bold">Integration</div>
                      <div class="text-xs text-gray-500">Connect seamlessly with tools</div>
                    </div>
                  </a>
                </div>
              </div>

            </div>
            <!-- login -->
            <div class="flex ">
              <a href="contact.html"
                class=" flex items-center font-bold px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-black  transition-all duration-300">
                Contact
              </a>

              <a href="/LEADManagement/auth/login.php"
                class=" flex items-center bg-orange-500 rounded-3xl font-bold px-3 sm:px-4 md:px-5 lg:px-6 py-1.5 sm:py-2 text-sm sm:text-base md:text-lg text-white  transition-all duration-300">
                Login
              </a>
            </div>

          </nav>

          <!-- Mobile Menu Button -->
          <div class="lg:hidden z-50">
            <button @click="isOpen = !isOpen" class="focus:outline-none relative w-6 sm:w-7 md:w-8 h-6 sm:h-7 md:h-8">
              <span class="absolute top-1 left-0 w-6 sm:w-7 md:w-8 h-0.5 bg-black transition-transform duration-300"
                :class="isOpen ? 'rotate-45 top-3' : ''"></span>
              <span class="absolute top-3 left-0 w-6 sm:w-7 md:w-8 h-0.5 bg-black transition-opacity duration-300"
                :class="isOpen ? 'opacity-0' : ''"></span>
              <span class="absolute top-5 left-0 w-6 sm:w-7 md:w-8 h-0.5 bg-black transition-transform duration-300"
                :class="isOpen ? '-rotate-45 top-3' : ''"></span>
            </button>
          </div>
        </div>

        <!-- Mobile Nav Menu -->
        <div class="lg:hidden px-3 sm:px-4 md:px-6 pb-4 sm:pb-5 md:pb-6 pt-2" x-show="isOpen" x-transition>
          <div class="flex flex-col space-y-2 sm:space-y-3 md:space-y-4">
            <a href="index.html"
              class="text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200">
              Home
            </a>
            <a href="About.html"
              class="text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200">
              About
            </a>
            <a href="products.html"
              class="text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200">
              Products
            </a>
            <a href="contact.html"
              class="text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200">
              Contact
            </a>

            <!-- Mobile Features Section -->
            <div x-data="{ mobileFeatures: false }">
              <button @click="mobileFeatures = !mobileFeatures"
                class="w-full text-left text-sm sm:text-base md:text-lg text-black font-bold hover:text-[#1E3E62] transition-colors duration-200 flex items-center justify-between">
                <span>Features</span>
                <i class="ri-arrow-down-s-line transition-transform duration-200"
                  :class="mobileFeatures ? 'rotate-180' : ''"></i>
              </button>

              <div x-show="mobileFeatures" x-transition class="ml-4 mt-2 space-y-2">
                <a href="leadmanagement.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-brain-line mr-2"></i>Lead Management
                </a>
                <a href="salemanagement.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-settings-3-line mr-2"></i>Email
                  Marketing
                </a>
                <a href="analytics.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-pie-chart-line mr-2"></i>Advanced Analytics
                </a>
                <a href="taskmanager.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-smartphone-line mr-2"></i>Task Manager
                </a>
                <a href="customersupport.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-team-line mr-2"></i>Customer Support
                </a>
                <a href="integration.html" class="block text-xs sm:text-sm text-gray-600 font-bold hover:text-[#1E3E62] py-1">
                  <i class="ri-shield-check-line mr-2"></i>Integration
                </a>
              </div>
            </div>

            <a href="/LEADManagement/auth/login.php"
              class="bg-[#FF6500] text-white px-4 sm:px-5 md:px-6 py-1.5 sm:py-2 md:py-2.5 text-sm sm:text-base md:text-lg rounded-full font-bold text-center mt-2 hover:bg-[#e55a00] transition-colors duration-200">
              Login
            </a>
          </div>
        </div>
      </div>
    </div>
  </header>

    <!-- Hero Section -->
    <section class="bg-[#1E3E62] text-white py-16   " data-aos="fade-down">

        <div class="absolute inset-0 bg-black opacity-20"></div>
        <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center mt-20">
                <h1 class="text-4xl sm:text-6xl lg:text-7xl font-bold mb-6 leading-tight" data-aos="fade-up">
                    Lead Management
                    <span class="block text-orange-500 mt-2">Revolution</span>
                    
                </h1>
                <p class="text-xl sm:text-2xl text-gray-100 mb-8 max-w-3xl mx-auto" data-aos="fade-up"
                    data-aos-delay="200">
                    "Transform Your Business Growth by Seamlessly Capturing, Nurturing, and Converting Prospects into
                    Loyal Customers with Cutting-Edge Tools."
                </p>
            </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 bg-gray-50">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl sm:text-5xl font-bold mb-6 text-black">
                    Key Features
                </h2>
                <p class="text-xl text-gray-900 max-w-2xl mx-auto " data-aos="fade-up" data-aos-delay="200">
                    Discover the powerful tools that make lead management effortless and effective
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Feature 1 -->
                <div class="feature-card bg-[#0B192C] rounded-2xl p-8 glass-effect border-gray-400" data-aos="fade-up"
                    data-aos-delay="100">
                    <div class="mb-6">
                        <div
                            class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mb-4 floating">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                                </path>
                            </svg>
                        </div>
                        <h3 class="text-2xl   mb-3">Lead Scoring & Qualification</h3>
                        <p class="text-gray-400 leading-relaxed">
                            Automatically score and qualify leads based on behavior, demographics, and engagement
                            patterns to prioritize your sales efforts.
                        </p>
                    </div>
                </div>

                <!-- Feature 2 -->
                <div class="feature-card bg-[#0B192C] rounded-2xl p-8 glass-effect" data-aos="fade-up"
                    data-aos-delay="200">
                    <div class="mb-6">
                        <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-teal-600 rounded-2xl flex items-center justify-center mb-4 floating"
                            style="animation-delay: 0.5s;">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-semibold mb-3">Automated Workflows</h3>
                        <p class="text-gray-400 leading-relaxed">
                            Set up intelligent automation sequences that nurture leads through personalized email
                            campaigns and follow-up tasks.
                        </p>
                    </div>
                </div>

                <!-- Feature 3 -->
                <div class="feature-card bg-gray-800 rounded-2xl p-8 glass-effect" data-aos="fade-up"
                    data-aos-delay="300">
                    <div class="mb-6">
                        <div class="w-16 h-16 bg-gradient-to-r from-pink-500 to-red-600 rounded-2xl flex items-center justify-center mb-4 floating"
                            style="animation-delay: 1s;">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                </path>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-semibold mb-3">360° Lead Tracking</h3>
                        <p class="text-gray-400 leading-relaxed">
                            Track every interaction, touchpoint, and behavior across all channels to get a complete view
                            of your lead's journey.
                        </p>
                    </div>
                </div>

                <!-- Feature 4 -->
                <div class="feature-card bg-gray-800 rounded-2xl p-8 glass-effect" data-aos="fade-up"
                    data-aos-delay="400">
                    <div class="mb-6">
                        <div class="w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-600 rounded-2xl flex items-center justify-center mb-4 floating"
                            style="animation-delay: 1.5s;">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4">
                                </path>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-semibold mb-3">Smart Lead Distribution</h3>
                        <p class="text-gray-400 leading-relaxed">
                            Automatically assign leads to the right sales representatives based on territory, expertise,
                            and workload balance.
                        </p>
                    </div>
                </div>

                <!-- Feature 5 -->
                <div class="feature-card bg-gray-800 rounded-2xl p-8 glass-effect" data-aos="fade-up"
                    data-aos-delay="500">
                    <div class="mb-6">
                        <div class="w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center mb-4 floating"
                            style="animation-delay: 2s;">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                                </path>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-semibold mb-3">Advanced Analytics</h3>
                        <p class="text-gray-400 leading-relaxed">
                            Get detailed insights into lead conversion rates, pipeline performance, and ROI with
                            comprehensive reporting dashboards.
                        </p>
                    </div>
                </div>

                <!-- Feature 6 -->
                <div class="feature-card bg-gray-800 rounded-2xl p-8 glass-effect" data-aos="fade-up"
                    data-aos-delay="600">
                    <div class="mb-6">
                        <div class="w-16 h-16 bg-gradient-to-r from-cyan-500 to-blue-600 rounded-2xl flex items-center justify-center mb-4 floating"
                            style="animation-delay: 2.5s;">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z">
                                </path>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-semibold mb-3">Multi-Channel Integration</h3>
                        <p class="text-gray-400 leading-relaxed">
                            Capture leads from websites, social media, email campaigns, and phone calls in one unified
                            platform.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="py-20 bg-gray-50">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="text-center" data-aos="fade-up" data-aos-delay="100">
                    <div class="text-4xl lg:text-5xl font-bold text-[#1E3E62] mb-2">95%</div>
                    <p class="text-gray-400">Lead Capture Rate</p>
                </div>
                <div class="text-center" data-aos="fade-up" data-aos-delay="200">
                    <div class="text-4xl lg:text-5xl font-bold text-[#1E3E62]  mb-2">3x</div>
                    <p class="text-gray-400">Faster Conversions</p>
                </div>
                <div class="text-center" data-aos="fade-up" data-aos-delay="300">
                    <div class="text-4xl lg:text-5xl font-bold text-[#1E3E62]  mb-2">40%</div>
                    <p class="text-gray-400">Higher ROI</p>
                </div>
                <div class="text-center" data-aos="fade-up" data-aos-delay="400">
                    <div class="text-4xl lg:text-5xl font-bold text-[#1E3E62]  mb-2">24/7</div>
                    <p class="text-gray-400">Support Available</p>
                </div>
            </div>
        </div>
    </section>
    <section class="py-20 bg-gray-50 ">
        <div class="max-w-7xl mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">Streamlined Lead Process</h2>
                <p class="text-xl text-gray-600 dark:text-gray-400">From capture to conversion in 4 simple steps</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="text-center card-hover">
                    <div
                        class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                        <span class="text-white font-bold text-xl">1</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">Capture</h3>
                    <p class="text-gray-600 dark:text-gray-400">Automatically capture leads from multiple channels
                        including your website, social media, and marketing campaigns</p>
                </div>

                <div class="text-center card-hover">
                    <div
                        class="w-16 h-16 bg-gradient-to-r from-green-500 to-teal-600 rounded-full flex items-center justify-center mx-auto mb-6">
                        <span class="text-white font-bold text-xl">2</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">Score</h3>
                    <p class="text-gray-600 dark:text-gray-400">AI-powered lead scoring analyzes behavior and
                        demographics to identify your most promising prospects</p>
                </div>

                <div class="text-center card-hover">
                    <div
                        class="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-6">
                        <span class="text-white font-bold text-xl">3</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">Nurture</h3>
                    <p class="text-gray-600 dark:text-gray-400">Automated workflows send personalized messages and
                        content to move leads through your sales funnel</p>
                </div>

                <div class="text-center card-hover">
                    <div
                        class="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-6">
                        <span class="text-white font-bold text-xl">4</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">Convert</h3>
                    <p class="text-gray-600 dark:text-gray-400">Track conversions and optimize your process with
                        detailed analytics and performance insights</p>
                </div>
            </div>
        </div>
    </section>
    <!-- Dashboard Preview -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">Complete Lead Management Dashboard
                </h2>
                <p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">Monitor, track, and convert leads
                    with our comprehensive dashboard featuring real-time analytics and automated workflows</p>
            </div>

            <!-- Dashboard Cards -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
                <!-- Lead Pipeline -->
                <div class="bg-white rounded-2xl p-6 shadow-lg card-hover">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Lead Pipeline</h3>
                        <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                            <i data-lucide="funnel" class="w-4 h-4 text-blue-600"></i>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">New Leads</span>
                            <span class="font-semibold">148</span>
                        </div>
                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                            <div class="bg-blue-500 h-2 rounded-full" style="width: 75%"></div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Qualified</span>
                            <span class="font-semibold">89</span>
                        </div>
                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                            <div class="bg-green-500 h-2 rounded-full" style="width: 60%"></div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Converted</span>
                            <span class="font-semibold">34</span>
                        </div>
                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                            <div class="bg-purple-500 h-2 rounded-full" style="width: 23%"></div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="bg-white rounded-2xl p-6 shadow-lg card-hover">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Activity</h3>
                        <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                            <i data-lucide="activity" class="w-4 h-4 text-green-600"></i>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-green-500 rounded-full pulse-animation"></div>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-900 dark:text-white">New lead from website</p>
                                <p class="text-xs text-gray-500">2 minutes ago</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-900 dark:text-white">Email campaign sent</p>
                                <p class="text-xs text-gray-500">15 minutes ago</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-900 dark:text-white">Lead converted to customer
                                </p>
                                <p class="text-xs text-gray-500">1 hour ago</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Performance Metrics -->
                <div class="bg-white rounded-2xl p-6 shadow-lg card-hover">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Performance</h3>
                        <div
                            class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                            <i data-lucide="bar-chart" class="w-4 h-4 text-purple-600"></i>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <div class="text-center">
                            <p class="text-3xl font-bold text-gray-900 dark:text-white">94%</p>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Lead Quality Score</p>
                        </div>
                        <div class="grid grid-cols-2 gap-4 text-center">
                            <div>
                                <p class="text-xl font-semibold text-green-600">+23%</p>
                                <p class="text-xs text-gray-600 dark:text-gray-400">This Month</p>
                            </div>
                            <div>
                                <p class="text-xl font-semibold text-blue-600">156</p>
                                <p class="text-xs text-gray-600 dark:text-gray-400">Total Leads</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>


    <!-- CTA Section -->
    <section class="py-20 bg-[#1E3E62] relative">
        <div class="absolute inset-0 bg-black opacity-20"></div>
        <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center max-w-4xl mx-auto">
                <h2 class="text-4xl sm:text-5xl font-bold mb-6" data-aos="fade-up">
                    Ready to Transform Your Lead Management?
                </h2>
                <p class="text-xl text-gray-200 mb-8" data-aos="fade-up" data-aos-delay="200">
                    Join thousands of businesses already using our advanced CRM to convert more leads into customers
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center" data-aos="fade-up" data-aos-delay="400">
                    <button
                        class="px-10 py-4 border-2 border-white text-white font-semibold rounded-full hover:bg-white hover:text-purple-600 transition-all duration-300 text-lg">
                        Get Started Free
                    </button>
                    <button
                        class="px-10 py-4 border-2 border-white text-white font-semibold rounded-full hover:bg-white hover:text-purple-600 transition-all duration-300 text-lg">
                        Schedule Demo
                    </button>
                </div>
            </div>
        </div>
    </section>
    <!--What our customer say-->
    <section class="py-20 bg-gray-50 ">
        <div class="max-w-7xl mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">What Our Users Say</h2>
                <p class="text-xl text-gray-600 dark:text-gray-400">Hear from our satisfied customers</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Testimonial 1 -->
                <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg card-hover">
                    <div class="flex items-center mb-4">
                        <img src="https://www.shutterstock.com/image-photo/portrait-young-investor-banker-workplace-260nw-**********.jpg"
                            alt="User 1" class="w-12 h-12 rounded-full mr-3">
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900 dark:text-white">John Doe</h4>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Sales Manager at Company X</p>
                        </div>
                    </div>
                    <p class="text-gray-700 dark:text-gray-300">"LeadFlow has transformed our lead management process.
                        The AI scoring and automated workflows have saved us countless hours and significantly increased
                        our conversion rates."</p>
                </div>
                <!-- Testimonial 2 -->
                <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg card-hover">
                    <div class="flex items-center mb-4">
                        <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSkny9A2w--AZTnQLBTK6NorT4syplXv3JBMgvOTTEYUPTOlWOJjWZp-3ENUpiBh6mY1f0&usqp=CAU"
                            alt="User 2" class="w-12 h-12 rounded-full mr-3">
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900 dark:text-white">Jane Smith</h4>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Marketing Director at Company Y</p>
                        </div>
                    </div>
                    <p class="text-gray-700 dark:text-gray-300">"The dashboard is incredibly intuitive and provides all
                        the insights we need to optimize our campaigns. The integration with our existing tools was
                        seamless."</p>
                </div>
                <!-- Testimonial 3 -->
                <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg card-hover">
                    <div class="flex items-center mb-4">
                        <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSS-Og8lScR6f_JoaXPx7EeLAO_kQkzqxz1kHiRT99tAYDofSY7yePnc5UURgRf7If4p6c&usqp=CAU"
                            alt="User 3" class="w-12 h-12 rounded-full mr-3">
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900 dark:text-white">Michael Brown</h4>
                            <p class="text-sm text-gray-600 dark:text-gray-400">CEO at Company Z</p>
                        </div>
                    </div>
                    <p class="text-gray-700 dark:text-gray-300">"LeadFlow has been a game-changer for our sales team.
                        The ability to track leads in real-time and automate follow-ups has significantly improved our
                        efficiency."</p>
                </div>
            </div>
        </div>
    </section>
    <footer class="bg-black text-white px-4 sm:px-6 lg:px-10 w-full mt-1">
    <div class="max-w-7xl mx-auto">
      <div class=" py-8 grid grid-cols-1 md:grid-cols-4 lg:grid-cols-4 gap-8">
        <!-- Logo & Description -->
        <div class="flex flex-col items-start lg:items-start space-y-4">
          <img src="images/logo.png" alt="Logo" class="w-32 sm:w-40 h-auto bg-white" />
          <p class="text-gray-400 text-sm max-w-sm">
            Effortless payroll, seamless success – simplify your pay process today.
          </p>
          <div class="flex gap-4">
            <!-- Social Icons -->
            <!-- Facebook -->
            <a href="#" class="text-gray-400 hover:text-white transition">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path
                  d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"" />
            </svg>
          </a>
          <!-- Twitter -->
          <a href=" #" class="text-gray-400 hover:text-white transition">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path
                      d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"" />
            </svg>
          </a>
          <!-- Instagram -->
          <a href=" #" class="text-gray-400 hover:text-white transition">
                      <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path
                          d="M7.75 2h8.5A5.75 5.75 0 0122 7.75v8.5A5.75 5.75 0 0116.25 22h-8.5A5.75 5.75 0 012 16.25v-8.5A5.75 5.75 0 017.75 2zm0 1.5A4.25 4.25 0 003.5 7.75v8.5A4.25 4.25 0 007.75 20.5h8.5a4.25 4.25 0 004.25-4.25v-8.5A4.25 4.25 0 0016.25 3.5h-8.5zm8.75 2a1 1 0 110 2 1 1 0 010-2zM12 7a5 5 0 110 10 5 5 0 010-10zm0 1.5a3.5 3.5 0 100 7 3.5 3.5 0 000-7z" " />
            </svg>
          </a>
          <!-- LinkedIn -->
          <a href=" #" class="text-gray-400 hover:text-white transition">
                          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                            <path
                              d="M4.98 3.5C4.98 4.61 4.1 5.5 3 5.5S1.02 4.61 1.02 3.5 1.9 1.5 3 1.5 4.98 2.39 4.98 3.5zM.5 6h5V20h-5V6zm7.5 0h4.7v1.785h.066c.655-1.24 2.255-2.535 4.634-2.535 4.953 0 5.867 3.26 5.867 7.5V20h-5v-6.417c0-1.531-.027-3.5-2.134-3.5-2.138 0-2.466 1.668-2.466 3.39V20h-5V6z" " />
            </svg>
          </a>
        </div>
      </div>

      <!-- Quick Links -->
      <div class=" space-y-4">
                              <h3 class="text-lg font-semibold">Quick Links</h3>
                              <ul class="space-y-2 text-gray-400 text-sm">
                                <li><a href="index.html" class="hover:text-white">Home</a></li>
                                <li><a href="about.html" class="hover:text-white">About Us</a></li>
                                <li><a href="products.html" class="hover:text-white">Products</a></li>
                                <li><a href="contact.html" class="hover:text-white">Contactus</a></li>
                                <li><a href="login.html" class="hover:text-white">Login</a></li>
                                
                              </ul>
          </div>

          <!-- Products -->
          <div class="space-y-4">
            <h3 class="text-lg font-semibold">Products</h3>
            <ul class="space-y-2 text-gray-400 text-sm">
              <li><a href="leadmanagement.html" class="hover:text-white">Lead Management</a></li>
              <li><a href="salemanagement.html" class="hover:text-white">Sales Management</a></li>
              <li><a href="analytics.html" class="hover:text-white">Advance Analytics</a></li>
              <li><a href="customersupport.html" class="hover:text-white">Customer Management</a></li>
              <li><a href="integration.html" class="hover:text-white">Integration</a></li>
            </ul>
          </div>

          <!-- Contact -->
          <div class="space-y-4">
            <h3 class="text-lg font-semibold">Contact</h3>
            <ul class="space-y-3 text-gray-400 text-sm">
              <li class="flex items-center gap-2">
                <svg class="w-4 h-4" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                <a href="mailto:<EMAIL>" class="hover:text-white"><EMAIL></a>
              </li>
              <li class="flex items-center gap-2">
                <svg class="w-4 h-4" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
                +1 (555) 482-9316
              </li>
              <li class="flex items-start gap-2">
                <svg class="w-4 h-4" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <span>
                  1234 Elm Street, Suite 567,<br />
                  Springfield, IL 62701, USA
                </span>
              </li>
            </ul>
          </div>
        </div>

        <!-- Bottom -->
        <div
          class="border-t border-gray-800  py-3 sm:text-left flex flex-col sm:flex-row justify-between items-center text-gray-400 text-sm gap-4">
          <p>© 2025 LMS. All rights reserved.</p>
          <div class="flex gap-4">
            <a href="#" class="hover:text-white">Terms & Conditions</a>
            <a href="#" class="hover:text-white">Privacy Policy</a>
          </div>
        </div>
      </div>
  </footer>


    <!-- Alpine.js for mobile menu -->

    <script>
        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true,
            offset: 100
        });

        // Add smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
    <script src="https://unpkg.com/remixicon/fonts/remixicon.css"></script>
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        const animatedCards = document.querySelectorAll(".animated-card");

        const cardObserver = new IntersectionObserver((entries) => {
            entries.forEach((entry) => {
                if (entry.isIntersecting) {
                    entry.target.classList.add("visible");
                    cardObserver.unobserve(entry.target); // Only animate once
                }
            });
        }, {
            threshold: 0.1,
        });

        animatedCards.forEach((card) => cardObserver.observe(card));


        const cards = document.querySelectorAll(".stat-card");

        const observer = new IntersectionObserver(entries => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add("visible");
                    observer.unobserve(entry.target); // Animate only once
                }
            });
        }, {
            threshold: 0.1,
        });

        cards.forEach(card => {
            observer.observe(card);
        });
        document.querySelectorAll('.toggle-btn').forEach(button => {
            button.addEventListener('click', () => {
                const targetId = button.getAttribute('data-target');
                const content = document.getElementById(targetId);
                const plusIcon = button.querySelector('.plus-icon path');

                if (content.classList.contains('hidden')) {
                    content.classList.remove('hidden');
                    // Change plus to minus (rotate + hide vertical stroke)
                    button.querySelector('.plus-icon').innerHTML = `
                <path stroke-linecap="round" stroke-linejoin="round" d="M6 12h12"></path>`;
                } else {
                    content.classList.add('hidden');
                    // revert back to plus
                    button.querySelector('.plus-icon').innerHTML = `
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v12m6-6H6"></path>
            `;
                }
            });
        });

        // Animate counting up function
        function animateCount(id, target, duration = 2000) {
            const el = document.getElementById(id);
            let start = 0;
            const isK = typeof target === 'string' && target.includes('K');
            const isPlus = typeof target === 'string' && target.includes('+');

            // Extract number value, e.g. "24K+" => 24
            const targetNum = typeof target === 'string'
                ? parseFloat(target.replace(/[^\d.]/g, ''))
                : target;

            const stepTime = Math.abs(Math.floor(duration / targetNum));
            const increment = targetNum / (duration / stepTime);

            let current = 0;
            const interval = setInterval(() => {
                current += increment;
                if (current >= targetNum) {
                    clearInterval(interval);
                    el.textContent = target;
                } else {
                    let displayVal = Math.floor(current);
                    if (isK) displayVal += 'K';
                    if (isPlus) displayVal += '+';
                    el.textContent = displayVal;
                }
            }, stepTime);
        }

        // On page load, animate all counters
        window.addEventListener('DOMContentLoaded', () => {
            animateCount('counter1', '140+');
            animateCount('counter2', '24K+');
            animateCount('counter3', '8+');
        });


        AOS.init({
            duration: 1000,
            once: true
        });

        // Simple animation for elements
        document.addEventListener('DOMContentLoaded', function () {
            // Add animation delay to stagger the animations
            const animatedElements = document.querySelectorAll('.animate-fade-in');
            animatedElements.forEach((el, index) => {
                el.style.opacity = '0';
                setTimeout(() => {
                    el.style.opacity = '1';
                }, 100 * index);
            });

            // Smooth scroll for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href');
                    if (targetId === '#') return;

                    const targetElement = document.querySelector(targetId);
                    if (targetElement) {
                        window.scrollTo({
                            top: targetElement.offsetTop - 100,
                            behavior: 'smooth'
                        });
                    }
                });
            });

            // Simple slider logic
            const slides = document.querySelectorAll('.testimonial-slide');
            let current = 0;

            function showSlide(idx) {
                slides.forEach((slide, i) => {
                    slide.classList.toggle('hidden', i !== idx);
                });
            }

            document.getElementById('prev-slide').onclick = function () {
                current = (current - 1 + slides.length) % slides.length;
                showSlide(current);
            };
            document.getElementById('next-slide').onclick = function () {
                current = (current + 1) % slides.length;
                showSlide(current);
            };
        });

        window.addEventListener('load', function () {
            setTimeout(function () {
                openModal();
            }, 1000);
        });

        function openModal() {
            document.getElementById('contactModal').classList.add('show');
            document.body.style.overflow = 'hidden';
        }

        function closeModal() {
            document.getElementById('contactModal').classList.remove('show');
            document.body.style.overflow = 'auto';
        }

        // Close modal when clicking outside
        document.getElementById('contactModal').addEventListener('click', function (e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js"></script>
    <script>
        AOS.init();
    </script>
</body>

</html>