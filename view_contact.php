<?php
// Get database connection using reliable method
$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';

// Check if database is set up first
require_once 'check_database_setup.php';
redirect_to_setup_if_needed();

// Check if the user is logged in
ensure_session_started();
require_login();

// Get contact ID from URL
$contact_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($contact_id <= 0) {
    header("Location: contacts.php");
    exit;
}

// Get contact data with related information
$sql = "SELECT c.*,
        CONCAT(COALESCE(c.first_name, ''), ' ', COALESCE(c.last_name, '')) as full_name,
        CONCAT(COALESCE(u.first_name, ''), ' ', COALESCE(u.last_name, '')) as assigned_to_name,
        CONCAT(COALESCE(u2.first_name, ''), ' ', COALESCE(u2.last_name, '')) as created_by_name
        FROM contacts c
        LEFT JOIN employees u ON c.assigned_to = u.id
        LEFT JOIN employees u2 ON c.created_by = u2.id
        WHERE c.id = ?";

$stmt = mysqli_prepare($conn, $sql);
mysqli_stmt_bind_param($stmt, "i", $contact_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) == 0) {
    header("Location: contacts.php?error=Contact not found");
    exit;
}

$contact = mysqli_fetch_assoc($result);

// Get product interests from customer_interest field
$product_interests_text = $contact['customer_interest'];

// Get sales history for this contact
$sales_sql = "SELECT s.*, p.name as product_name 
              FROM sales s
              LEFT JOIN products p ON s.product_id = p.id
              WHERE s.contact_id = ?
              ORDER BY s.sale_date DESC";
$sales_stmt = mysqli_prepare($conn, $sales_sql);
mysqli_stmt_bind_param($sales_stmt, "i", $contact_id);
mysqli_stmt_execute($sales_stmt);
$sales_result = mysqli_stmt_get_result($sales_stmt);

// Get total sales amount
$total_sales_sql = "SELECT COUNT(*) as count, COALESCE(SUM(total_amount), 0) as total 
                    FROM sales WHERE contact_id = ?";
$total_stmt = mysqli_prepare($conn, $total_sales_sql);
mysqli_stmt_bind_param($total_stmt, "i", $contact_id);
mysqli_stmt_execute($total_stmt);
$total_result = mysqli_stmt_get_result($total_stmt);
$total_data = mysqli_fetch_assoc($total_result);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="img/minilogo.jpg" type="image/x-icon">
    <title>View Contact - CRM System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .text-color { color: #FF6500; }
        .bg-color { background-color: #FF6500; }
    </style>
</head>
<body class="bg-gray-100">

<?php include 'includes/navigation.php'; ?>

<!-- Main Content -->
<div class="ml-0 min-[870px]:ml-60 min-h-screen flex flex-col">
    <header class="bg-[#1E3E62] p-4 flex justify-between z-10 flex-col">
        <h1 class="text-2xl font-bold text-white mx-10 my-2">Contact Details</h1>
            </header>
    
    <main class="flex-1 p-10">
        <?php if (isset($_GET['success'])): ?>
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
            Contact created successfully!
        </div>
        <?php endif; ?>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Contact Information -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow p-6 mb-6">
                    <div class="flex justify-between items-start mb-6">
                        <h2 class="text-xl font-semibold text-gray-900">Contact Information</h2>
                        <div class="flex space-x-2">
                            <?php if (isset($_SESSION['role']) && $_SESSION['role'] !== 'sales_rep'): ?>
                            <a href="edit_contact.php?id=<?php echo $contact['id']; ?>"
                               class="px-4 py-2 bg-[#0b192c] text-white rounded-sm hover:bg-[#1e3e62]" title="Edit Contact">
                                <i class="fas fa-edit"></i>
                            </a>
                            <?php endif; ?>
                            
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Basic Information -->
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Full Name</label>
                            <p class="text-lg text-gray-900"><?php echo htmlspecialchars($contact['full_name'] ?? 'No Name Provided'); ?></p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-600">Email</label>
                            <p class="text-lg text-gray-900">
                                <a href="mailto:<?php echo htmlspecialchars($contact['email']); ?>" 
                                   class="text-blue-600 hover:text-blue-800">
                                    <?php echo htmlspecialchars($contact['email']); ?>
                                </a>
                            </p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-600">Phone</label>
                            <p class="text-lg text-gray-900">
                                <?php if (!empty($contact['phone'])): ?>
                                <a href="tel:<?php echo htmlspecialchars($contact['phone']); ?>" 
                                   class="text-blue-600 hover:text-blue-800">
                                    <?php echo htmlspecialchars($contact['phone']); ?>
                                </a>
                                <?php else: ?>
                                <span class="text-gray-500">Not provided</span>
                                <?php endif; ?>
                            </p>
                        </div>

                        <!-- Address Information -->
                        <?php if (!empty($contact['address'])): ?>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-600">Address</label>
                            <p class="text-lg text-gray-900"><?php echo nl2br(htmlspecialchars($contact['address'])); ?></p>
                        </div>
                        <?php endif; ?>



                        <?php if (!empty($contact['category'])): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Category</label>
                            <p class="text-lg text-gray-900"><?php echo htmlspecialchars($contact['category']); ?></p>
                        </div>
                        <?php endif; ?>

                        <!-- Management Information -->
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Assigned To</label>
                            <p class="text-lg text-gray-900"><?php echo htmlspecialchars($contact['assigned_to_name'] ?? 'Unassigned'); ?></p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-600">Created By</label>
                            <p class="text-lg text-gray-900"><?php echo htmlspecialchars($contact['created_by_name'] ?? 'Unknown'); ?></p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-600">Created Date</label>
                            <p class="text-lg text-gray-900"><?php echo date('M j, Y g:i A', strtotime($contact['created_at'])); ?></p>
                        </div>
                        
                        <!-- Notes Section -->
                        <?php if (!empty($contact['notes'])): ?>
                        <div class="md:col-span-2 mt-4">
                            <label class="block text-sm font-medium text-gray-600 mb-2">Notes</label>
                            <div class="bg-gray-50 p-4 rounded-md">
                                <p class="text-gray-800"><?php echo nl2br(htmlspecialchars($contact['notes'])); ?></p>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <?php if (!empty($product_interests_text)): ?>
                    <div class="mt-6">
                        <label class="block text-sm font-medium text-gray-600 mb-2">Product Interests</label>
                        <div class="bg-gray-50 p-4 rounded-md">
                            <?php
                            $interests = array_map('trim', explode(',', $product_interests_text));
                            foreach ($interests as $index => $interest): ?>
                                <div class="flex items-center py-2 border-b border-gray-200 last:border-b-0">
                                    <span class="bg-[#0b192c] text-white text-xs font-medium px-2 py-1 rounded-full mr-3 min-w-[24px] text-center">
                                        <?php echo $index + 1; ?>
                                    </span>
                                    <span class="text-gray-800 font-medium">
                                        <?php echo htmlspecialchars($interest); ?>
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </main>
</div>

</body>
</html>
