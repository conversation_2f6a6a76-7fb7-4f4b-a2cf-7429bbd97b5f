<?php
$conn = require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/lead_conversion.php';

ensure_session_started();
require_any_role(["admin", "manager"]);

header('Content-Type: application/json');

// Initialize response
$response = [
    'success' => false,
    'message' => '',
    'data' => []
];

// bulk actions
if ($_SERVER["REQUEST_METHOD"] == "POST") {

    $json_data = file_get_contents('php://input');
    $data = json_decode($json_data, true);
    
    // if leads are selected
    if (isset($data['selected_leads']) && is_array($data['selected_leads']) && !empty($data['selected_leads'])) {
        $selected_leads = array_map('intval', $data['selected_leads']);
        $lead_ids = implode(',', $selected_leads);
        
        // Check which action to perform
        if (isset($data['action']) && !empty($data['action'])) {
            $action = $data['action'];
            
            switch ($action) {
                case 'delete':
                    // Delete leads
                    $sql = "DELETE FROM leads WHERE id IN ($lead_ids)";
                    if (mysqli_query($conn, $sql)) {
                        $affected_rows = mysqli_affected_rows($conn);
                        $response['success'] = true;
                        $response['message'] = "$affected_rows leads have been deleted.";
                        $response['data'] = [
                            'deleted_ids' => $selected_leads,
                            'count' => $affected_rows
                        ];
                    } else {
                        $response['message'] = "Error deleting leads: " . mysqli_error($conn);
                    }
                    break;
                    
                case 'assign':
                    // Assign leads to a sales rep
                    if (isset($data['assign_to_user_id'])) {
                        $user_id = $data['assign_to_user_id'];
                        
                        if ($user_id === 'unassigned') {
                            // Set assigned_to to NULL
                            $sql = "UPDATE leads SET assigned_to = NULL WHERE id IN ($lead_ids)";
                            if (mysqli_query($conn, $sql)) {
                                $affected_rows = mysqli_affected_rows($conn);
                                $response['success'] = true;
                                $response['message'] = "$affected_rows leads have been unassigned.";
                                $response['data'] = [
                                    'updated_ids' => $selected_leads,
                                    'count' => $affected_rows
                                ];
                            } else {
                                $response['message'] = "Error updating leads: " . mysqli_error($conn);
                            }
                        } else {
                            // Assign to specific user
                            $user_id = intval($user_id);
                            
                            // Verify that the user exists and is a sales rep also
                            $user_check_sql = "SELECT id, CONCAT(first_name, ' ', last_name) as name FROM employees WHERE id = ? AND role = 'sales_rep'";
                            $stmt = mysqli_prepare($conn, $user_check_sql);
                            mysqli_stmt_bind_param($stmt, "i", $user_id);
                            mysqli_stmt_execute($stmt);
                            $result = mysqli_stmt_get_result($stmt);
                            
                            if ($user = mysqli_fetch_assoc($result)) {
                                $sql = "UPDATE leads SET assigned_to = ? WHERE id IN ($lead_ids)";
                                $update_stmt = mysqli_prepare($conn, $sql);
                                mysqli_stmt_bind_param($update_stmt, "i", $user_id);
                                
                                if (mysqli_stmt_execute($update_stmt)) {
                                    $affected_rows = mysqli_stmt_affected_rows($update_stmt);
                                    $response['success'] = true;
                                    $response['message'] = "$affected_rows leads have been assigned to " . $user['name'] . ".";
                                    $response['data'] = [
                                        'updated_ids' => $selected_leads,
                                        'count' => $affected_rows,
                                        'assigned_to' => [
                                            'id' => $user_id,
                                            'name' => $user['name']
                                        ]
                                    ];
                                } else {
                                    $response['message'] = "Error updating leads: " . mysqli_error($conn);
                                }
                            } else {
                                $response['message'] = "Invalid sales representative selected.";
                            }
                        }
                    } else {
                        $response['message'] = "No sales representative selected for assignment.";
                    }
                    break;
                    
                case 'change_status':
                    // Change lead status
                    if (isset($data['change_to_status']) && !empty($data['change_to_status'])) {
                        $status = mysqli_real_escape_string($conn, $data['change_to_status']);
                        
                        // Verify the status is valid
                        $valid_statuses = ['new', 'contacted', 'qualified', 'lost', 'converted'];
                        if (in_array($status, $valid_statuses)) {
                            // Special handling for 'converted' status
                            if ($status === 'converted') {
                                $converted_leads = [];
                                $converted_customers = [];
                                $failed_conversions = [];
                                
                                // Process each lead individually for conversion
                                foreach ($selected_leads as $lead_id) {
                                    $conversion_result = convert_lead_to_customer($conn, $lead_id);
                                    
                                    if ($conversion_result['success']) {
                                        $converted_leads[] = $lead_id;
                                        $converted_customers[] = [
                                            'lead_id' => $lead_id,
                                            'customer_id' => $conversion_result['customer_id']
                                        ];
                                    } else {
                                        $failed_conversions[] = [
                                            'lead_id' => $lead_id,
                                            'error' => $conversion_result['message']
                                        ];
                                    }
                                }
                                
                                $converted_count = count($converted_leads);
                                $failed_count = count($failed_conversions);
                                
                                if ($converted_count > 0) {
                                    $response['success'] = true;
                                    $response['message'] = "$converted_count leads have been converted to customers.";
                                    if ($failed_count > 0) {
                                        $response['message'] .= " $failed_count leads failed to convert.";
                                    }
                                    $response['data'] = [
                                        'converted_leads' => $converted_leads,
                                        'converted_customers' => $converted_customers,
                                        'failed_conversions' => $failed_conversions,
                                        'count' => $converted_count,
                                        'new_status' => $status
                                    ];
                                } else {
                                    $response['success'] = false;
                                    $response['message'] = "Failed to convert any leads to customers.";
                                    $response['data'] = [
                                        'failed_conversions' => $failed_conversions
                                    ];
                                }
                            } else {
                                // Normal status update for non-converted statuses
                                $sql = "UPDATE leads SET status = ? WHERE id IN ($lead_ids)";
                                $stmt = mysqli_prepare($conn, $sql);
                                mysqli_stmt_bind_param($stmt, "s", $status);
                                
                                if (mysqli_stmt_execute($stmt)) {
                                    $affected_rows = mysqli_stmt_affected_rows($stmt);
                                    $response['success'] = true;
                                    $response['message'] = "$affected_rows leads have been updated to status: " . ucfirst($status);
                                    $response['data'] = [
                                        'updated_ids' => $selected_leads,
                                        'count' => $affected_rows,
                                        'new_status' => $status
                                    ];
                                } else {
                                    $response['message'] = "Error updating leads: " . mysqli_error($conn);
                                }
                            }
                        } else {
                            $response['message'] = "Invalid status selected.";
                        }
                    } else {
                        $response['message'] = "No status selected.";
                    }
                    break;
                    
                default:
                    $response['message'] = "Invalid action selected.";
                    break;
            }
        } else {
            $response['message'] = "No action selected.";
        }
    } else {
        $response['message'] = "No leads selected.";
    }
} else {
    $response['message'] = "Invalid request method.";
}

// Return JSON response
echo json_encode($response);
exit;
?>
