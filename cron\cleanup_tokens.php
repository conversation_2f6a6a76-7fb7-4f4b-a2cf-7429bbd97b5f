<?php

// Include database configuration
$conn = require_once '../config/database.php';
require_once '../includes/functions.php';

// Get current date and time
$current_datetime = date('Y-m-d H:i:s');

// Find and clear expired tokens
$sql = "UPDATE employees SET remember_token = NULL, token_expiry = NULL WHERE token_expiry IS NOT NULL AND token_expiry < ?";
$stmt = mysqli_prepare($conn, $sql);
mysqli_stmt_bind_param($stmt, "s", $current_datetime);
mysqli_stmt_execute($stmt);

$affected_rows = mysqli_stmt_affected_rows($stmt);
mysqli_stmt_close($stmt);

// Log the result
$log_message = date('Y-m-d H:i:s') . " - Cleaned up $affected_rows expired tokens\n";
file_put_contents(__DIR__ . '/token_cleanup.log', $log_message, FILE_APPEND);

echo "Token cleanup completed. $affected_rows expired tokens removed.\n";
?>
