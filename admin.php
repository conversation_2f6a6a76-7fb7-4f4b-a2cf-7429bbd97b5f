<?php
// Get database connection using reliable method
$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';

// Check if database is set up first
require_once 'check_database_setup.php';
redirect_to_setup_if_needed();

// Check if the user is logged in and has admin role
ensure_session_started();
require_any_role(["admin", "manager"]);

// Check if a sales rep ID is provided
$selected_sales_rep_id = isset($_GET['sales_rep_id']) ? intval($_GET['sales_rep_id']) : null;
$selected_sales_rep_name = '';

if ($selected_sales_rep_id) {
    // Get the sales rep name
    $sales_rep_query = "SELECT CONCAT(first_name, ' ', last_name) as name FROM employees WHERE id = ? AND role = 'sales_rep'";
    $sales_rep_stmt = mysqli_prepare($conn, $sales_rep_query);
    mysqli_stmt_bind_param($sales_rep_stmt, "i", $selected_sales_rep_id);
    mysqli_stmt_execute($sales_rep_stmt);
    $sales_rep_result = mysqli_stmt_get_result($sales_rep_stmt);
    
    if ($sales_rep_row = mysqli_fetch_assoc($sales_rep_result)) {
        $selected_sales_rep_name = $sales_rep_row['name'];
    } else {
        // Invalid sales rep ID, reset to null
        $selected_sales_rep_id = null;
    }
}

// Get all sales reps for the dropdown
$sales_reps_query = "SELECT id, CONCAT(first_name, ' ', last_name) as name FROM employees WHERE role = 'sales_rep' ORDER BY first_name, last_name";
$sales_reps_result = mysqli_query($conn, $sales_reps_query);
$sales_reps = [];
while ($row = mysqli_fetch_assoc($sales_reps_result)) {
    $sales_reps[] = $row;
}

// Get dashboard statistics
if ($selected_sales_rep_id) {
    // Get statistics for the selected sales rep
    $total_sales_query = "SELECT COUNT(*) as total FROM sales WHERE sales_rep_id = ?";
    $total_sales_stmt = mysqli_prepare($conn, $total_sales_query);
    mysqli_stmt_bind_param($total_sales_stmt, "i", $selected_sales_rep_id);
    mysqli_stmt_execute($total_sales_stmt);
    $total_sales_result = mysqli_stmt_get_result($total_sales_stmt);
    $total_sales_row = mysqli_fetch_assoc($total_sales_result);
    $total_sales = $total_sales_row['total'];
    
    $total_revenue_query = "SELECT COALESCE(SUM(total_amount), 0) as total FROM sales WHERE sales_rep_id = ?";
    $total_revenue_stmt = mysqli_prepare($conn, $total_revenue_query);
    mysqli_stmt_bind_param($total_revenue_stmt, "i", $selected_sales_rep_id);
    mysqli_stmt_execute($total_revenue_stmt);
    $total_revenue_result = mysqli_stmt_get_result($total_revenue_stmt);
    $total_revenue_row = mysqli_fetch_assoc($total_revenue_result);
    $total_revenue = format_currency($total_revenue_row['total']);
    


    // Count customers by checking if they have sales records (indicating they are customers)
    $total_converted_query = "SELECT COUNT(DISTINCT c.id) as total FROM contacts c
                             INNER JOIN sales s ON c.id = s.contact_id
                             WHERE c.assigned_to = ?";
    $total_converted_stmt = mysqli_prepare($conn, $total_converted_query);
    mysqli_stmt_bind_param($total_converted_stmt, "i", $selected_sales_rep_id);
    mysqli_stmt_execute($total_converted_stmt);
    $total_converted_result = mysqli_stmt_get_result($total_converted_stmt);
    $total_converted_row = mysqli_fetch_assoc($total_converted_result);
    $total_converted = $total_converted_row['total'];

    // Get total assigned contacts for this sales rep (for performance section)
    $total_assigned_query = "SELECT COUNT(*) as total FROM contacts WHERE assigned_to = ?";
    $total_assigned_stmt = mysqli_prepare($conn, $total_assigned_query);
    mysqli_stmt_bind_param($total_assigned_stmt, "i", $selected_sales_rep_id);
    mysqli_stmt_execute($total_assigned_stmt);
    $total_assigned_result = mysqli_stmt_get_result($total_assigned_stmt);
    $total_assigned_row = mysqli_fetch_assoc($total_assigned_result);
    $total_assigned_contacts = $total_assigned_row['total'];
    
    // Get contacts managed by this sales rep (both leads and customers)
    $contacts_query = "SELECT * FROM contacts WHERE assigned_to = ? OR created_by = ? ORDER BY first_name, last_name";
    $contacts_stmt = mysqli_prepare($conn, $contacts_query);
    mysqli_stmt_bind_param($contacts_stmt, "ii", $selected_sales_rep_id, $selected_sales_rep_id);
    mysqli_stmt_execute($contacts_stmt);
    $contacts_result = mysqli_stmt_get_result($contacts_stmt);
    $contacts_count = mysqli_num_rows($contacts_result);
    
    // Get customers for this sales rep (contacts who have made purchases)
    $customers_query = "
        SELECT DISTINCT c.* FROM contacts c
        INNER JOIN sales s ON c.id = s.contact_id
        WHERE (c.created_by = ? OR c.assigned_to = ? OR s.sales_rep_id = ?)
        ORDER BY c.first_name, c.last_name
    ";
    $customers_stmt = mysqli_prepare($conn, $customers_query);
    mysqli_stmt_bind_param($customers_stmt, "iii", $selected_sales_rep_id, $selected_sales_rep_id, $selected_sales_rep_id);
    mysqli_stmt_execute($customers_stmt);
    $customers_result = mysqli_stmt_get_result($customers_stmt);

    // Count for debugging
    $customers_count = mysqli_num_rows($customers_result);
} else {
    // Get overall statistics
    $total_sales_query = "SELECT COUNT(*) as total FROM sales";
    $total_sales_result = mysqli_query($conn, $total_sales_query);
    $total_sales_row = mysqli_fetch_assoc($total_sales_result);
    $total_sales = $total_sales_row['total'];
    
    $total_revenue_query = "SELECT COALESCE(SUM(total_amount), 0) as total FROM sales";
    $total_revenue_result = mysqli_query($conn, $total_revenue_query);
    $total_revenue_row = mysqli_fetch_assoc($total_revenue_result);
    $total_revenue = format_currency($total_revenue_row['total']);


    
    // Count total customers by checking if they have sales records
    $total_converted_query = "SELECT COUNT(DISTINCT c.id) as total FROM contacts c
                             INNER JOIN sales s ON c.id = s.contact_id";
    $total_converted_result = mysqli_query($conn, $total_converted_query);
    $total_converted_row = mysqli_fetch_assoc($total_converted_result);
    $total_converted = $total_converted_row['total'];
}

// Calculate conversion rate based on total contacts vs converted customers
$total_contacts_query = "SELECT COUNT(*) as total FROM contacts";
$total_contacts_result = mysqli_query($conn, $total_contacts_query);
$total_contacts_row = mysqli_fetch_assoc($total_contacts_result);
$total_contacts = $total_contacts_row['total'];

$conversion_rate = calculate_conversion_rate($total_contacts, $total_converted);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="img/minilogo.jpg" type="image/x-icon">
    <title>Admin Dashboard - CRM System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .text-color{ color: #FF6500; }
        .button-hover { transition: background-color 0.3s ease, transform 0.2s ease; }
        .button-hover:hover { background-color: #e55b00; transform: scale(1.05); }
    </style>
</head>
<body class="">
<?php include 'includes/navigation.php'; ?>

<!-- Inline navigation toggle script for immediate execution -->
<script>
// Immediate execution for menu toggle functionality
(function() {
    console.log("Running inline navigation script");
    
    // Get the toggle button and sidebar
    const toggleBtn = document.getElementById('menu-toggle');
    const sidebar = document.getElementById('sidebar');
    const closeButtons = document.querySelectorAll('.close-menu-btn');
    
    // Check if elements exist
    if (!toggleBtn || !sidebar) {
        console.error('Menu elements not found in inline script');
        return;
    }
    
    console.log("Toggle button:", toggleBtn);
    console.log("Sidebar:", sidebar);
    console.log("Close buttons:", closeButtons.length);
    
    // Function to open sidebar
    function openSidebar() {
        console.log("Opening sidebar");
        sidebar.classList.remove('-translate-x-full');
        toggleBtn.classList.add('hidden');
    }
    
    // Function to close sidebar
    function closeSidebar() {
        console.log("Closing sidebar");
        sidebar.classList.add('-translate-x-full');
        if (window.innerWidth < 870) {
            toggleBtn.classList.remove('hidden');
        }
    }
    
    // Add click event to toggle button
    toggleBtn.onclick = function(e) {
        e.preventDefault();
        e.stopPropagation();
        openSidebar();
    };
    
    // Add click events to all close buttons
    closeButtons.forEach(function(btn) {
        btn.onclick = function(e) {
            e.preventDefault();
            e.stopPropagation();
            closeSidebar();
        };
    });
    
    // Initial setup - IMPORTANT: This ensures the toggle button is hidden when sidebar is open
    if (window.innerWidth < 870) {
        console.log("Small screen detected");
        if (!sidebar.classList.contains('-translate-x-full')) {
            // If sidebar is open, hide the toggle button
            toggleBtn.classList.add('hidden');
            console.log("Sidebar is open, hiding toggle button");
        } else {
            // If sidebar is closed, show the toggle button
            toggleBtn.classList.remove('hidden');
            console.log("Sidebar is closed, showing toggle button");
        }
    } else {
        // On larger screens, always hide the toggle button
        toggleBtn.classList.add('hidden');
        console.log("Large screen, hiding toggle button");
    }
    
    // Force check after a short delay to ensure proper state
    setTimeout(function() {
        if (window.innerWidth < 870 && !sidebar.classList.contains('-translate-x-full')) {
            toggleBtn.classList.add('hidden');
            console.log("Delayed check: Sidebar open, hiding toggle");
        }
    }, 100);
})();
</script>
<!-- Main Content -->
<div class="ml-0 min-[870px]:ml-60 min-h-screen flex flex-col">
    <header class="bg-[#1E3E62] p-4 flex justify-between items-center z-10">
      <h1 class="text-2xl font-bold text-white mx-10 my-2">Admin/Management Dashboard</h1>
      <div class="mx-10">
        <?php include 'includes/top_notification.php'; ?>
      </div>
    </header>
    <main class="flex-1 overflow-y-auto space-y-10">
      <div class="flex-1">
        <!-- Analytics Cards -->
        <section class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-10 px-10" style="background: linear-gradient(to bottom, #1E3E62 60%, white 50%);">
          <div class="bg-white p-4 rounded-2xl shadow-xl h-[150px]">
            <img src="img/totalsales.png" alt="" class="w-10">
            <p class="text-sm text-gray-500">Number of Sales</p>
            <h2 id="admin-total-sales" class="text-xl font-bold mt-2 text-color"><?php echo $total_sales; ?></h2>
          </div>
          <div class="bg-white p-4 rounded-2xl shadow-xl h-[150px]">
            <img src="img/revenue.png" alt="" class="w-10">
            <p class="text-sm text-gray-500">Total Revenue</p>
            <h2 id="admin-total-revenue" class="text-xl font-bold mt-2 text-color"><?php echo $total_revenue; ?></h2>
          </div>
          <div class="bg-white p-4 rounded-2xl shadow-xl h-[150px]">
            <i class="fas fa-users text-3xl text-black mb-2"></i>
            <p class="text-sm text-gray-500">Total Contacts</p>
            <h2 id="admin-total-contacts" class="text-xl font-bold mt-2 text-color"><?php echo $total_contacts; ?></h2>
          </div>
          <div class="bg-white p-4 rounded-2xl shadow-xl h-[150px]">
            <i class="fas fa-chart-line text-3xl text-black mb-2"></i>
            <p class="text-sm text-gray-500">Conversion Rate</p>
            <h2 id="admin-conversion-rate" class="text-xl font-bold mt-2 text-color"><?php echo $conversion_rate; ?></h2>
          </div>
        </section>

        <!-- Bulk Contact Import -->
        <section class="px-10 mt-10">
          <div class="bg-white p-6 rounded-2xl shadow-xl">
            <div class="flex justify-between items-center mb-6">
              <h3 class="text-lg font-semibold text-color">Bulk Contact Import</h3>
              <a href="templates/contact_import_template.csv" download class="text-blue-600 hover:text-blue-800 text-sm">
                <i class="fas fa-download mr-1"></i> Download Template
              </a>
            </div>

            <form action="import_contacts.php" method="post" enctype="multipart/form-data" id="import-form" class="space-y-4">
    <div class="flex flex-row flex-wrap items-center gap-2 sm:gap-3 md:gap-4">
        <div class="flex-grow min-w-[150px] w-full sm:w-auto">
            <label for="contact_csv" class="block text-xs sm:text-sm font-medium text-gray-700 mb-1">Select CSV File</label>
            <input type="file" name="contact_csv" id="contact_csv" accept=".csv" class="block w-full text-xs sm:text-sm text-gray-500
                file:mr-3 sm:file:mr-4 file:py-1 sm:file:py-1.5 md:file:py-2 file:px-2 sm:file:px-3 md:file:px-4
                file:rounded-full file:border-0
                file:text-xs sm:file:text-sm file:font-semibold
                file:bg-blue-50 file:text-blue-700
                hover:file:bg-blue-100" required>
        </div>

        <div class="flex-shrink-0 w-full sm:w-40 md:w-48">
            <label for="assign_to" class="block text-xs sm:text-sm font-medium text-gray-700 mb-1">Assign To (Optional)</label>
            <select name="assign_to" id="assign_to" class="mt-1 block w-full py-1 sm:py-1.5 md:py-2 px-3 border border-gray-500 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-xs sm:text-sm">
                <option value="">Unassigned</option>
                <?php foreach ($sales_reps as $rep) : ?>
                    <option value="<?php echo $rep['id']; ?>"><?php echo htmlspecialchars($rep['name']); ?></option>
                <?php endforeach; ?>
            </select>
        </div>

        <div class="flex-shrink-0 w-full sm:w-auto self-end">
            <button type="submit" id="import-btn" disabled class="bg-gray-400 text-white px-2 sm:px-3 md:px-4 py-1 sm:py-1.5 md:py-2 rounded-sm cursor-not-allowed transition-colors text-xs sm:text-sm">
                <i class="fas fa-upload mr-1 sm:mr-2"></i> Import Contacts
            </button>
        </div>
    </div>

    <!-- Confirmation Checkbox -->
    <div class="flex items-start space-x-2 mt-3">
        <input type="checkbox" id="confirm-import" name="confirm_import" class="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
        <label for="confirm-import" class="text-sm text-gray-700">
            <span class="font-medium">I confirm that:</span>
            <ul class="mt-1 text-xs text-gray-600 list-disc list-inside space-y-1">
                <li>The CSV file format matches the template</li>
                <li>All phone numbers are valid Indian mobile numbers</li>
                <li>I have verified the data before uploading</li>
                <li>I understand that duplicate contacts will be skipped</li>
            </ul>
        </label>
    </div>
</form>

<!-- Upload Summary Box -->
<?php
// Get upload statistics for the current user
$current_user_id = $_SESSION['user_id'];
$current_user_name = $_SESSION['first_name'] . ' ' . $_SESSION['last_name'];

// Get total contacts uploaded by this user
$upload_stats_sql = "SELECT COUNT(*) as total_uploaded FROM contacts WHERE created_by = ?";
$upload_stats_stmt = mysqli_prepare($conn, $upload_stats_sql);
mysqli_stmt_bind_param($upload_stats_stmt, "i", $current_user_id);
mysqli_stmt_execute($upload_stats_stmt);
$upload_stats_result = mysqli_stmt_get_result($upload_stats_stmt);
$upload_stats = mysqli_fetch_assoc($upload_stats_result);

// Get recent upload activity (last 30 days)
$recent_uploads_sql = "SELECT COUNT(*) as recent_uploads FROM contacts WHERE created_by = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
$recent_uploads_stmt = mysqli_prepare($conn, $recent_uploads_sql);
mysqli_stmt_bind_param($recent_uploads_stmt, "i", $current_user_id);
mysqli_stmt_execute($recent_uploads_stmt);
$recent_uploads_result = mysqli_stmt_get_result($recent_uploads_stmt);
$recent_uploads = mysqli_fetch_assoc($recent_uploads_result);
?>

<div class="mt-6 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4">
    <h4 class="text-sm font-semibold text-blue-800 mb-3 flex items-center">
        <i class="fas fa-chart-bar mr-2"></i>Upload Summary
    </h4>
    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div class="bg-white rounded-md p-3 border border-blue-100">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-xs text-gray-600">Total Contacts Uploaded</p>
                    <p class="text-lg font-bold text-blue-600"><?php echo number_format($upload_stats['total_uploaded']); ?></p>
                </div>
                <div class="text-blue-400">
                    <i class="fas fa-users text-xl"></i>
                </div>
            </div>
        </div>
        <div class="bg-white rounded-md p-3 border border-blue-100">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-xs text-gray-600">Recent Uploads (30 days)</p>
                    <p class="text-lg font-bold text-green-600"><?php echo number_format($recent_uploads['recent_uploads']); ?></p>
                </div>
                <div class="text-green-400">
                    <i class="fas fa-upload text-xl"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="mt-3 pt-3 border-t border-blue-100">
        <p class="text-xs text-gray-600">
            <i class="fas fa-user mr-1"></i>
            <span class="font-medium">Responsible Person:</span>
            <?php echo htmlspecialchars($current_user_name); ?>
            <span class="ml-2 text-blue-600">(<?php echo ucfirst($_SESSION['role']); ?>)</span>
        </p>
    </div>
</div>

<style>
input[type="file"], button {
    transition: all 0.2s ease-in-out;
}
</style>

<script>
// Import form validation
document.addEventListener('DOMContentLoaded', function() {
    const importForm = document.getElementById('import-form');
    const importBtn = document.getElementById('import-btn');
    const confirmCheckbox = document.getElementById('confirm-import');
    const fileInput = document.getElementById('contact_csv');

    function updateImportButton() {
        const fileSelected = fileInput.files.length > 0;
        const checkboxChecked = confirmCheckbox.checked;

        if (fileSelected && checkboxChecked) {
            importBtn.disabled = false;
            importBtn.classList.remove('bg-gray-400', 'cursor-not-allowed');
            importBtn.classList.add('bg-[#0b192c]', 'hover:bg-[#1e3e62]');
        } else {
            importBtn.disabled = true;
            importBtn.classList.add('bg-gray-400', 'cursor-not-allowed');
            importBtn.classList.remove('bg-[#0b192c]', 'hover:bg-[#1e3e62]');
        }
    }

    // Event listeners
    fileInput.addEventListener('change', updateImportButton);
    confirmCheckbox.addEventListener('change', updateImportButton);

    // Form submission validation
    importForm.addEventListener('submit', function(e) {
        if (!confirmCheckbox.checked) {
            e.preventDefault();
            alert('Please confirm that you have verified the data before uploading.');
            return false;
        }

        if (fileInput.files.length === 0) {
            e.preventDefault();
            alert('Please select a CSV file to upload.');
            return false;
        }

        // Show loading state
        importBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1 sm:mr-2"></i> Importing...';
        importBtn.disabled = true;
    });
});
</script>
          </div>
        </section>
        
         <!-- Sales Representative Performance -->
      <section class="px-4 lg:px-10 mt-10 mb-20">
        <div class="bg-white p-6 rounded-2xl shadow-xl">
          <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4 mb-6">
            <h3 class="text-lg font-semibold text-color">Sales Representative Performance</h3>
            <form action="admin.php" method="GET" class="flex items-center space-x-4 w-full lg:w-auto">
              <label for="sales_rep_id" class="text-sm font-medium text-gray-700">Select Sales Representative:</label>
              <select name="sales_rep_id" id="sales_rep_id" class="w-full lg:w-auto rounded-md border border-gray-500 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 p-1" onchange="this.form.submit()">
                <option value="">Overall Statistics</option>
                <?php foreach ($sales_reps as $rep) : ?>
                  <option value="<?php echo $rep['id']; ?>" <?php echo ($selected_sales_rep_id == $rep['id']) ? 'selected' : ''; ?>>
                    <?php echo htmlspecialchars($rep['name']); ?>
                  </option>
                <?php endforeach; ?>
              </select>
            </form>
          </div>

          <?php if ($selected_sales_rep_id) : ?>
          <div class="mb-6">
            <h4 class="text-md font-medium text-gray-800 mb-2">Performance for <?php echo htmlspecialchars($selected_sales_rep_name); ?></h4>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div class="bg-blue-50 p-3 rounded-lg">
                <p class="text-sm text-gray-600">Assigned Contacts</p>
                <p class="text-xl font-bold text-blue-700"><?php echo $total_assigned_contacts; ?></p>
              </div>
              <div class="bg-green-50 p-3 rounded-lg">
                <p class="text-sm text-gray-600">Customers</p>
                <p class="text-xl font-bold text-green-700"><?php echo $total_converted; ?></p>
              </div>
              <div class="bg-purple-50 p-3 rounded-lg">
                <p class="text-sm text-gray-600">Conversion Rate</p>
                <p class="text-xl font-bold text-purple-700"><?php echo $conversion_rate; ?></p>
              </div>
              <div class="bg-orange-50 p-3 rounded-lg">
                <p class="text-sm text-gray-600">Total Sales</p>
                <p class="text-xl font-bold text-orange-700"><?php echo $total_sales; ?></p>
              </div>
            </div>
          </div>


          
          <!-- Customer Table -->
          <div class="mt-6">
            <h4 class="text-md font-medium text-gray-800 mb-2">Customers Managed by <?php echo htmlspecialchars($selected_sales_rep_name); ?></h4>
            <?php if (mysqli_num_rows($customers_result) > 0) : ?>
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200 text-sm">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left font-medium text-gray-500 uppercase">Name</th>
                    <th class="px-6 py-3 text-left font-medium text-gray-500 uppercase">Contact</th>
                    <th class="px-6 py-3 text-left font-medium text-gray-500 uppercase">ID Documents</th>
                    <th class="px-6 py-3 text-left font-medium text-gray-500 uppercase">Created</th>
                    <th class="px-6 py-3 text-right font-medium text-gray-500 uppercase">Actions</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <?php while ($row = mysqli_fetch_assoc($customers_result)) : ?>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap font-medium text-gray-900"><?php echo htmlspecialchars ($row['first_name'] . ' ' . $row['last_name']); ?></td>
                    <td class="px-6 py-4">
                      <div><?php echo htmlspecialchars($row['email']); ?></div>
                      <div class="text-gray-500"><?php echo htmlspecialchars($row['phone'] ?? 'N/A'); ?></div>
                    </td>
                    <td class="px-6 py-4">
                      <div>Aadhar: <?php echo empty($row['aadhar_card']) ? 'N/A' : htmlspecialchars($row['aadhar_card']); ?></div>
                      <div class="text-gray-500">PAN: <?php echo empty($row['pan_card']) ? 'N/A' : htmlspecialchars($row['pan_card']); ?></div>
                    </td>
                    <td class="px-6 py-4 text-gray-500"><?php echo date('d M, Y', strtotime($row['created_at'])); ?></td>
                    <td class="px-6 py-4 text-right">
                      <a href="view_contact.php?id=<?php echo $row['id']; ?>" class="text-blue-600 hover:text-blue-900 mx-2" title="View Contact"><i class="fas fa-eye"></i></a>
                    </td>
                  </tr>
                  <?php endwhile; ?>
                </tbody>
              </table>
            </div>
            <?php else : ?>
            <div class="bg-yellow-50 p-4 rounded-md">
              <p class="text-yellow-700">No customers found for this sales representative.</p>
            </div>
            <?php endif; ?>
          </div>
          <?php else : ?>
          <div class="bg-gray-50 p-6 rounded-md text-center">
          </div>
          <?php endif; ?>
        </div>
      </section>
   </div>
    </main>
</div>
<script>
// Chart.js examples (replace with real data)
new Chart(document.getElementById('currencyChart'), { 
    type: 'doughnut', 
    data: { 
        labels: ['INR', 'USD', 'EUR'], 
        datasets: [{ 
            data: [70, 20, 10], 
            backgroundColor: ['#FF6500', '#1E3E62', '#e5e5e5'] 
        }] 
    }, 
    options: { 
        responsive: true, 
        plugins: { 
            legend: { 
                position: 'bottom' 
            } 
        } 
    } 
});

new Chart(document.getElementById('taxChart'), { 
    type: 'bar', 
    data: { 
        labels: ['GST', 'VAT', 'Other'], 
        datasets: [{ 
            label: 'Tax Amount', 
            data: [12000, 5000, 1000], 
            backgroundColor: '#FF6500' 
        }] 
    }, 
    options: { 
        responsive: true, 
        plugins: { 
            legend: { 
                display: false 
            } 
        } 
    } 
});

const ctx = document.getElementById('buyersDensityChart').getContext('2d');
const buyersDensityChart = new Chart(ctx, {
    type: 'pie',
    data: {
        labels: ['Asia', 'Europe', 'America'],
        datasets: [{
            data: [40, 30, 30],
            backgroundColor: ['#FF6500', '#1E3E62', '#e5e5e5']
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});

new Chart(document.getElementById('leadsPerformanceChart'), { 
    type: 'line', 
    data: { 
        labels: ['Jan', 'Feb', 'Mar', 'Apr'], 
        datasets: [{ 
            label: 'Leads', 
            data: [300, 400, 350, 150], 
            borderColor: '#FF6500', 
            backgroundColor: 'rgba(255,101,0,0.1)', 
            fill: true 
        }] 
    }, 
    options: { 
        responsive: true, 
        plugins: { 
            legend: { 
                position: 'bottom' 
            } 
        } 
    } 
});
</script>
</body>
</html>
