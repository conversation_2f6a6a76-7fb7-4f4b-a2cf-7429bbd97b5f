<?php
// Top notification component for header
// This file should be included in the header section of pages

// Initialize session notifications if not set
if (!isset($_SESSION['notifications'])) {
    $_SESSION['notifications'] = [];
}

// Get unread notification count
$unread_count = 0;
if (isset($_SESSION['notifications'])) {
    foreach ($_SESSION['notifications'] as $notification) {
        if (!$notification['is_read']) {
            $unread_count++;
        }
    }
}

// Determine path prefix (same logic as navigation.php)
$script_path = $_SERVER['SCRIPT_NAME'];
$is_in_subfolder = strpos($script_path, '/auth/') !== false || strpos($script_path, '/includes/') !== false;
$prefix = $is_in_subfolder ? '../' : '';
?>

<!-- Top Notification Bell -->
<div class="relative">
    <button id="notification-bell" class="text-white hover:text-orange-300 transition-colors p-2">
        <i class="fas fa-bell text-xl"></i>
        <span id="notification-count" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center <?php echo $unread_count > 0 ? '' : 'hidden'; ?>">
            <?php echo $unread_count; ?>
        </span>
    </button>
    
    <!-- Notification Dropdown -->
    <div id="notification-dropdown" class="hidden absolute right-0 mt-2 w-96 bg-white rounded-md shadow-lg z-50 overflow-hidden border border-gray-200">
        <div class="px-4 py-3 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
            <h3 class="font-semibold text-gray-800">Notifications</h3>
            <button class="text-gray-500 hover:text-gray-700 text-sm" onclick="markAllAsRead()">
                <i class="fas fa-check-double mr-1"></i> Mark all as read
            </button>
        </div>
        <ul id="notification-list" class="max-h-80 overflow-y-auto">
            <?php if (empty($_SESSION['notifications'])): ?>
                <li class="py-3 px-4 text-gray-500 text-center">No notifications</li>
            <?php else: ?>
                <?php foreach (array_reverse($_SESSION['notifications']) as $index => $notification): ?>
                    <li class="border-b border-gray-100 last:border-0">
                        <div class="py-3 px-4 hover:bg-gray-50 transition-colors <?php echo $notification['is_read'] ? 'bg-white' : 'bg-blue-50'; ?>">
                            <div class="flex items-start space-x-3">
                                <div class="flex-shrink-0 mt-0.5">
                                    <i class="<?php
                                        switch($notification['type']) {
                                            case 'success': echo 'fas fa-check-circle text-green-500'; break;
                                            case 'warning': echo 'fas fa-exclamation-triangle text-yellow-500'; break;
                                            case 'error': echo 'fas fa-exclamation-circle text-red-500'; break;
                                            default: echo 'fas fa-info-circle text-blue-500';
                                        }
                                    ?>"></i>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="font-medium text-gray-900 text-sm">
                                        <?php echo htmlspecialchars($notification['title'] ?? 'Notification'); ?>
                                    </div>
                                    <div class="text-sm text-gray-600 mt-1">
                                        <?php echo htmlspecialchars($notification['message']); ?>
                                    </div>
                                    <div class="text-xs text-gray-400 mt-1">
                                        <?php echo date('M j, Y g:i A', strtotime($notification['created_at'])); ?>
                                    </div>
                                </div>
                                <?php if (!$notification['is_read']): ?>
                                    <div class="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </li>
                <?php endforeach; ?>
            <?php endif; ?>
        </ul>
        <div class="px-4 py-3 bg-gray-50 border-t border-gray-200 text-center">
            <a href="<?php echo $prefix; ?>notifications.php" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                View all notifications
            </a>
        </div>
    </div>
</div>

<!-- Session-based notifications script -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const notificationBell = document.getElementById('notification-bell');
    const notificationDropdown = document.getElementById('notification-dropdown');

    if (notificationBell && notificationDropdown) {
        // Toggle dropdown
        notificationBell.addEventListener('click', function(e) {
            e.preventDefault();
            notificationDropdown.classList.toggle('hidden');

            // Mark notifications as read when dropdown is opened
            if (!notificationDropdown.classList.contains('hidden')) {
                markNotificationsAsRead();
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!notificationBell.contains(e.target) && !notificationDropdown.contains(e.target)) {
                notificationDropdown.classList.add('hidden');
            }
        });
    }
});

function markAllAsRead() {
    // Send AJAX request to mark all as read
    fetch('<?php echo $prefix; ?>ajax/mark_notifications_read.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'mark_all_read' })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Reload page to update notification count
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

function markNotificationsAsRead() {
    // Send AJAX request to mark notifications as read when dropdown is opened
    fetch('<?php echo $prefix; ?>ajax/mark_notifications_read.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'mark_read_on_view' })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update notification count badge
            const countBadge = document.getElementById('notification-count');
            if (countBadge) {
                countBadge.classList.add('hidden');
            }

            // Update notification items to show as read
            const notificationItems = document.querySelectorAll('#notification-list li div');
            notificationItems.forEach(item => {
                item.classList.remove('bg-blue-50');
                item.classList.add('bg-white');
            });

            // Remove unread dots
            const unreadDots = document.querySelectorAll('#notification-list .bg-blue-500');
            unreadDots.forEach(dot => {
                dot.remove();
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}
</script>
