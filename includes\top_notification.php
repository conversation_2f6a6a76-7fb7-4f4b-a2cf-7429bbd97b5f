<?php
// Top notification component for header
// This file should be included in the header section of pages

// Get unread notification count (if not already set)
if (!isset($unread_count)) {
    $unread_count = 0;
    if (isset($_SESSION['user_id'])) {
        $notification_sql = "SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND is_read = 0";
        $notification_stmt = mysqli_prepare($conn, $notification_sql);
        mysqli_stmt_bind_param($notification_stmt, "i", $_SESSION['user_id']);
        mysqli_stmt_execute($notification_stmt);
        $notification_result = mysqli_stmt_get_result($notification_stmt);
        $unread_count = mysqli_fetch_assoc($notification_result)['count'];
    }
}

// Determine path prefix (same logic as navigation.php)
$script_path = $_SERVER['SCRIPT_NAME'];
$is_in_subfolder = strpos($script_path, '/auth/') !== false || strpos($script_path, '/includes/') !== false;
$prefix = $is_in_subfolder ? '../' : '';
?>

<!-- Top Notification Bell -->
<div class="relative">
    <button id="notification-bell" class="text-white hover:text-orange-300 transition-colors p-2">
        <i class="fas fa-bell text-xl"></i>
        <span id="notification-count" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center <?php echo $unread_count > 0 ? '' : 'hidden'; ?>">
            <?php echo $unread_count; ?>
        </span>
    </button>
    
    <!-- Notification Dropdown -->
    <div id="notification-dropdown" class="hidden absolute right-0 mt-2 w-96 bg-white rounded-md shadow-lg z-50 overflow-hidden border border-gray-200">
        <div class="px-4 py-3 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
            <h3 class="font-semibold text-gray-800">Notifications</h3>
            <button class="text-gray-500 hover:text-gray-700 text-sm" onclick="markAllAsRead()">
                <i class="fas fa-check-double mr-1"></i> Mark all as read
            </button>
        </div>
        <ul id="notification-list" class="max-h-80 overflow-y-auto">
            <!-- Notifications will be loaded here via JavaScript -->
            <li class="py-3 px-4 text-gray-500 text-center">Loading notifications...</li>
        </ul>
        <div class="px-4 py-3 bg-gray-50 border-t border-gray-200 text-center">
            <a href="<?php echo $prefix; ?>notifications.php" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                View all notifications
            </a>
        </div>
    </div>
</div>

<!-- Include notifications script -->
<script src="<?php echo $prefix; ?>js/notifications.js"></script>
