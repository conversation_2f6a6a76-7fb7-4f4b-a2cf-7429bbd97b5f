<?php
// Get database connection using reliable method
$conn = require_once 'get_db_connection.php';
require_once 'includes/functions.php';

// Check if database is set up first
require_once 'check_database_setup.php';
redirect_to_setup_if_needed();

// Check if the user is logged in
ensure_session_started();
require_login();

// Block access for sales reps
if (isset($_SESSION['role']) && $_SESSION['role'] === 'sales_rep') {
    header("Location: contacts.php?error=Unauthorized");
    exit;
}

// Get contact ID from URL
$contact_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($contact_id <= 0) {
    header("Location: contacts.php?error=Invalid contact ID");
    exit;
}

// Get contact data to display confirmation
$sql = "SELECT * FROM contacts WHERE id = ?";
$stmt = mysqli_prepare($conn, $sql);
mysqli_stmt_bind_param($stmt, "i", $contact_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) == 0) {
    header("Location: contacts.php?error=Contact not found");
    exit;
}

$contact = mysqli_fetch_assoc($result);

// Check if contact has sales (to warn user)
$sales_check_sql = "SELECT COUNT(*) as count FROM sales WHERE contact_id = ?";
$sales_stmt = mysqli_prepare($conn, $sales_check_sql);
mysqli_stmt_bind_param($sales_stmt, "i", $contact_id);
mysqli_stmt_execute($sales_stmt);
$sales_result = mysqli_stmt_get_result($sales_stmt);
$sales_data = mysqli_fetch_assoc($sales_result);
$has_sales = $sales_data['count'] > 0;

// Handle deletion confirmation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_delete'])) {
    try {
        // Start transaction
        mysqli_begin_transaction($conn);
        
        // Delete related records first (if any)
        // Note: Foreign key constraints should handle this automatically with CASCADE
        
        // Delete the contact
        $delete_sql = "DELETE FROM contacts WHERE id = ?";
        $delete_stmt = mysqli_prepare($conn, $delete_sql);
        mysqli_stmt_bind_param($delete_stmt, "i", $contact_id);
        
        if (mysqli_stmt_execute($delete_stmt)) {
            // Commit transaction
            mysqli_commit($conn);
            
            // Redirect with success message
            header("Location: contacts.php?success=Contact deleted successfully");
            exit;
        } else {
            throw new Exception("Error deleting contact: " . mysqli_error($conn));
        }
        
    } catch (Exception $e) {
        // Rollback transaction on error
        mysqli_rollback($conn);
        
        // Redirect with error message
        header("Location: contacts.php?error=" . urlencode($e->getMessage()));
        exit;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="img/minilogo.jpg" type="image/x-icon">
    <title>Delete Contact - CRM System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .text-color { color: #FF6500; }
        .bg-color { background-color: #FF6500; }
    </style>
</head>
<body class="bg-gray-100">

<?php include 'includes/navigation.php'; ?>

<!-- Main Content -->
<div class="ml-0 min-[870px]:ml-60 min-h-screen flex flex-col">
    <header class="bg-[#1E3E62] p-4 flex justify-between z-10 flex-col">
        <h1 class="text-2xl font-bold text-white mx-10 my-2">Delete Contact</h1>
            </header>
    
    <main class="flex-1 p-10">
        <div class="max-w-2xl mx-auto">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center mb-6">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-xl font-semibold text-gray-900">Confirm Deletion</h2>
                        <p class="text-gray-600">This action cannot be undone.</p>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="bg-gray-50 rounded-lg p-4 mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-3">Contact to be deleted:</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Name</label>
                            <p class="text-gray-900"><?php echo htmlspecialchars($contact['first_name'] . ' ' . $contact['last_name']); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Email</label>
                            <p class="text-gray-900"><?php echo htmlspecialchars($contact['email']); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Phone</label>
                            <p class="text-gray-900"><?php echo htmlspecialchars($contact['phone'] ?? 'Not provided'); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Company</label>
                            <p class="text-gray-900"><?php echo htmlspecialchars($contact['company'] ?? 'Not provided'); ?></p>
                        </div>
                                                <div>
                            <label class="block text-sm font-medium text-gray-600">Created</label>
                            <p class="text-gray-900"><?php echo date('M j, Y', strtotime($contact['created_at'])); ?></p>
                        </div>
                    </div>
                </div>

                <!-- Warning about related data -->
                <?php if ($has_sales): ?>
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">Warning: Related Data</h3>
                            <div class="mt-2 text-sm text-yellow-700">
                                <p>This contact has <strong><?php echo $sales_data['count']; ?> sales record(s)</strong> associated with it.</p>
                                <p>Deleting this contact will also remove all related sales data.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Deletion consequences -->
                <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                    <h3 class="text-sm font-medium text-red-800 mb-2">What will be deleted:</h3>
                    <ul class="text-sm text-red-700 space-y-1">
                        <li>• Contact information and details</li>
                        <?php if ($has_sales): ?>
                        <li>• All sales records (<?php echo $sales_data['count']; ?> record(s))</li>
                        <li>• Associated invoices and receipts</li>
                        <?php endif; ?>
                        <li>• Any notes and interaction history</li>
                        <li>• All related data (this action cannot be undone)</li>
                    </ul>
                </div>

                <!-- Confirmation Form -->
                <form method="POST" class="space-y-4">
                    <div class="flex items-center">
                        <input type="checkbox" id="confirm_checkbox" required
                               class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded">
                        <label for="confirm_checkbox" class="ml-2 block text-sm text-gray-900">
                            I understand that this action cannot be undone and will permanently delete this contact and all related data.
                        </label>
                    </div>

                    <div class="flex justify-end space-x-4 pt-4">
                        <a href="view_contact.php?id=<?php echo $contact_id; ?>" 
                           class="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
                            <i class="fas fa-arrow-left mr-2"></i>Cancel
                        </a>
                        <a href="contacts.php" 
                           class="px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
                            <i class="fas fa-list mr-2"></i>Back to Contacts
                        </a>
                        <button type="submit" name="confirm_delete" value="1"
                                class="px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
                                onclick="return confirm('Are you absolutely sure you want to delete this contact? This action cannot be undone.')">
                            <i class="fas fa-trash mr-2"></i>Delete Contact
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </main>
</div>

<script>
// Additional JavaScript confirmation
document.querySelector('form').addEventListener('submit', function(e) {
    const checkbox = document.getElementById('confirm_checkbox');
    if (!checkbox.checked) {
        e.preventDefault();
        alert('Please confirm that you understand the consequences of this action.');
        return false;
    }
    
    const confirmed = confirm('FINAL CONFIRMATION: Are you absolutely sure you want to permanently delete this contact and all related data?');
    if (!confirmed) {
        e.preventDefault();
        return false;
    }
});
</script>

</body>
</html>
