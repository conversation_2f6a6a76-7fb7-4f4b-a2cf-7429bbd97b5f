<?php
/**
 * Enhanced Phone Number Validation API
 * 
 * This script provides comprehensive validation for Indian mobile numbers
 * including checks for dummy numbers, patterns, and sequences.
 */

header('Content-Type: application/json');
require_once '../get_db_connection.php';
require_once '../includes/validation.php';

// Enable error reporting for debugging (remove in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

function sendJsonResponse($data) {
    echo json_encode($data);
    exit;
}

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendJsonResponse([
        'valid' => false,
        'message' => 'Invalid request method'
    ]);
}

// Get phone number from POST data
$phone = isset($_POST['phone']) ? trim($_POST['phone']) : '';

if (empty($phone)) {
    sendJsonResponse([
        'valid' => false,
        'message' => 'Phone number is required'
    ]);
}

// Get detailed validation result
$validation_result = get_phone_validation_result($phone);

// Check for uniqueness in database if phone is valid
if ($validation_result['valid']) {
    try {
        // Check if phone number already exists in contacts table
        $check_sql = "SELECT id, CONCAT(first_name, ' ', last_name) as name FROM contacts WHERE phone = ?";
        $stmt = mysqli_prepare($conn, $check_sql);
        
        if ($stmt) {
            mysqli_stmt_bind_param($stmt, "s", $phone);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            
            if (mysqli_num_rows($result) > 0) {
                $existing_contact = mysqli_fetch_assoc($result);
                $validation_result['valid'] = false;
                $validation_result['message'] = 'Phone number already exists for contact: ' . $existing_contact['name'];
                $validation_result['existing_contact'] = $existing_contact;
            }
            
            mysqli_stmt_close($stmt);
        }
    } catch (Exception $e) {
        // Log error but don't expose it to client
        error_log("Database error in phone validation: " . $e->getMessage());
        
        // Continue with validation even if database check fails
        $validation_result['database_check'] = false;
    }
}

// Add additional metadata for client-side use
$clean_phone = preg_replace('/[^0-9]/', '', $phone);
if (strlen($clean_phone) === 10) {
    $validation_result['formatted_number'] = '+91 ' . substr($clean_phone, 0, 5) . ' ' . substr($clean_phone, 5);
    $validation_result['carrier'] = getCarrierInfo($clean_phone);
}

sendJsonResponse($validation_result);

/**
 * Get carrier information based on phone number prefix
 * 
 * @param string $phone 10-digit phone number
 * @return string Carrier name
 */
function getCarrierInfo($phone) {
    $prefix = substr($phone, 0, 2);
    
    switch ($prefix) {
        case '60':
        case '62':
        case '63':
        case '64':
        case '65':
        case '90':
        case '91':
        case '92':
        case '93':
        case '98':
        case '99':
            return 'Airtel';
            
        case '70':
        case '74':
        case '79':
        case '87':
        case '88':
        case '89':
            return 'Reliance Jio';
            
        case '71':
        case '72':
        case '75':
        case '76':
        case '77':
        case '78':
        case '84':
        case '85':
        case '86':
        case '96':
        case '97':
            return 'Vodafone Idea';
            
        case '73':
            // 73 can be either Jio or Vodafone Idea
            $prefix3 = substr($phone, 0, 3);
            if (in_array($prefix3, ['730', '731', '732', '733'])) {
                return 'Reliance Jio';
            } else {
                return 'Vodafone Idea';
            }
            
        case '94':
        case '95':
            return 'BSNL';
            
        default:
            return 'Indian Telecom Provider';
    }
}
?>
