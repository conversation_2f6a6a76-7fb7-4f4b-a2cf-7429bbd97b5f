<?php
$conn = require_once '../config/database.php';
require_once '../includes/functions.php';

// Checking that the user is logged in or other person
ensure_session_started();
require_login();

// Initialize response
$response = [
    'unique' => true,
    'message' => ''
];

// Get parameters
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'check_unique') {
    $field = isset($_POST['field']) ? sanitize_input($_POST['field']) : '';
    $value = isset($_POST['value']) ? sanitize_input($_POST['value']) : '';
    $exclude_id = isset($_POST['exclude_id']) ? intval($_POST['exclude_id']) : 0;
} else {
    $field = isset($_GET['field']) ? sanitize_input($_GET['field']) : '';
    $value = isset($_GET['value']) ? sanitize_input($_GET['value']) : '';
    $exclude_id = isset($_GET['exclude_id']) ? intval($_GET['exclude_id']) : 0;
}

// Validate input
if (empty($field) || empty($value)) {
    $response['unique'] = false;
    $response['message'] = 'Missing required parameters';
    echo json_encode($response);
    exit;
}

$allowed_fields = ['phone', 'email'];
if (!in_array($field, $allowed_fields)) {
    $response['unique'] = false;
    $response['message'] = 'Invalid field';
    echo json_encode($response);
    exit;
}

// Check in contacts table for phone
if ($field == 'phone') {
    $sql = "SELECT id FROM contacts WHERE $field = ?";
    $params = [$value];
    $types = "s";

    if ($exclude_id > 0) {
        $sql .= " AND id != ?";
        $params[] = $exclude_id;
        $types .= "i";
    }

    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, $types, ...$params);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    if (mysqli_num_rows($result) > 0) {
        $response['unique'] = false;
        $response['message'] = "This phone number is already registered with another customer.";
        echo json_encode($response);
        exit;
    }
}

if ($field == 'email') {
    $sql = "SELECT id FROM leads WHERE $field = ?";
    $params = [$value];
    $types = "s";
    
    if ($exclude_id > 0) {
        $sql .= " AND id != ?";
        $params[] = $exclude_id;
        $types .= "i";
    }
    
    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, $types, ...$params);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if (mysqli_num_rows($result) > 0) {
        $response['unique'] = false;
        $response['message'] = "This " . str_replace('_', ' ', $field) . " is already registered with another lead.";
        echo json_encode($response);
        exit;
    }
}
// For phone, we need to check both phone and country_code
else if ($field == 'phone' && (has_role("admin") || has_role("manager"))) {
    // We'll skip this check for now as we're handling it in the edit_lead.php file
    // This prevents duplicate checks that might cause issues
    $response['unique'] = true;
}

// Also check in employees table for email
if ($field == 'email') {
    $sql = "SELECT id FROM employees WHERE email = ?";
    $params = [$value];
    $types = "s";

    if ($exclude_id > 0) {
        $sql .= " AND id != ?";
        $params[] = $exclude_id;
        $types .= "i";
    }

    $stmt = mysqli_prepare($conn, $sql);
    mysqli_stmt_bind_param($stmt, $types, ...$params);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    if (mysqli_num_rows($result) > 0) {
        $response['unique'] = false;
        $response['message'] = "This email is already registered with an employee.";
        echo json_encode($response);
        exit;
    }
}



// If we get here, the field is unique
echo json_encode($response);
