<?php
// Get database connection using reliable method
$conn = require_once 'get_db_connection.php';

// Check if database is set up first
require_once 'check_database_setup.php';
redirect_to_setup_if_needed();

// Include functions file
require_once 'includes/functions.php';

// Check if the user is logged in
ensure_session_started();

if (is_logged_in()) {
    // Redirect based on role
    switch ($_SESSION["role"]) {
        case 'admin':
        case 'manager':
            header("Location: admin.php");
            break;
        case 'sales_rep':
            header("Location: sales.php");
            break;
        default:
            header("Location: admin.php");
    }
    exit;
} else {
    // Redirect to login page
    header("Location: auth/login.php");
    exit;
}
?>
